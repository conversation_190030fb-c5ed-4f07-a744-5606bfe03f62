import { readFile, readdir, stat } from 'fs/promises';
import { join, extname } from 'path';
import { v4 as uuidv4 } from 'uuid';
import type { Tool, ToolResult } from '../types';

interface SearchResult {
  file: string;
  line: number;
  content: string;
  match: string;
}

interface SearchOptions {
  pattern: string;
  path: string;
  recursive?: boolean;
  ignoreCase?: boolean;
  wholeWord?: boolean;
  filePattern?: string;
  excludePattern?: string;
  maxResults?: number;
  contextLines?: number;
}

const grepTool: Tool = {
  name: 'grep_search',
  description: 'Search for patterns in files using grep-like functionality',
  schema: {
    name: 'grep',
    description: 'Search for patterns in files using regular expressions',
    parameters: {
      type: 'object',
      properties: {
        pattern: {
          type: 'string',
          description: 'Regular expression pattern to search for'
        },
        path: {
          type: 'string',
          description: 'File or directory path to search in'
        },
        recursive: {
          type: 'boolean',
          description: 'Search recursively in subdirectories (default: false)'
        },
        ignoreCase: {
          type: 'boolean',
          description: 'Case-insensitive search (default: false)'
        },
        wholeWord: {
          type: 'boolean',
          description: 'Match whole words only (default: false)'
        },
        filePattern: {
          type: 'string',
          description: 'File name pattern to include (glob pattern)'
        },
        excludePattern: {
          type: 'string',
          description: 'File name pattern to exclude (glob pattern)'
        },
        maxResults: {
          type: 'number',
          description: 'Maximum number of results to return (default: 100)'
        },
        contextLines: {
          type: 'number',
          description: 'Number of context lines to show around matches (default: 0)'
        }
      },
      required: ['pattern', 'path']
    }
  },

  async execute(args: Record<string, any>): Promise<ToolResult> {
    const options: SearchOptions = {
      pattern: args.pattern,
      path: args.path,
      recursive: args.recursive || false,
      ignoreCase: args.ignoreCase || false,
      wholeWord: args.wholeWord || false,
      filePattern: args.filePattern,
      excludePattern: args.excludePattern,
      maxResults: args.maxResults || 100,
      contextLines: args.contextLines || 0
    };

    const id = uuidv4();
    const startTime = Date.now();

    try {
      const results = await searchPattern(options);
      
      const output = formatSearchResults(results, options);
      
      return {
        id,
        success: true,
        output,
        executionTime: Date.now() - startTime
      };
    } catch (error: any) {
      return {
        id,
        success: false,
        output: '',
        error: `Search failed: ${error.message}`,
        executionTime: Date.now() - startTime
      };
    }
  }
};

async function searchPattern(options: SearchOptions): Promise<SearchResult[]> {
  const results: SearchResult[] = [];
  const { pattern, path, recursive, ignoreCase, wholeWord, maxResults } = options;

  // Create regex pattern
  let regexPattern = pattern;
  if (wholeWord) {
    regexPattern = `\\b${pattern}\\b`;
  }
  
  const flags = ignoreCase ? 'gi' : 'g';
  const regex = new RegExp(regexPattern, flags);

  // Get files to search
  const files = await getFilesToSearch(path, recursive || false, options.filePattern, options.excludePattern);

  for (const file of files) {
    if (results.length >= maxResults!) {
      break;
    }

    try {
      const fileResults = await searchInFile(file, regex, options.contextLines || 0);
      results.push(...fileResults);
      
      if (results.length >= maxResults!) {
        results.splice(maxResults!);
        break;
      }
    } catch (error) {
      // Skip files that can't be read
      console.warn(`Cannot read file: ${file}`);
    }
  }

  return results;
}

async function getFilesToSearch(
  path: string,
  recursive: boolean,
  filePattern?: string,
  excludePattern?: string
): Promise<string[]> {
  const files: string[] = [];
  
  try {
    const stats = await stat(path);
    
    if (stats.isFile()) {
      if (shouldIncludeFile(path, filePattern, excludePattern)) {
        files.push(path);
      }
    } else if (stats.isDirectory()) {
      const items = await readdir(path);
      
      for (const item of items) {
        const itemPath = join(path, item);
        const itemStats = await stat(itemPath);
        
        if (itemStats.isFile()) {
          if (shouldIncludeFile(itemPath, filePattern, excludePattern)) {
            files.push(itemPath);
          }
        } else if (itemStats.isDirectory() && recursive) {
          const subFiles = await getFilesToSearch(itemPath, recursive, filePattern, excludePattern);
          files.push(...subFiles);
        }
      }
    }
  } catch (error) {
    throw new Error(`Cannot access path: ${path}`);
  }
  
  return files;
}

function shouldIncludeFile(filePath: string, filePattern?: string, excludePattern?: string): boolean {
  const fileName = filePath.split(/[/\\]/).pop() || '';
  
  // Check exclude pattern first
  if (excludePattern && matchesPattern(fileName, excludePattern)) {
    return false;
  }
  
  // Check include pattern
  if (filePattern && !matchesPattern(fileName, filePattern)) {
    return false;
  }
  
  // Skip binary files by extension
  const binaryExtensions = ['.exe', '.dll', '.so', '.dylib', '.bin', '.obj', '.o', '.a', '.lib'];
  const ext = extname(fileName).toLowerCase();
  if (binaryExtensions.includes(ext)) {
    return false;
  }
  
  return true;
}

function matchesPattern(fileName: string, pattern: string): boolean {
  // Simple glob pattern matching
  const regexPattern = pattern
    .replace(/\./g, '\\.')
    .replace(/\*/g, '.*')
    .replace(/\?/g, '.');
  
  const regex = new RegExp(`^${regexPattern}$`, 'i');
  return regex.test(fileName);
}

async function searchInFile(filePath: string, regex: RegExp, contextLines: number): Promise<SearchResult[]> {
  const results: SearchResult[] = [];
  
  try {
    const content = await readFile(filePath, 'utf8');
    const lines = content.split('\n');
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const matches = line.match(regex);
      
      if (matches) {
        for (const match of matches) {
          const result: SearchResult = {
            file: filePath,
            line: i + 1,
            content: line,
            match
          };
          
          // Add context lines if requested
          if (contextLines > 0) {
            const contextStart = Math.max(0, i - contextLines);
            const contextEnd = Math.min(lines.length - 1, i + contextLines);
            const contextContent = lines.slice(contextStart, contextEnd + 1);
            
            result.content = contextContent.map((contextLine, idx) => {
              const lineNum = contextStart + idx + 1;
              const marker = lineNum === i + 1 ? '>' : ' ';
              return `${marker} ${lineNum}: ${contextLine}`;
            }).join('\n');
          }
          
          results.push(result);
        }
      }
    }
  } catch (error) {
    // Skip files that can't be read as text
    if (error instanceof Error && error.message.includes('EISDIR')) {
      throw error;
    }
  }
  
  return results;
}

function formatSearchResults(results: SearchResult[], options: SearchOptions): string {
  if (results.length === 0) {
    return `No matches found for pattern: ${options.pattern}`;
  }
  
  const output: string[] = [];
  output.push(`Found ${results.length} matches for pattern: ${options.pattern}`);
  output.push('');
  
  let currentFile = '';
  
  for (const result of results) {
    if (result.file !== currentFile) {
      currentFile = result.file;
      output.push(`File: ${result.file}`);
      output.push('─'.repeat(50));
    }
    
    if (options.contextLines && options.contextLines > 0) {
      output.push(result.content);
    } else {
      output.push(`Line ${result.line}: ${result.content}`);
    }
    
    output.push('');
  }
  
  return output.join('\n');
}

// Export tool individually and as default
export { grepTool };

// Export as default for tool registry
export default [grepTool];
