{"version": 3, "file": "getConstrainedTypeAtLocation.js", "sourceRoot": "", "sources": ["../src/getConstrainedTypeAtLocation.ts"], "names": [], "mappings": ";;;AAMA;;GAEG;AACH,SAAgB,4BAA4B,CAC1C,QAA2C,EAC3C,IAAmB;IAEnB,MAAM,QAAQ,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAClD,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO;SACjC,cAAc,EAAE;SAChB,uBAAuB,CAAC,QAAQ,CAAC,CAAC;IAErC,OAAO,WAAW,IAAI,QAAQ,CAAC;AACjC,CAAC;AAVD,oEAUC"}