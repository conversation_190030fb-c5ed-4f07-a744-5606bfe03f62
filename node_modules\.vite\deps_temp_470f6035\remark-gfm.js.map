{"version": 3, "sources": ["../../ccount/index.js", "../../mdast-util-find-and-replace/node_modules/escape-string-regexp/index.js", "../../mdast-util-find-and-replace/lib/index.js", "../../mdast-util-gfm-autolink-literal/lib/index.js", "../../mdast-util-gfm-footnote/lib/index.js", "../../mdast-util-gfm-strikethrough/lib/index.js", "../../markdown-table/index.js", "../../zwitch/index.js", "../../mdast-util-to-markdown/lib/configure.js", "../../mdast-util-to-markdown/lib/handle/blockquote.js", "../../mdast-util-to-markdown/lib/util/pattern-in-scope.js", "../../mdast-util-to-markdown/lib/handle/break.js", "../../longest-streak/index.js", "../../mdast-util-to-markdown/lib/util/format-code-as-indented.js", "../../mdast-util-to-markdown/lib/util/check-fence.js", "../../mdast-util-to-markdown/lib/handle/code.js", "../../mdast-util-to-markdown/lib/util/check-quote.js", "../../mdast-util-to-markdown/lib/handle/definition.js", "../../mdast-util-to-markdown/lib/util/check-emphasis.js", "../../mdast-util-to-markdown/lib/util/encode-character-reference.js", "../../mdast-util-to-markdown/lib/util/encode-info.js", "../../mdast-util-to-markdown/lib/handle/emphasis.js", "../../mdast-util-to-markdown/lib/util/format-heading-as-setext.js", "../../mdast-util-to-markdown/lib/handle/heading.js", "../../mdast-util-to-markdown/lib/handle/html.js", "../../mdast-util-to-markdown/lib/handle/image.js", "../../mdast-util-to-markdown/lib/handle/image-reference.js", "../../mdast-util-to-markdown/lib/handle/inline-code.js", "../../mdast-util-to-markdown/lib/util/format-link-as-autolink.js", "../../mdast-util-to-markdown/lib/handle/link.js", "../../mdast-util-to-markdown/lib/handle/link-reference.js", "../../mdast-util-to-markdown/lib/util/check-bullet.js", "../../mdast-util-to-markdown/lib/util/check-bullet-other.js", "../../mdast-util-to-markdown/lib/util/check-bullet-ordered.js", "../../mdast-util-to-markdown/lib/util/check-rule.js", "../../mdast-util-to-markdown/lib/handle/list.js", "../../mdast-util-to-markdown/lib/util/check-list-item-indent.js", "../../mdast-util-to-markdown/lib/handle/list-item.js", "../../mdast-util-to-markdown/lib/handle/paragraph.js", "../../mdast-util-phrasing/lib/index.js", "../../mdast-util-to-markdown/lib/handle/root.js", "../../mdast-util-to-markdown/lib/util/check-strong.js", "../../mdast-util-to-markdown/lib/handle/strong.js", "../../mdast-util-to-markdown/lib/handle/text.js", "../../mdast-util-to-markdown/lib/util/check-rule-repetition.js", "../../mdast-util-to-markdown/lib/handle/thematic-break.js", "../../mdast-util-to-markdown/lib/handle/index.js", "../../mdast-util-gfm-table/lib/index.js", "../../mdast-util-gfm-task-list-item/lib/index.js", "../../mdast-util-gfm/lib/index.js", "../../micromark-extension-gfm-autolink-literal/dev/lib/syntax.js", "../../micromark-extension-gfm-footnote/dev/lib/syntax.js", "../../micromark-extension-gfm-footnote/dev/lib/html.js", "../../micromark-extension-gfm-strikethrough/dev/lib/syntax.js", "../../micromark-extension-gfm-table/dev/lib/edit-map.js", "../../micromark-extension-gfm-table/dev/lib/infer.js", "../../micromark-extension-gfm-table/dev/lib/syntax.js", "../../micromark-extension-gfm-tagfilter/lib/index.js", "../../micromark-extension-gfm-task-list-item/dev/lib/syntax.js", "../../micromark-extension-gfm/index.js", "../../remark-gfm/lib/index.js"], "sourcesContent": ["/**\n * Count how often a character (or substring) is used in a string.\n *\n * @param {string} value\n *   Value to search in.\n * @param {string} character\n *   Character (or substring) to look for.\n * @return {number}\n *   Number of times `character` occurred in `value`.\n */\nexport function ccount(value, character) {\n  const source = String(value)\n\n  if (typeof character !== 'string') {\n    throw new TypeError('Expected character')\n  }\n\n  let count = 0\n  let index = source.indexOf(character)\n\n  while (index !== -1) {\n    count++\n    index = source.indexOf(character, index + character.length)\n  }\n\n  return count\n}\n", "export default function escapeStringRegexp(string) {\n\tif (typeof string !== 'string') {\n\t\tthrow new TypeError('Expected a string');\n\t}\n\n\t// Escape characters with special meaning either inside or outside character sets.\n\t// Use a simple backslash escape when it’s always valid, and a `\\xnn` escape when the simpler form would be disallowed by Unicode patterns’ stricter grammar.\n\treturn string\n\t\t.replace(/[|\\\\{}()[\\]^$+*?.]/g, '\\\\$&')\n\t\t.replace(/-/g, '\\\\x2d');\n}\n", "/**\n * @import {<PERSON><PERSON>, <PERSON>, PhrasingContent, <PERSON>, Text} from 'mdast'\n * @import {BuildVisitor, Test, VisitorResult} from 'unist-util-visit-parents'\n */\n\n/**\n * @typedef RegExpMatchObject\n *   Info on the match.\n * @property {number} index\n *   The index of the search at which the result was found.\n * @property {string} input\n *   A copy of the search string in the text node.\n * @property {[...Array<Parents>, Text]} stack\n *   All ancestors of the text node, where the last node is the text itself.\n *\n * @typedef {RegExp | string} Find\n *   Pattern to find.\n *\n *   Strings are escaped and then turned into global expressions.\n *\n * @typedef {Array<FindAndReplaceTuple>} FindAndReplaceList\n *   Several find and replaces, in array form.\n *\n * @typedef {[Find, Replace?]} FindAndReplaceTuple\n *   Find and replace in tuple form.\n *\n * @typedef {ReplaceFunction | string | null | undefined} Replace\n *   Thing to replace with.\n *\n * @callback ReplaceFunction\n *   Callback called when a search matches.\n * @param {...any} parameters\n *   The parameters are the result of corresponding search expression:\n *\n *   * `value` (`string`) — whole match\n *   * `...capture` (`Array<string>`) — matches from regex capture groups\n *   * `match` (`RegExpMatchObject`) — info on the match\n * @returns {Array<PhrasingContent> | PhrasingContent | string | false | null | undefined}\n *   Thing to replace with.\n *\n *   * when `null`, `undefined`, `''`, remove the match\n *   * …or when `false`, do not replace at all\n *   * …or when `string`, replace with a text node of that value\n *   * …or when `Node` or `Array<Node>`, replace with those nodes\n *\n * @typedef {[RegExp, ReplaceFunction]} Pair\n *   Normalized find and replace.\n *\n * @typedef {Array<Pair>} Pairs\n *   All find and replaced.\n *\n * @typedef Options\n *   Configuration.\n * @property {Test | null | undefined} [ignore]\n *   Test for which nodes to ignore (optional).\n */\n\nimport escape from 'escape-string-regexp'\nimport {visitParents} from 'unist-util-visit-parents'\nimport {convert} from 'unist-util-is'\n\n/**\n * Find patterns in a tree and replace them.\n *\n * The algorithm searches the tree in *preorder* for complete values in `Text`\n * nodes.\n * Partial matches are not supported.\n *\n * @param {Nodes} tree\n *   Tree to change.\n * @param {FindAndReplaceList | FindAndReplaceTuple} list\n *   Patterns to find.\n * @param {Options | null | undefined} [options]\n *   Configuration (when `find` is not `Find`).\n * @returns {undefined}\n *   Nothing.\n */\nexport function findAndReplace(tree, list, options) {\n  const settings = options || {}\n  const ignored = convert(settings.ignore || [])\n  const pairs = toPairs(list)\n  let pairIndex = -1\n\n  while (++pairIndex < pairs.length) {\n    visitParents(tree, 'text', visitor)\n  }\n\n  /** @type {BuildVisitor<Root, 'text'>} */\n  function visitor(node, parents) {\n    let index = -1\n    /** @type {Parents | undefined} */\n    let grandparent\n\n    while (++index < parents.length) {\n      const parent = parents[index]\n      /** @type {Array<Nodes> | undefined} */\n      const siblings = grandparent ? grandparent.children : undefined\n\n      if (\n        ignored(\n          parent,\n          siblings ? siblings.indexOf(parent) : undefined,\n          grandparent\n        )\n      ) {\n        return\n      }\n\n      grandparent = parent\n    }\n\n    if (grandparent) {\n      return handler(node, parents)\n    }\n  }\n\n  /**\n   * Handle a text node which is not in an ignored parent.\n   *\n   * @param {Text} node\n   *   Text node.\n   * @param {Array<Parents>} parents\n   *   Parents.\n   * @returns {VisitorResult}\n   *   Result.\n   */\n  function handler(node, parents) {\n    const parent = parents[parents.length - 1]\n    const find = pairs[pairIndex][0]\n    const replace = pairs[pairIndex][1]\n    let start = 0\n    /** @type {Array<Nodes>} */\n    const siblings = parent.children\n    const index = siblings.indexOf(node)\n    let change = false\n    /** @type {Array<PhrasingContent>} */\n    let nodes = []\n\n    find.lastIndex = 0\n\n    let match = find.exec(node.value)\n\n    while (match) {\n      const position = match.index\n      /** @type {RegExpMatchObject} */\n      const matchObject = {\n        index: match.index,\n        input: match.input,\n        stack: [...parents, node]\n      }\n      let value = replace(...match, matchObject)\n\n      if (typeof value === 'string') {\n        value = value.length > 0 ? {type: 'text', value} : undefined\n      }\n\n      // It wasn’t a match after all.\n      if (value === false) {\n        // False acts as if there was no match.\n        // So we need to reset `lastIndex`, which currently being at the end of\n        // the current match, to the beginning.\n        find.lastIndex = position + 1\n      } else {\n        if (start !== position) {\n          nodes.push({\n            type: 'text',\n            value: node.value.slice(start, position)\n          })\n        }\n\n        if (Array.isArray(value)) {\n          nodes.push(...value)\n        } else if (value) {\n          nodes.push(value)\n        }\n\n        start = position + match[0].length\n        change = true\n      }\n\n      if (!find.global) {\n        break\n      }\n\n      match = find.exec(node.value)\n    }\n\n    if (change) {\n      if (start < node.value.length) {\n        nodes.push({type: 'text', value: node.value.slice(start)})\n      }\n\n      parent.children.splice(index, 1, ...nodes)\n    } else {\n      nodes = [node]\n    }\n\n    return index + nodes.length\n  }\n}\n\n/**\n * Turn a tuple or a list of tuples into pairs.\n *\n * @param {FindAndReplaceList | FindAndReplaceTuple} tupleOrList\n *   Schema.\n * @returns {Pairs}\n *   Clean pairs.\n */\nfunction toPairs(tupleOrList) {\n  /** @type {Pairs} */\n  const result = []\n\n  if (!Array.isArray(tupleOrList)) {\n    throw new TypeError('Expected find and replace tuple or list of tuples')\n  }\n\n  /** @type {FindAndReplaceList} */\n  // @ts-expect-error: correct.\n  const list =\n    !tupleOrList[0] || Array.isArray(tupleOrList[0])\n      ? tupleOrList\n      : [tupleOrList]\n\n  let index = -1\n\n  while (++index < list.length) {\n    const tuple = list[index]\n    result.push([toExpression(tuple[0]), toFunction(tuple[1])])\n  }\n\n  return result\n}\n\n/**\n * Turn a find into an expression.\n *\n * @param {Find} find\n *   Find.\n * @returns {RegExp}\n *   Expression.\n */\nfunction toExpression(find) {\n  return typeof find === 'string' ? new RegExp(escape(find), 'g') : find\n}\n\n/**\n * Turn a replace into a function.\n *\n * @param {Replace} replace\n *   Replace.\n * @returns {ReplaceFunction}\n *   Function.\n */\nfunction toFunction(replace) {\n  return typeof replace === 'function'\n    ? replace\n    : function () {\n        return replace\n      }\n}\n", "/**\n * @import {RegExpMatchObject, ReplaceFunction} from 'mdast-util-find-and-replace'\n * @import {CompileContext, Extension as FromMarkdownExtension, Handle as FromMarkdownHandle, Transform as FromMarkdownTransform} from 'mdast-util-from-markdown'\n * @import {ConstructName, Options as ToMarkdownExtension} from 'mdast-util-to-markdown'\n * @import {Link, PhrasingContent} from 'mdast'\n */\n\nimport {ccount} from 'ccount'\nimport {ok as assert} from 'devlop'\nimport {unicodePunctuation, unicodeWhitespace} from 'micromark-util-character'\nimport {findAndReplace} from 'mdast-util-find-and-replace'\n\n/** @type {ConstructName} */\nconst inConstruct = 'phrasing'\n/** @type {Array<ConstructName>} */\nconst notInConstruct = ['autolink', 'link', 'image', 'label']\n\n/**\n * Create an extension for `mdast-util-from-markdown` to enable GFM autolink\n * literals in markdown.\n *\n * @returns {FromMarkdownExtension}\n *   Extension for `mdast-util-to-markdown` to enable GFM autolink literals.\n */\nexport function gfmAutolinkLiteralFromMarkdown() {\n  return {\n    transforms: [transformGfmAutolinkLiterals],\n    enter: {\n      literalAutolink: enterLiteralAutolink,\n      literalAutolinkEmail: enterLiteralAutolinkValue,\n      literalAutolinkHttp: enterLiteralAutolinkValue,\n      literalAutolinkWww: enterLiteralAutolinkValue\n    },\n    exit: {\n      literalAutolink: exitLiteralAutolink,\n      literalAutolinkEmail: exitLiteralAutolinkEmail,\n      literalAutolinkHttp: exitLiteralAutolinkHttp,\n      literalAutolinkWww: exitLiteralAutolinkWww\n    }\n  }\n}\n\n/**\n * Create an extension for `mdast-util-to-markdown` to enable GFM autolink\n * literals in markdown.\n *\n * @returns {ToMarkdownExtension}\n *   Extension for `mdast-util-to-markdown` to enable GFM autolink literals.\n */\nexport function gfmAutolinkLiteralToMarkdown() {\n  return {\n    unsafe: [\n      {\n        character: '@',\n        before: '[+\\\\-.\\\\w]',\n        after: '[\\\\-.\\\\w]',\n        inConstruct,\n        notInConstruct\n      },\n      {\n        character: '.',\n        before: '[Ww]',\n        after: '[\\\\-.\\\\w]',\n        inConstruct,\n        notInConstruct\n      },\n      {\n        character: ':',\n        before: '[ps]',\n        after: '\\\\/',\n        inConstruct,\n        notInConstruct\n      }\n    ]\n  }\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterLiteralAutolink(token) {\n  this.enter({type: 'link', title: null, url: '', children: []}, token)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterLiteralAutolinkValue(token) {\n  this.config.enter.autolinkProtocol.call(this, token)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitLiteralAutolinkHttp(token) {\n  this.config.exit.autolinkProtocol.call(this, token)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitLiteralAutolinkWww(token) {\n  this.config.exit.data.call(this, token)\n  const node = this.stack[this.stack.length - 1]\n  assert(node.type === 'link')\n  node.url = 'http://' + this.sliceSerialize(token)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitLiteralAutolinkEmail(token) {\n  this.config.exit.autolinkEmail.call(this, token)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitLiteralAutolink(token) {\n  this.exit(token)\n}\n\n/** @type {FromMarkdownTransform} */\nfunction transformGfmAutolinkLiterals(tree) {\n  findAndReplace(\n    tree,\n    [\n      [/(https?:\\/\\/|www(?=\\.))([-.\\w]+)([^ \\t\\r\\n]*)/gi, findUrl],\n      [/(?<=^|\\s|\\p{P}|\\p{S})([-.\\w+]+)@([-\\w]+(?:\\.[-\\w]+)+)/gu, findEmail]\n    ],\n    {ignore: ['link', 'linkReference']}\n  )\n}\n\n/**\n * @type {ReplaceFunction}\n * @param {string} _\n * @param {string} protocol\n * @param {string} domain\n * @param {string} path\n * @param {RegExpMatchObject} match\n * @returns {Array<PhrasingContent> | Link | false}\n */\n// eslint-disable-next-line max-params\nfunction findUrl(_, protocol, domain, path, match) {\n  let prefix = ''\n\n  // Not an expected previous character.\n  if (!previous(match)) {\n    return false\n  }\n\n  // Treat `www` as part of the domain.\n  if (/^w/i.test(protocol)) {\n    domain = protocol + domain\n    protocol = ''\n    prefix = 'http://'\n  }\n\n  if (!isCorrectDomain(domain)) {\n    return false\n  }\n\n  const parts = splitUrl(domain + path)\n\n  if (!parts[0]) return false\n\n  /** @type {Link} */\n  const result = {\n    type: 'link',\n    title: null,\n    url: prefix + protocol + parts[0],\n    children: [{type: 'text', value: protocol + parts[0]}]\n  }\n\n  if (parts[1]) {\n    return [result, {type: 'text', value: parts[1]}]\n  }\n\n  return result\n}\n\n/**\n * @type {ReplaceFunction}\n * @param {string} _\n * @param {string} atext\n * @param {string} label\n * @param {RegExpMatchObject} match\n * @returns {Link | false}\n */\nfunction findEmail(_, atext, label, match) {\n  if (\n    // Not an expected previous character.\n    !previous(match, true) ||\n    // Label ends in not allowed character.\n    /[-\\d_]$/.test(label)\n  ) {\n    return false\n  }\n\n  return {\n    type: 'link',\n    title: null,\n    url: 'mailto:' + atext + '@' + label,\n    children: [{type: 'text', value: atext + '@' + label}]\n  }\n}\n\n/**\n * @param {string} domain\n * @returns {boolean}\n */\nfunction isCorrectDomain(domain) {\n  const parts = domain.split('.')\n\n  if (\n    parts.length < 2 ||\n    (parts[parts.length - 1] &&\n      (/_/.test(parts[parts.length - 1]) ||\n        !/[a-zA-Z\\d]/.test(parts[parts.length - 1]))) ||\n    (parts[parts.length - 2] &&\n      (/_/.test(parts[parts.length - 2]) ||\n        !/[a-zA-Z\\d]/.test(parts[parts.length - 2])))\n  ) {\n    return false\n  }\n\n  return true\n}\n\n/**\n * @param {string} url\n * @returns {[string, string | undefined]}\n */\nfunction splitUrl(url) {\n  const trailExec = /[!\"&'),.:;<>?\\]}]+$/.exec(url)\n\n  if (!trailExec) {\n    return [url, undefined]\n  }\n\n  url = url.slice(0, trailExec.index)\n\n  let trail = trailExec[0]\n  let closingParenIndex = trail.indexOf(')')\n  const openingParens = ccount(url, '(')\n  let closingParens = ccount(url, ')')\n\n  while (closingParenIndex !== -1 && openingParens > closingParens) {\n    url += trail.slice(0, closingParenIndex + 1)\n    trail = trail.slice(closingParenIndex + 1)\n    closingParenIndex = trail.indexOf(')')\n    closingParens++\n  }\n\n  return [url, trail]\n}\n\n/**\n * @param {RegExpMatchObject} match\n * @param {boolean | null | undefined} [email=false]\n * @returns {boolean}\n */\nfunction previous(match, email) {\n  const code = match.input.charCodeAt(match.index - 1)\n\n  return (\n    (match.index === 0 ||\n      unicodeWhitespace(code) ||\n      unicodePunctuation(code)) &&\n    // If it’s an email, the previous character should not be a slash.\n    (!email || code !== 47)\n  )\n}\n", "/**\n * @import {\n *   CompileContext,\n *   Extension as FromMarkdownExtension,\n *   Handle as FromMarkdownHandle\n * } from 'mdast-util-from-markdown'\n * @import {ToMarkdownOptions} from 'mdast-util-gfm-footnote'\n * @import {\n *   Handle as ToMarkdownHandle,\n *   Map,\n *   Options as ToMarkdownExtension\n * } from 'mdast-util-to-markdown'\n * @import {FootnoteDefinition, FootnoteReference} from 'mdast'\n */\n\nimport {ok as assert} from 'devlop'\nimport {normalizeIdentifier} from 'micromark-util-normalize-identifier'\n\nfootnoteReference.peek = footnoteReferencePeek\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterFootnoteCallString() {\n  this.buffer()\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterFootnoteCall(token) {\n  this.enter({type: 'footnoteReference', identifier: '', label: ''}, token)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterFootnoteDefinitionLabelString() {\n  this.buffer()\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterFootnoteDefinition(token) {\n  this.enter(\n    {type: 'footnoteDefinition', identifier: '', label: '', children: []},\n    token\n  )\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitFootnoteCallString(token) {\n  const label = this.resume()\n  const node = this.stack[this.stack.length - 1]\n  assert(node.type === 'footnoteReference')\n  node.identifier = normalizeIdentifier(\n    this.sliceSerialize(token)\n  ).toLowerCase()\n  node.label = label\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitFootnoteCall(token) {\n  this.exit(token)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitFootnoteDefinitionLabelString(token) {\n  const label = this.resume()\n  const node = this.stack[this.stack.length - 1]\n  assert(node.type === 'footnoteDefinition')\n  node.identifier = normalizeIdentifier(\n    this.sliceSerialize(token)\n  ).toLowerCase()\n  node.label = label\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitFootnoteDefinition(token) {\n  this.exit(token)\n}\n\n/** @type {ToMarkdownHandle} */\nfunction footnoteReferencePeek() {\n  return '['\n}\n\n/**\n * @type {ToMarkdownHandle}\n * @param {FootnoteReference} node\n */\nfunction footnoteReference(node, _, state, info) {\n  const tracker = state.createTracker(info)\n  let value = tracker.move('[^')\n  const exit = state.enter('footnoteReference')\n  const subexit = state.enter('reference')\n  value += tracker.move(\n    state.safe(state.associationId(node), {after: ']', before: value})\n  )\n  subexit()\n  exit()\n  value += tracker.move(']')\n  return value\n}\n\n/**\n * Create an extension for `mdast-util-from-markdown` to enable GFM footnotes\n * in markdown.\n *\n * @returns {FromMarkdownExtension}\n *   Extension for `mdast-util-from-markdown`.\n */\nexport function gfmFootnoteFromMarkdown() {\n  return {\n    enter: {\n      gfmFootnoteCallString: enterFootnoteCallString,\n      gfmFootnoteCall: enterFootnoteCall,\n      gfmFootnoteDefinitionLabelString: enterFootnoteDefinitionLabelString,\n      gfmFootnoteDefinition: enterFootnoteDefinition\n    },\n    exit: {\n      gfmFootnoteCallString: exitFootnoteCallString,\n      gfmFootnoteCall: exitFootnoteCall,\n      gfmFootnoteDefinitionLabelString: exitFootnoteDefinitionLabelString,\n      gfmFootnoteDefinition: exitFootnoteDefinition\n    }\n  }\n}\n\n/**\n * Create an extension for `mdast-util-to-markdown` to enable GFM footnotes\n * in markdown.\n *\n * @param {ToMarkdownOptions | null | undefined} [options]\n *   Configuration (optional).\n * @returns {ToMarkdownExtension}\n *   Extension for `mdast-util-to-markdown`.\n */\nexport function gfmFootnoteToMarkdown(options) {\n  // To do: next major: change default.\n  let firstLineBlank = false\n\n  if (options && options.firstLineBlank) {\n    firstLineBlank = true\n  }\n\n  return {\n    handlers: {footnoteDefinition, footnoteReference},\n    // This is on by default already.\n    unsafe: [{character: '[', inConstruct: ['label', 'phrasing', 'reference']}]\n  }\n\n  /**\n   * @type {ToMarkdownHandle}\n   * @param {FootnoteDefinition} node\n   */\n  function footnoteDefinition(node, _, state, info) {\n    const tracker = state.createTracker(info)\n    let value = tracker.move('[^')\n    const exit = state.enter('footnoteDefinition')\n    const subexit = state.enter('label')\n    value += tracker.move(\n      state.safe(state.associationId(node), {before: value, after: ']'})\n    )\n    subexit()\n\n    value += tracker.move(']:')\n\n    if (node.children && node.children.length > 0) {\n      tracker.shift(4)\n\n      value += tracker.move(\n        (firstLineBlank ? '\\n' : ' ') +\n          state.indentLines(\n            state.containerFlow(node, tracker.current()),\n            firstLineBlank ? mapAll : mapExceptFirst\n          )\n      )\n    }\n\n    exit()\n\n    return value\n  }\n}\n\n/** @type {Map} */\nfunction mapExceptFirst(line, index, blank) {\n  return index === 0 ? line : mapAll(line, index, blank)\n}\n\n/** @type {Map} */\nfunction mapAll(line, index, blank) {\n  return (blank ? '' : '    ') + line\n}\n", "/**\n * @typedef {import('mdast').Delete} Delete\n *\n * @typedef {import('mdast-util-from-markdown').CompileContext} CompileContext\n * @typedef {import('mdast-util-from-markdown').Extension} FromMarkdownExtension\n * @typedef {import('mdast-util-from-markdown').Handle} FromMarkdownHandle\n *\n * @typedef {import('mdast-util-to-markdown').ConstructName} ConstructName\n * @typedef {import('mdast-util-to-markdown').Handle} ToMarkdownHandle\n * @typedef {import('mdast-util-to-markdown').Options} ToMarkdownExtension\n */\n\n/**\n * List of constructs that occur in phrasing (paragraphs, headings), but cannot\n * contain strikethrough.\n * So they sort of cancel each other out.\n * Note: could use a better name.\n *\n * Note: keep in sync with: <https://github.com/syntax-tree/mdast-util-to-markdown/blob/8ce8dbf/lib/unsafe.js#L14>\n *\n * @type {Array<ConstructName>}\n */\nconst constructsWithoutStrikethrough = [\n  'autolink',\n  'destinationLiteral',\n  'destinationRaw',\n  'reference',\n  'titleQuote',\n  'titleApostrophe'\n]\n\nhandleDelete.peek = peekDelete\n\n/**\n * Create an extension for `mdast-util-from-markdown` to enable GFM\n * strikethrough in markdown.\n *\n * @returns {FromMarkdownExtension}\n *   Extension for `mdast-util-from-markdown` to enable GFM strikethrough.\n */\nexport function gfmStrikethroughFromMarkdown() {\n  return {\n    canContainEols: ['delete'],\n    enter: {strikethrough: enterStrikethrough},\n    exit: {strikethrough: exitStrikethrough}\n  }\n}\n\n/**\n * Create an extension for `mdast-util-to-markdown` to enable GFM\n * strikethrough in markdown.\n *\n * @returns {ToMarkdownExtension}\n *   Extension for `mdast-util-to-markdown` to enable GFM strikethrough.\n */\nexport function gfmStrikethroughToMarkdown() {\n  return {\n    unsafe: [\n      {\n        character: '~',\n        inConstruct: 'phrasing',\n        notInConstruct: constructsWithoutStrikethrough\n      }\n    ],\n    handlers: {delete: handleDelete}\n  }\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterStrikethrough(token) {\n  this.enter({type: 'delete', children: []}, token)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitStrikethrough(token) {\n  this.exit(token)\n}\n\n/**\n * @type {ToMarkdownHandle}\n * @param {Delete} node\n */\nfunction handleDelete(node, _, state, info) {\n  const tracker = state.createTracker(info)\n  const exit = state.enter('strikethrough')\n  let value = tracker.move('~~')\n  value += state.containerPhrasing(node, {\n    ...tracker.current(),\n    before: value,\n    after: '~'\n  })\n  value += tracker.move('~~')\n  exit()\n  return value\n}\n\n/** @type {ToMarkdownHandle} */\nfunction peekDelete() {\n  return '~'\n}\n", "// To do: next major: remove.\n/**\n * @typedef {Options} MarkdownTableOptions\n *   Configuration.\n */\n\n/**\n * @typedef Options\n *   Configuration.\n * @property {boolean | null | undefined} [alignDelimiters=true]\n *   Whether to align the delimiters (default: `true`);\n *   they are aligned by default:\n *\n *   ```markdown\n *   | Alpha | B     |\n *   | ----- | ----- |\n *   | C     | Delta |\n *   ```\n *\n *   Pass `false` to make them staggered:\n *\n *   ```markdown\n *   | Alpha | B |\n *   | - | - |\n *   | C | Delta |\n *   ```\n * @property {ReadonlyArray<string | null | undefined> | string | null | undefined} [align]\n *   How to align columns (default: `''`);\n *   one style for all columns or styles for their respective columns;\n *   each style is either `'l'` (left), `'r'` (right), or `'c'` (center);\n *   other values are treated as `''`, which doesn’t place the colon in the\n *   alignment row but does align left;\n *   *only the lowercased first character is used, so `Right` is fine.*\n * @property {boolean | null | undefined} [delimiterEnd=true]\n *   Whether to end each row with the delimiter (default: `true`).\n *\n *   > 👉 **Note**: please don’t use this: it could create fragile structures\n *   > that aren’t understandable to some markdown parsers.\n *\n *   When `true`, there are ending delimiters:\n *\n *   ```markdown\n *   | Alpha | B     |\n *   | ----- | ----- |\n *   | C     | Delta |\n *   ```\n *\n *   When `false`, there are no ending delimiters:\n *\n *   ```markdown\n *   | Alpha | B\n *   | ----- | -----\n *   | C     | Delta\n *   ```\n * @property {boolean | null | undefined} [delimiterStart=true]\n *   Whether to begin each row with the delimiter (default: `true`).\n *\n *   > 👉 **Note**: please don’t use this: it could create fragile structures\n *   > that aren’t understandable to some markdown parsers.\n *\n *   When `true`, there are starting delimiters:\n *\n *   ```markdown\n *   | Alpha | B     |\n *   | ----- | ----- |\n *   | C     | Delta |\n *   ```\n *\n *   When `false`, there are no starting delimiters:\n *\n *   ```markdown\n *   Alpha | B     |\n *   ----- | ----- |\n *   C     | Delta |\n *   ```\n * @property {boolean | null | undefined} [padding=true]\n *   Whether to add a space of padding between delimiters and cells\n *   (default: `true`).\n *\n *   When `true`, there is padding:\n *\n *   ```markdown\n *   | Alpha | B     |\n *   | ----- | ----- |\n *   | C     | Delta |\n *   ```\n *\n *   When `false`, there is no padding:\n *\n *   ```markdown\n *   |Alpha|B    |\n *   |-----|-----|\n *   |C    |Delta|\n *   ```\n * @property {((value: string) => number) | null | undefined} [stringLength]\n *   Function to detect the length of table cell content (optional);\n *   this is used when aligning the delimiters (`|`) between table cells;\n *   full-width characters and emoji mess up delimiter alignment when viewing\n *   the markdown source;\n *   to fix this, you can pass this function,\n *   which receives the cell content and returns its “visible” size;\n *   note that what is and isn’t visible depends on where the text is displayed.\n *\n *   Without such a function, the following:\n *\n *   ```js\n *   markdownTable([\n *     ['Alpha', 'Bravo'],\n *     ['中文', 'Charlie'],\n *     ['👩‍❤️‍👩', 'Delta']\n *   ])\n *   ```\n *\n *   Yields:\n *\n *   ```markdown\n *   | Alpha | Bravo |\n *   | - | - |\n *   | 中文 | Charlie |\n *   | 👩‍❤️‍👩 | Delta |\n *   ```\n *\n *   With [`string-width`](https://github.com/sindresorhus/string-width):\n *\n *   ```js\n *   import stringWidth from 'string-width'\n *\n *   markdownTable(\n *     [\n *       ['Alpha', 'Bravo'],\n *       ['中文', 'Charlie'],\n *       ['👩‍❤️‍👩', 'Delta']\n *     ],\n *     {stringLength: stringWidth}\n *   )\n *   ```\n *\n *   Yields:\n *\n *   ```markdown\n *   | Alpha | Bravo   |\n *   | ----- | ------- |\n *   | 中文  | Charlie |\n *   | 👩‍❤️‍👩    | Delta   |\n *   ```\n */\n\n/**\n * @param {string} value\n *   Cell value.\n * @returns {number}\n *   Cell size.\n */\nfunction defaultStringLength(value) {\n  return value.length\n}\n\n/**\n * Generate a markdown\n * ([GFM](https://docs.github.com/en/github/writing-on-github/working-with-advanced-formatting/organizing-information-with-tables))\n * table.\n *\n * @param {ReadonlyArray<ReadonlyArray<string | null | undefined>>} table\n *   Table data (matrix of strings).\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Result.\n */\nexport function markdownTable(table, options) {\n  const settings = options || {}\n  // To do: next major: change to spread.\n  const align = (settings.align || []).concat()\n  const stringLength = settings.stringLength || defaultStringLength\n  /** @type {Array<number>} Character codes as symbols for alignment per column. */\n  const alignments = []\n  /** @type {Array<Array<string>>} Cells per row. */\n  const cellMatrix = []\n  /** @type {Array<Array<number>>} Sizes of each cell per row. */\n  const sizeMatrix = []\n  /** @type {Array<number>} */\n  const longestCellByColumn = []\n  let mostCellsPerRow = 0\n  let rowIndex = -1\n\n  // This is a superfluous loop if we don’t align delimiters, but otherwise we’d\n  // do superfluous work when aligning, so optimize for aligning.\n  while (++rowIndex < table.length) {\n    /** @type {Array<string>} */\n    const row = []\n    /** @type {Array<number>} */\n    const sizes = []\n    let columnIndex = -1\n\n    if (table[rowIndex].length > mostCellsPerRow) {\n      mostCellsPerRow = table[rowIndex].length\n    }\n\n    while (++columnIndex < table[rowIndex].length) {\n      const cell = serialize(table[rowIndex][columnIndex])\n\n      if (settings.alignDelimiters !== false) {\n        const size = stringLength(cell)\n        sizes[columnIndex] = size\n\n        if (\n          longestCellByColumn[columnIndex] === undefined ||\n          size > longestCellByColumn[columnIndex]\n        ) {\n          longestCellByColumn[columnIndex] = size\n        }\n      }\n\n      row.push(cell)\n    }\n\n    cellMatrix[rowIndex] = row\n    sizeMatrix[rowIndex] = sizes\n  }\n\n  // Figure out which alignments to use.\n  let columnIndex = -1\n\n  if (typeof align === 'object' && 'length' in align) {\n    while (++columnIndex < mostCellsPerRow) {\n      alignments[columnIndex] = toAlignment(align[columnIndex])\n    }\n  } else {\n    const code = toAlignment(align)\n\n    while (++columnIndex < mostCellsPerRow) {\n      alignments[columnIndex] = code\n    }\n  }\n\n  // Inject the alignment row.\n  columnIndex = -1\n  /** @type {Array<string>} */\n  const row = []\n  /** @type {Array<number>} */\n  const sizes = []\n\n  while (++columnIndex < mostCellsPerRow) {\n    const code = alignments[columnIndex]\n    let before = ''\n    let after = ''\n\n    if (code === 99 /* `c` */) {\n      before = ':'\n      after = ':'\n    } else if (code === 108 /* `l` */) {\n      before = ':'\n    } else if (code === 114 /* `r` */) {\n      after = ':'\n    }\n\n    // There *must* be at least one hyphen-minus in each alignment cell.\n    let size =\n      settings.alignDelimiters === false\n        ? 1\n        : Math.max(\n            1,\n            longestCellByColumn[columnIndex] - before.length - after.length\n          )\n\n    const cell = before + '-'.repeat(size) + after\n\n    if (settings.alignDelimiters !== false) {\n      size = before.length + size + after.length\n\n      if (size > longestCellByColumn[columnIndex]) {\n        longestCellByColumn[columnIndex] = size\n      }\n\n      sizes[columnIndex] = size\n    }\n\n    row[columnIndex] = cell\n  }\n\n  // Inject the alignment row.\n  cellMatrix.splice(1, 0, row)\n  sizeMatrix.splice(1, 0, sizes)\n\n  rowIndex = -1\n  /** @type {Array<string>} */\n  const lines = []\n\n  while (++rowIndex < cellMatrix.length) {\n    const row = cellMatrix[rowIndex]\n    const sizes = sizeMatrix[rowIndex]\n    columnIndex = -1\n    /** @type {Array<string>} */\n    const line = []\n\n    while (++columnIndex < mostCellsPerRow) {\n      const cell = row[columnIndex] || ''\n      let before = ''\n      let after = ''\n\n      if (settings.alignDelimiters !== false) {\n        const size =\n          longestCellByColumn[columnIndex] - (sizes[columnIndex] || 0)\n        const code = alignments[columnIndex]\n\n        if (code === 114 /* `r` */) {\n          before = ' '.repeat(size)\n        } else if (code === 99 /* `c` */) {\n          if (size % 2) {\n            before = ' '.repeat(size / 2 + 0.5)\n            after = ' '.repeat(size / 2 - 0.5)\n          } else {\n            before = ' '.repeat(size / 2)\n            after = before\n          }\n        } else {\n          after = ' '.repeat(size)\n        }\n      }\n\n      if (settings.delimiterStart !== false && !columnIndex) {\n        line.push('|')\n      }\n\n      if (\n        settings.padding !== false &&\n        // Don’t add the opening space if we’re not aligning and the cell is\n        // empty: there will be a closing space.\n        !(settings.alignDelimiters === false && cell === '') &&\n        (settings.delimiterStart !== false || columnIndex)\n      ) {\n        line.push(' ')\n      }\n\n      if (settings.alignDelimiters !== false) {\n        line.push(before)\n      }\n\n      line.push(cell)\n\n      if (settings.alignDelimiters !== false) {\n        line.push(after)\n      }\n\n      if (settings.padding !== false) {\n        line.push(' ')\n      }\n\n      if (\n        settings.delimiterEnd !== false ||\n        columnIndex !== mostCellsPerRow - 1\n      ) {\n        line.push('|')\n      }\n    }\n\n    lines.push(\n      settings.delimiterEnd === false\n        ? line.join('').replace(/ +$/, '')\n        : line.join('')\n    )\n  }\n\n  return lines.join('\\n')\n}\n\n/**\n * @param {string | null | undefined} [value]\n *   Value to serialize.\n * @returns {string}\n *   Result.\n */\nfunction serialize(value) {\n  return value === null || value === undefined ? '' : String(value)\n}\n\n/**\n * @param {string | null | undefined} value\n *   Value.\n * @returns {number}\n *   Alignment.\n */\nfunction toAlignment(value) {\n  const code = typeof value === 'string' ? value.codePointAt(0) : 0\n\n  return code === 67 /* `C` */ || code === 99 /* `c` */\n    ? 99 /* `c` */\n    : code === 76 /* `L` */ || code === 108 /* `l` */\n      ? 108 /* `l` */\n      : code === 82 /* `R` */ || code === 114 /* `r` */\n        ? 114 /* `r` */\n        : 0\n}\n", "/**\n * @callback Handler\n *   Handle a value, with a certain ID field set to a certain value.\n *   The ID field is passed to `zwitch`, and it’s value is this function’s\n *   place on the `handlers` record.\n * @param {...any} parameters\n *   Arbitrary parameters passed to the zwitch.\n *   The first will be an object with a certain ID field set to a certain value.\n * @returns {any}\n *   Anything!\n */\n\n/**\n * @callback UnknownHandler\n *   Handle values that do have a certain ID field, but it’s set to a value\n *   that is not listed in the `handlers` record.\n * @param {unknown} value\n *   An object with a certain ID field set to an unknown value.\n * @param {...any} rest\n *   Arbitrary parameters passed to the zwitch.\n * @returns {any}\n *   Anything!\n */\n\n/**\n * @callback InvalidHandler\n *   Handle values that do not have a certain ID field.\n * @param {unknown} value\n *   Any unknown value.\n * @param {...any} rest\n *   Arbitrary parameters passed to the zwitch.\n * @returns {void|null|undefined|never}\n *   This should crash or return nothing.\n */\n\n/**\n * @template {InvalidHandler} [Invalid=InvalidHandler]\n * @template {UnknownHandler} [Unknown=UnknownHandler]\n * @template {Record<string, Handler>} [Handlers=Record<string, Handler>]\n * @typedef Options\n *   Configuration (required).\n * @property {Invalid} [invalid]\n *   Handler to use for invalid values.\n * @property {Unknown} [unknown]\n *   Handler to use for unknown values.\n * @property {Handlers} [handlers]\n *   Handlers to use.\n */\n\nconst own = {}.hasOwnProperty\n\n/**\n * Handle values based on a field.\n *\n * @template {InvalidHandler} [Invalid=InvalidHandler]\n * @template {UnknownHandler} [Unknown=UnknownHandler]\n * @template {Record<string, Handler>} [Handlers=Record<string, Handler>]\n * @param {string} key\n *   Field to switch on.\n * @param {Options<Invalid, Unknown, Handlers>} [options]\n *   Configuration (required).\n * @returns {{unknown: Unknown, invalid: Invalid, handlers: Handlers, (...parameters: Parameters<Handlers[keyof Handlers]>): ReturnType<Handlers[keyof Handlers]>, (...parameters: Parameters<Unknown>): ReturnType<Unknown>}}\n */\nexport function zwitch(key, options) {\n  const settings = options || {}\n\n  /**\n   * Handle one value.\n   *\n   * Based on the bound `key`, a respective handler will be called.\n   * If `value` is not an object, or doesn’t have a `key` property, the special\n   * “invalid” handler will be called.\n   * If `value` has an unknown `key`, the special “unknown” handler will be\n   * called.\n   *\n   * All arguments, and the context object, are passed through to the handler,\n   * and it’s result is returned.\n   *\n   * @this {unknown}\n   *   Any context object.\n   * @param {unknown} [value]\n   *   Any value.\n   * @param {...unknown} parameters\n   *   Arbitrary parameters passed to the zwitch.\n   * @property {Handler} invalid\n   *   Handle for values that do not have a certain ID field.\n   * @property {Handler} unknown\n   *   Handle values that do have a certain ID field, but it’s set to a value\n   *   that is not listed in the `handlers` record.\n   * @property {Handlers} handlers\n   *   Record of handlers.\n   * @returns {unknown}\n   *   Anything.\n   */\n  function one(value, ...parameters) {\n    /** @type {Handler|undefined} */\n    let fn = one.invalid\n    const handlers = one.handlers\n\n    if (value && own.call(value, key)) {\n      // @ts-expect-error Indexable.\n      const id = String(value[key])\n      // @ts-expect-error Indexable.\n      fn = own.call(handlers, id) ? handlers[id] : one.unknown\n    }\n\n    if (fn) {\n      return fn.call(this, value, ...parameters)\n    }\n  }\n\n  one.handlers = settings.handlers || {}\n  one.invalid = settings.invalid\n  one.unknown = settings.unknown\n\n  // @ts-expect-error: matches!\n  return one\n}\n", "/**\n * @import {Options, State} from './types.js'\n */\n\nconst own = {}.hasOwnProperty\n\n/**\n * @param {State} base\n * @param {Options} extension\n * @returns {State}\n */\nexport function configure(base, extension) {\n  let index = -1\n  /** @type {keyof Options} */\n  let key\n\n  // First do subextensions.\n  if (extension.extensions) {\n    while (++index < extension.extensions.length) {\n      configure(base, extension.extensions[index])\n    }\n  }\n\n  for (key in extension) {\n    if (own.call(extension, key)) {\n      switch (key) {\n        case 'extensions': {\n          // Empty.\n          break\n        }\n\n        /* c8 ignore next 4 */\n        case 'unsafe': {\n          list(base[key], extension[key])\n          break\n        }\n\n        case 'join': {\n          list(base[key], extension[key])\n          break\n        }\n\n        case 'handlers': {\n          map(base[key], extension[key])\n          break\n        }\n\n        default: {\n          // @ts-expect-error: matches.\n          base.options[key] = extension[key]\n        }\n      }\n    }\n  }\n\n  return base\n}\n\n/**\n * @template T\n * @param {Array<T>} left\n * @param {Array<T> | null | undefined} right\n */\nfunction list(left, right) {\n  if (right) {\n    left.push(...right)\n  }\n}\n\n/**\n * @template T\n * @param {Record<string, T>} left\n * @param {Record<string, T> | null | undefined} right\n */\nfunction map(left, right) {\n  if (right) {\n    Object.assign(left, right)\n  }\n}\n", "/**\n * @import {Blockquote, Parents} from 'mdast'\n * @import {Info, Map, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {Blockquote} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function blockquote(node, _, state, info) {\n  const exit = state.enter('blockquote')\n  const tracker = state.createTracker(info)\n  tracker.move('> ')\n  tracker.shift(2)\n  const value = state.indentLines(\n    state.containerFlow(node, tracker.current()),\n    map\n  )\n  exit()\n  return value\n}\n\n/** @type {Map} */\nfunction map(line, _, blank) {\n  return '>' + (blank ? '' : ' ') + line\n}\n", "/**\n * @import {ConstructName, Unsafe} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {Array<ConstructName>} stack\n * @param {Unsafe} pattern\n * @returns {boolean}\n */\nexport function patternInScope(stack, pattern) {\n  return (\n    listInScope(stack, pattern.inConstruct, true) &&\n    !listInScope(stack, pattern.notInConstruct, false)\n  )\n}\n\n/**\n * @param {Array<ConstructName>} stack\n * @param {Unsafe['inConstruct']} list\n * @param {boolean} none\n * @returns {boolean}\n */\nfunction listInScope(stack, list, none) {\n  if (typeof list === 'string') {\n    list = [list]\n  }\n\n  if (!list || list.length === 0) {\n    return none\n  }\n\n  let index = -1\n\n  while (++index < list.length) {\n    if (stack.includes(list[index])) {\n      return true\n    }\n  }\n\n  return false\n}\n", "/**\n * @import {Break, Parents} from 'mdast'\n * @import {Info, State} from 'mdast-util-to-markdown'\n */\n\nimport {patternInScope} from '../util/pattern-in-scope.js'\n\n/**\n * @param {Break} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function hardBreak(_, _1, state, info) {\n  let index = -1\n\n  while (++index < state.unsafe.length) {\n    // If we can’t put eols in this construct (setext headings, tables), use a\n    // space instead.\n    if (\n      state.unsafe[index].character === '\\n' &&\n      patternInScope(state.stack, state.unsafe[index])\n    ) {\n      return /[ \\t]/.test(info.before) ? '' : ' '\n    }\n  }\n\n  return '\\\\\\n'\n}\n", "/**\n * Get the count of the longest repeating streak of `substring` in `value`.\n *\n * @param {string} value\n *   Content to search in.\n * @param {string} substring\n *   Substring to look for, typically one character.\n * @returns {number}\n *   Count of most frequent adjacent `substring`s in `value`.\n */\nexport function longestStreak(value, substring) {\n  const source = String(value)\n  let index = source.indexOf(substring)\n  let expected = index\n  let count = 0\n  let max = 0\n\n  if (typeof substring !== 'string') {\n    throw new TypeError('Expected substring')\n  }\n\n  while (index !== -1) {\n    if (index === expected) {\n      if (++count > max) {\n        max = count\n      }\n    } else {\n      count = 1\n    }\n\n    expected = index + substring.length\n    index = source.indexOf(substring, expected)\n  }\n\n  return max\n}\n", "/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Code} from 'mdast'\n */\n\n/**\n * @param {Code} node\n * @param {State} state\n * @returns {boolean}\n */\nexport function formatCodeAsIndented(node, state) {\n  return Boolean(\n    state.options.fences === false &&\n      node.value &&\n      // If there’s no info…\n      !node.lang &&\n      // And there’s a non-whitespace character…\n      /[^ \\r\\n]/.test(node.value) &&\n      // And the value doesn’t start or end in a blank…\n      !/^[\\t ]*(?:[\\r\\n]|$)|(?:^|[\\r\\n])[\\t ]*$/.test(node.value)\n  )\n}\n", "/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['fence'], null | undefined>}\n */\nexport function checkFence(state) {\n  const marker = state.options.fence || '`'\n\n  if (marker !== '`' && marker !== '~') {\n    throw new Error(\n      'Cannot serialize code with `' +\n        marker +\n        '` for `options.fence`, expected `` ` `` or `~`'\n    )\n  }\n\n  return marker\n}\n", "/**\n * @import {Info, Map, State} from 'mdast-util-to-markdown'\n * @import {Code, Parents} from 'mdast'\n */\n\nimport {longestStreak} from 'longest-streak'\nimport {formatCodeAsIndented} from '../util/format-code-as-indented.js'\nimport {checkFence} from '../util/check-fence.js'\n\n/**\n * @param {Code} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function code(node, _, state, info) {\n  const marker = checkFence(state)\n  const raw = node.value || ''\n  const suffix = marker === '`' ? 'GraveAccent' : 'Tilde'\n\n  if (formatCodeAsIndented(node, state)) {\n    const exit = state.enter('codeIndented')\n    const value = state.indentLines(raw, map)\n    exit()\n    return value\n  }\n\n  const tracker = state.createTracker(info)\n  const sequence = marker.repeat(Math.max(longestStreak(raw, marker) + 1, 3))\n  const exit = state.enter('codeFenced')\n  let value = tracker.move(sequence)\n\n  if (node.lang) {\n    const subexit = state.enter(`codeFencedLang${suffix}`)\n    value += tracker.move(\n      state.safe(node.lang, {\n        before: value,\n        after: ' ',\n        encode: ['`'],\n        ...tracker.current()\n      })\n    )\n    subexit()\n  }\n\n  if (node.lang && node.meta) {\n    const subexit = state.enter(`codeFencedMeta${suffix}`)\n    value += tracker.move(' ')\n    value += tracker.move(\n      state.safe(node.meta, {\n        before: value,\n        after: '\\n',\n        encode: ['`'],\n        ...tracker.current()\n      })\n    )\n    subexit()\n  }\n\n  value += tracker.move('\\n')\n\n  if (raw) {\n    value += tracker.move(raw + '\\n')\n  }\n\n  value += tracker.move(sequence)\n  exit()\n  return value\n}\n\n/** @type {Map} */\nfunction map(line, _, blank) {\n  return (blank ? '' : '    ') + line\n}\n", "/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['quote'], null | undefined>}\n */\nexport function checkQuote(state) {\n  const marker = state.options.quote || '\"'\n\n  if (marker !== '\"' && marker !== \"'\") {\n    throw new Error(\n      'Cannot serialize title with `' +\n        marker +\n        '` for `options.quote`, expected `\"`, or `\\'`'\n    )\n  }\n\n  return marker\n}\n", "/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Definition, Parents} from 'mdast'\n */\n\nimport {checkQuote} from '../util/check-quote.js'\n\n/**\n * @param {Definition} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function definition(node, _, state, info) {\n  const quote = checkQuote(state)\n  const suffix = quote === '\"' ? 'Quote' : 'Apostrophe'\n  const exit = state.enter('definition')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('[')\n  value += tracker.move(\n    state.safe(state.associationId(node), {\n      before: value,\n      after: ']',\n      ...tracker.current()\n    })\n  )\n  value += tracker.move(']: ')\n\n  subexit()\n\n  if (\n    // If there’s no url, or…\n    !node.url ||\n    // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)\n  ) {\n    subexit = state.enter('destinationLiteral')\n    value += tracker.move('<')\n    value += tracker.move(\n      state.safe(node.url, {before: value, after: '>', ...tracker.current()})\n    )\n    value += tracker.move('>')\n  } else {\n    // No whitespace, raw is prettier.\n    subexit = state.enter('destinationRaw')\n    value += tracker.move(\n      state.safe(node.url, {\n        before: value,\n        after: node.title ? ' ' : '\\n',\n        ...tracker.current()\n      })\n    )\n  }\n\n  subexit()\n\n  if (node.title) {\n    subexit = state.enter(`title${suffix}`)\n    value += tracker.move(' ' + quote)\n    value += tracker.move(\n      state.safe(node.title, {\n        before: value,\n        after: quote,\n        ...tracker.current()\n      })\n    )\n    value += tracker.move(quote)\n    subexit()\n  }\n\n  exit()\n\n  return value\n}\n", "/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['emphasis'], null | undefined>}\n */\nexport function checkEmphasis(state) {\n  const marker = state.options.emphasis || '*'\n\n  if (marker !== '*' && marker !== '_') {\n    throw new Error(\n      'Cannot serialize emphasis with `' +\n        marker +\n        '` for `options.emphasis`, expected `*`, or `_`'\n    )\n  }\n\n  return marker\n}\n", "/**\n * Encode a code point as a character reference.\n *\n * @param {number} code\n *   Code point to encode.\n * @returns {string}\n *   Encoded character reference.\n */\nexport function encodeCharacterReference(code) {\n  return '&#x' + code.toString(16).toUpperCase() + ';'\n}\n", "/**\n * @import {EncodeSides} from '../types.js'\n */\n\nimport {classifyCharacter} from 'micromark-util-classify-character'\n\n/**\n * Check whether to encode (as a character reference) the characters\n * surrounding an attention run.\n *\n * Which characters are around an attention run influence whether it works or\n * not.\n *\n * See <https://github.com/orgs/syntax-tree/discussions/60> for more info.\n * See this markdown in a particular renderer to see what works:\n *\n * ```markdown\n * |                         | A (letter inside) | B (punctuation inside) | C (whitespace inside) | D (nothing inside) |\n * | ----------------------- | ----------------- | ---------------------- | --------------------- | ------------------ |\n * | 1 (letter outside)      | x*y*z             | x*.*z                  | x* *z                 | x**z               |\n * | 2 (punctuation outside) | .*y*.             | .*.*.                  | .* *.                 | .**.               |\n * | 3 (whitespace outside)  | x *y* z           | x *.* z                | x * * z               | x ** z             |\n * | 4 (nothing outside)     | *x*               | *.*                    | * *                   | **                 |\n * ```\n *\n * @param {number} outside\n *   Code point on the outer side of the run.\n * @param {number} inside\n *   Code point on the inner side of the run.\n * @param {'*' | '_'} marker\n *   Marker of the run.\n *   Underscores are handled more strictly (they form less often) than\n *   asterisks.\n * @returns {EncodeSides}\n *   Whether to encode characters.\n */\n// Important: punctuation must never be encoded.\n// Punctuation is solely used by markdown constructs.\n// And by encoding itself.\n// Encoding them will break constructs or double encode things.\nexport function encodeInfo(outside, inside, marker) {\n  const outsideKind = classifyCharacter(outside)\n  const insideKind = classifyCharacter(inside)\n\n  // Letter outside:\n  if (outsideKind === undefined) {\n    return insideKind === undefined\n      ? // Letter inside:\n        // we have to encode *both* letters for `_` as it is looser.\n        // it already forms for `*` (and GFMs `~`).\n        marker === '_'\n        ? {inside: true, outside: true}\n        : {inside: false, outside: false}\n      : insideKind === 1\n        ? // Whitespace inside: encode both (letter, whitespace).\n          {inside: true, outside: true}\n        : // Punctuation inside: encode outer (letter)\n          {inside: false, outside: true}\n  }\n\n  // Whitespace outside:\n  if (outsideKind === 1) {\n    return insideKind === undefined\n      ? // Letter inside: already forms.\n        {inside: false, outside: false}\n      : insideKind === 1\n        ? // Whitespace inside: encode both (whitespace).\n          {inside: true, outside: true}\n        : // Punctuation inside: already forms.\n          {inside: false, outside: false}\n  }\n\n  // Punctuation outside:\n  return insideKind === undefined\n    ? // Letter inside: already forms.\n      {inside: false, outside: false}\n    : insideKind === 1\n      ? // Whitespace inside: encode inner (whitespace).\n        {inside: true, outside: false}\n      : // Punctuation inside: already forms.\n        {inside: false, outside: false}\n}\n", "/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Emphasis, Parents} from 'mdast'\n */\n\nimport {checkEmphasis} from '../util/check-emphasis.js'\nimport {encodeCharacterReference} from '../util/encode-character-reference.js'\nimport {encodeInfo} from '../util/encode-info.js'\n\nemphasis.peek = emphasisPeek\n\n/**\n * @param {Emphasis} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function emphasis(node, _, state, info) {\n  const marker = checkEmphasis(state)\n  const exit = state.enter('emphasis')\n  const tracker = state.createTracker(info)\n  const before = tracker.move(marker)\n\n  let between = tracker.move(\n    state.containerPhrasing(node, {\n      after: marker,\n      before,\n      ...tracker.current()\n    })\n  )\n  const betweenHead = between.charCodeAt(0)\n  const open = encodeInfo(\n    info.before.charCodeAt(info.before.length - 1),\n    betweenHead,\n    marker\n  )\n\n  if (open.inside) {\n    between = encodeCharacterReference(betweenHead) + between.slice(1)\n  }\n\n  const betweenTail = between.charCodeAt(between.length - 1)\n  const close = encodeInfo(info.after.charCodeAt(0), betweenTail, marker)\n\n  if (close.inside) {\n    between = between.slice(0, -1) + encodeCharacterReference(betweenTail)\n  }\n\n  const after = tracker.move(marker)\n\n  exit()\n\n  state.attentionEncodeSurroundingInfo = {\n    after: close.outside,\n    before: open.outside\n  }\n  return before + between + after\n}\n\n/**\n * @param {Emphasis} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @returns {string}\n */\nfunction emphasisPeek(_, _1, state) {\n  return state.options.emphasis || '*'\n}\n", "/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Heading} from 'mdast'\n */\n\nimport {EXIT, visit} from 'unist-util-visit'\nimport {toString} from 'mdast-util-to-string'\n\n/**\n * @param {Heading} node\n * @param {State} state\n * @returns {boolean}\n */\nexport function formatHeadingAsSetext(node, state) {\n  let literalWithBreak = false\n\n  // Look for literals with a line break.\n  // Note that this also\n  visit(node, function (node) {\n    if (\n      ('value' in node && /\\r?\\n|\\r/.test(node.value)) ||\n      node.type === 'break'\n    ) {\n      literalWithBreak = true\n      return EXIT\n    }\n  })\n\n  return Boolean(\n    (!node.depth || node.depth < 3) &&\n      toString(node) &&\n      (state.options.setext || literalWithBreak)\n  )\n}\n", "/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Heading, Parents} from 'mdast'\n */\n\nimport {encodeCharacterReference} from '../util/encode-character-reference.js'\nimport {formatHeadingAsSetext} from '../util/format-heading-as-setext.js'\n\n/**\n * @param {Heading} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function heading(node, _, state, info) {\n  const rank = Math.max(Math.min(6, node.depth || 1), 1)\n  const tracker = state.createTracker(info)\n\n  if (formatHeadingAsSetext(node, state)) {\n    const exit = state.enter('headingSetext')\n    const subexit = state.enter('phrasing')\n    const value = state.containerPhrasing(node, {\n      ...tracker.current(),\n      before: '\\n',\n      after: '\\n'\n    })\n    subexit()\n    exit()\n\n    return (\n      value +\n      '\\n' +\n      (rank === 1 ? '=' : '-').repeat(\n        // The whole size…\n        value.length -\n          // Minus the position of the character after the last EOL (or\n          // 0 if there is none)…\n          (Math.max(value.lastIndexOf('\\r'), value.lastIndexOf('\\n')) + 1)\n      )\n    )\n  }\n\n  const sequence = '#'.repeat(rank)\n  const exit = state.enter('headingAtx')\n  const subexit = state.enter('phrasing')\n\n  // Note: for proper tracking, we should reset the output positions when there\n  // is no content returned, because then the space is not output.\n  // Practically, in that case, there is no content, so it doesn’t matter that\n  // we’ve tracked one too many characters.\n  tracker.move(sequence + ' ')\n\n  let value = state.containerPhrasing(node, {\n    before: '# ',\n    after: '\\n',\n    ...tracker.current()\n  })\n\n  if (/^[\\t ]/.test(value)) {\n    // To do: what effect has the character reference on tracking?\n    value = encodeCharacterReference(value.charCodeAt(0)) + value.slice(1)\n  }\n\n  value = value ? sequence + ' ' + value : sequence\n\n  if (state.options.closeAtx) {\n    value += ' ' + sequence\n  }\n\n  subexit()\n  exit()\n\n  return value\n}\n", "/**\n * @import {Html} from 'mdast'\n */\n\nhtml.peek = htmlPeek\n\n/**\n * @param {Html} node\n * @returns {string}\n */\nexport function html(node) {\n  return node.value || ''\n}\n\n/**\n * @returns {string}\n */\nfunction htmlPeek() {\n  return '<'\n}\n", "/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Image, Parents} from 'mdast'\n */\n\nimport {checkQuote} from '../util/check-quote.js'\n\nimage.peek = imagePeek\n\n/**\n * @param {Image} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function image(node, _, state, info) {\n  const quote = checkQuote(state)\n  const suffix = quote === '\"' ? 'Quote' : 'Apostrophe'\n  const exit = state.enter('image')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('![')\n  value += tracker.move(\n    state.safe(node.alt, {before: value, after: ']', ...tracker.current()})\n  )\n  value += tracker.move('](')\n\n  subexit()\n\n  if (\n    // If there’s no url but there is a title…\n    (!node.url && node.title) ||\n    // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)\n  ) {\n    subexit = state.enter('destinationLiteral')\n    value += tracker.move('<')\n    value += tracker.move(\n      state.safe(node.url, {before: value, after: '>', ...tracker.current()})\n    )\n    value += tracker.move('>')\n  } else {\n    // No whitespace, raw is prettier.\n    subexit = state.enter('destinationRaw')\n    value += tracker.move(\n      state.safe(node.url, {\n        before: value,\n        after: node.title ? ' ' : ')',\n        ...tracker.current()\n      })\n    )\n  }\n\n  subexit()\n\n  if (node.title) {\n    subexit = state.enter(`title${suffix}`)\n    value += tracker.move(' ' + quote)\n    value += tracker.move(\n      state.safe(node.title, {\n        before: value,\n        after: quote,\n        ...tracker.current()\n      })\n    )\n    value += tracker.move(quote)\n    subexit()\n  }\n\n  value += tracker.move(')')\n  exit()\n\n  return value\n}\n\n/**\n * @returns {string}\n */\nfunction imagePeek() {\n  return '!'\n}\n", "/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {ImageReference, Parents} from 'mdast'\n */\n\nimageReference.peek = imageReferencePeek\n\n/**\n * @param {ImageReference} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function imageReference(node, _, state, info) {\n  const type = node.referenceType\n  const exit = state.enter('imageReference')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('![')\n  const alt = state.safe(node.alt, {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  value += tracker.move(alt + '][')\n\n  subexit()\n  // Hide the fact that we’re in phrasing, because escapes don’t work.\n  const stack = state.stack\n  state.stack = []\n  subexit = state.enter('reference')\n  // Note: for proper tracking, we should reset the output positions when we end\n  // up making a `shortcut` reference, because then there is no brace output.\n  // Practically, in that case, there is no content, so it doesn’t matter that\n  // we’ve tracked one too many characters.\n  const reference = state.safe(state.associationId(node), {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  subexit()\n  state.stack = stack\n  exit()\n\n  if (type === 'full' || !alt || alt !== reference) {\n    value += tracker.move(reference + ']')\n  } else if (type === 'shortcut') {\n    // Remove the unwanted `[`.\n    value = value.slice(0, -1)\n  } else {\n    value += tracker.move(']')\n  }\n\n  return value\n}\n\n/**\n * @returns {string}\n */\nfunction imageReferencePeek() {\n  return '!'\n}\n", "/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {InlineCode, Parents} from 'mdast'\n */\n\ninlineCode.peek = inlineCodePeek\n\n/**\n * @param {InlineCode} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @returns {string}\n */\nexport function inlineCode(node, _, state) {\n  let value = node.value || ''\n  let sequence = '`'\n  let index = -1\n\n  // If there is a single grave accent on its own in the code, use a fence of\n  // two.\n  // If there are two in a row, use one.\n  while (new RegExp('(^|[^`])' + sequence + '([^`]|$)').test(value)) {\n    sequence += '`'\n  }\n\n  // If this is not just spaces or eols (tabs don’t count), and either the\n  // first or last character are a space, eol, or tick, then pad with spaces.\n  if (\n    /[^ \\r\\n]/.test(value) &&\n    ((/^[ \\r\\n]/.test(value) && /[ \\r\\n]$/.test(value)) || /^`|`$/.test(value))\n  ) {\n    value = ' ' + value + ' '\n  }\n\n  // We have a potential problem: certain characters after eols could result in\n  // blocks being seen.\n  // For example, if someone injected the string `'\\n# b'`, then that would\n  // result in an ATX heading.\n  // We can’t escape characters in `inlineCode`, but because eols are\n  // transformed to spaces when going from markdown to HTML anyway, we can swap\n  // them out.\n  while (++index < state.unsafe.length) {\n    const pattern = state.unsafe[index]\n    const expression = state.compilePattern(pattern)\n    /** @type {RegExpExecArray | null} */\n    let match\n\n    // Only look for `atBreak`s.\n    // Btw: note that `atBreak` patterns will always start the regex at LF or\n    // CR.\n    if (!pattern.atBreak) continue\n\n    while ((match = expression.exec(value))) {\n      let position = match.index\n\n      // Support CRLF (patterns only look for one of the characters).\n      if (\n        value.charCodeAt(position) === 10 /* `\\n` */ &&\n        value.charCodeAt(position - 1) === 13 /* `\\r` */\n      ) {\n        position--\n      }\n\n      value = value.slice(0, position) + ' ' + value.slice(match.index + 1)\n    }\n  }\n\n  return sequence + value + sequence\n}\n\n/**\n * @returns {string}\n */\nfunction inlineCodePeek() {\n  return '`'\n}\n", "/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Link} from 'mdast'\n */\n\nimport {toString} from 'mdast-util-to-string'\n\n/**\n * @param {Link} node\n * @param {State} state\n * @returns {boolean}\n */\nexport function formatLinkAsAutolink(node, state) {\n  const raw = toString(node)\n\n  return Boolean(\n    !state.options.resourceLink &&\n      // If there’s a url…\n      node.url &&\n      // And there’s a no title…\n      !node.title &&\n      // And the content of `node` is a single text node…\n      node.children &&\n      node.children.length === 1 &&\n      node.children[0].type === 'text' &&\n      // And if the url is the same as the content…\n      (raw === node.url || 'mailto:' + raw === node.url) &&\n      // And that starts w/ a protocol…\n      /^[a-z][a-z+.-]+:/i.test(node.url) &&\n      // And that doesn’t contain ASCII control codes (character escapes and\n      // references don’t work), space, or angle brackets…\n      !/[\\0- <>\\u007F]/.test(node.url)\n  )\n}\n", "/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Link, Parents} from 'mdast'\n * @import {Exit} from '../types.js'\n */\n\nimport {checkQuote} from '../util/check-quote.js'\nimport {formatLinkAsAutolink} from '../util/format-link-as-autolink.js'\n\nlink.peek = linkPeek\n\n/**\n * @param {Link} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function link(node, _, state, info) {\n  const quote = checkQuote(state)\n  const suffix = quote === '\"' ? 'Quote' : 'Apostrophe'\n  const tracker = state.createTracker(info)\n  /** @type {Exit} */\n  let exit\n  /** @type {Exit} */\n  let subexit\n\n  if (formatLinkAsAutolink(node, state)) {\n    // Hide the fact that we’re in phrasing, because escapes don’t work.\n    const stack = state.stack\n    state.stack = []\n    exit = state.enter('autolink')\n    let value = tracker.move('<')\n    value += tracker.move(\n      state.containerPhrasing(node, {\n        before: value,\n        after: '>',\n        ...tracker.current()\n      })\n    )\n    value += tracker.move('>')\n    exit()\n    state.stack = stack\n    return value\n  }\n\n  exit = state.enter('link')\n  subexit = state.enter('label')\n  let value = tracker.move('[')\n  value += tracker.move(\n    state.containerPhrasing(node, {\n      before: value,\n      after: '](',\n      ...tracker.current()\n    })\n  )\n  value += tracker.move('](')\n  subexit()\n\n  if (\n    // If there’s no url but there is a title…\n    (!node.url && node.title) ||\n    // If there are control characters or whitespace.\n    /[\\0- \\u007F]/.test(node.url)\n  ) {\n    subexit = state.enter('destinationLiteral')\n    value += tracker.move('<')\n    value += tracker.move(\n      state.safe(node.url, {before: value, after: '>', ...tracker.current()})\n    )\n    value += tracker.move('>')\n  } else {\n    // No whitespace, raw is prettier.\n    subexit = state.enter('destinationRaw')\n    value += tracker.move(\n      state.safe(node.url, {\n        before: value,\n        after: node.title ? ' ' : ')',\n        ...tracker.current()\n      })\n    )\n  }\n\n  subexit()\n\n  if (node.title) {\n    subexit = state.enter(`title${suffix}`)\n    value += tracker.move(' ' + quote)\n    value += tracker.move(\n      state.safe(node.title, {\n        before: value,\n        after: quote,\n        ...tracker.current()\n      })\n    )\n    value += tracker.move(quote)\n    subexit()\n  }\n\n  value += tracker.move(')')\n\n  exit()\n  return value\n}\n\n/**\n * @param {Link} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @returns {string}\n */\nfunction linkPeek(node, _, state) {\n  return formatLinkAsAutolink(node, state) ? '<' : '['\n}\n", "/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {LinkReference, Parents} from 'mdast'\n */\n\nlinkReference.peek = linkReferencePeek\n\n/**\n * @param {LinkReference} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function linkReference(node, _, state, info) {\n  const type = node.referenceType\n  const exit = state.enter('linkReference')\n  let subexit = state.enter('label')\n  const tracker = state.createTracker(info)\n  let value = tracker.move('[')\n  const text = state.containerPhrasing(node, {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  value += tracker.move(text + '][')\n\n  subexit()\n  // Hide the fact that we’re in phrasing, because escapes don’t work.\n  const stack = state.stack\n  state.stack = []\n  subexit = state.enter('reference')\n  // Note: for proper tracking, we should reset the output positions when we end\n  // up making a `shortcut` reference, because then there is no brace output.\n  // Practically, in that case, there is no content, so it doesn’t matter that\n  // we’ve tracked one too many characters.\n  const reference = state.safe(state.associationId(node), {\n    before: value,\n    after: ']',\n    ...tracker.current()\n  })\n  subexit()\n  state.stack = stack\n  exit()\n\n  if (type === 'full' || !text || text !== reference) {\n    value += tracker.move(reference + ']')\n  } else if (type === 'shortcut') {\n    // Remove the unwanted `[`.\n    value = value.slice(0, -1)\n  } else {\n    value += tracker.move(']')\n  }\n\n  return value\n}\n\n/**\n * @returns {string}\n */\nfunction linkReferencePeek() {\n  return '['\n}\n", "/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['bullet'], null | undefined>}\n */\nexport function checkBullet(state) {\n  const marker = state.options.bullet || '*'\n\n  if (marker !== '*' && marker !== '+' && marker !== '-') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        marker +\n        '` for `options.bullet`, expected `*`, `+`, or `-`'\n    )\n  }\n\n  return marker\n}\n", "/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\nimport {checkBullet} from './check-bullet.js'\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['bullet'], null | undefined>}\n */\nexport function checkBulletOther(state) {\n  const bullet = checkBullet(state)\n  const bulletOther = state.options.bulletOther\n\n  if (!bulletOther) {\n    return bullet === '*' ? '-' : '*'\n  }\n\n  if (bulletOther !== '*' && bulletOther !== '+' && bulletOther !== '-') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        bulletOther +\n        '` for `options.bulletOther`, expected `*`, `+`, or `-`'\n    )\n  }\n\n  if (bulletOther === bullet) {\n    throw new Error(\n      'Expected `bullet` (`' +\n        bullet +\n        '`) and `bulletOther` (`' +\n        bulletOther +\n        '`) to be different'\n    )\n  }\n\n  return bulletOther\n}\n", "/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['bulletOrdered'], null | undefined>}\n */\nexport function checkBulletOrdered(state) {\n  const marker = state.options.bulletOrdered || '.'\n\n  if (marker !== '.' && marker !== ')') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        marker +\n        '` for `options.bulletOrdered`, expected `.` or `)`'\n    )\n  }\n\n  return marker\n}\n", "/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['rule'], null | undefined>}\n */\nexport function checkRule(state) {\n  const marker = state.options.rule || '*'\n\n  if (marker !== '*' && marker !== '-' && marker !== '_') {\n    throw new Error(\n      'Cannot serialize rules with `' +\n        marker +\n        '` for `options.rule`, expected `*`, `-`, or `_`'\n    )\n  }\n\n  return marker\n}\n", "/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {List, Parents} from 'mdast'\n */\n\nimport {checkBullet} from '../util/check-bullet.js'\nimport {checkBulletOther} from '../util/check-bullet-other.js'\nimport {checkBulletOrdered} from '../util/check-bullet-ordered.js'\nimport {checkRule} from '../util/check-rule.js'\n\n/**\n * @param {List} node\n * @param {Parents | undefined} parent\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function list(node, parent, state, info) {\n  const exit = state.enter('list')\n  const bulletCurrent = state.bulletCurrent\n  /** @type {string} */\n  let bullet = node.ordered ? checkBulletOrdered(state) : checkBullet(state)\n  /** @type {string} */\n  const bulletOther = node.ordered\n    ? bullet === '.'\n      ? ')'\n      : '.'\n    : checkBulletOther(state)\n  let useDifferentMarker =\n    parent && state.bulletLastUsed ? bullet === state.bulletLastUsed : false\n\n  if (!node.ordered) {\n    const firstListItem = node.children ? node.children[0] : undefined\n\n    // If there’s an empty first list item directly in two list items,\n    // we have to use a different bullet:\n    //\n    // ```markdown\n    // * - *\n    // ```\n    //\n    // …because otherwise it would become one big thematic break.\n    if (\n      // Bullet could be used as a thematic break marker:\n      (bullet === '*' || bullet === '-') &&\n      // Empty first list item:\n      firstListItem &&\n      (!firstListItem.children || !firstListItem.children[0]) &&\n      // Directly in two other list items:\n      state.stack[state.stack.length - 1] === 'list' &&\n      state.stack[state.stack.length - 2] === 'listItem' &&\n      state.stack[state.stack.length - 3] === 'list' &&\n      state.stack[state.stack.length - 4] === 'listItem' &&\n      // That are each the first child.\n      state.indexStack[state.indexStack.length - 1] === 0 &&\n      state.indexStack[state.indexStack.length - 2] === 0 &&\n      state.indexStack[state.indexStack.length - 3] === 0\n    ) {\n      useDifferentMarker = true\n    }\n\n    // If there’s a thematic break at the start of the first list item,\n    // we have to use a different bullet:\n    //\n    // ```markdown\n    // * ---\n    // ```\n    //\n    // …because otherwise it would become one big thematic break.\n    if (checkRule(state) === bullet && firstListItem) {\n      let index = -1\n\n      while (++index < node.children.length) {\n        const item = node.children[index]\n\n        if (\n          item &&\n          item.type === 'listItem' &&\n          item.children &&\n          item.children[0] &&\n          item.children[0].type === 'thematicBreak'\n        ) {\n          useDifferentMarker = true\n          break\n        }\n      }\n    }\n  }\n\n  if (useDifferentMarker) {\n    bullet = bulletOther\n  }\n\n  state.bulletCurrent = bullet\n  const value = state.containerFlow(node, info)\n  state.bulletLastUsed = bullet\n  state.bulletCurrent = bulletCurrent\n  exit()\n  return value\n}\n", "/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['listItemIndent'], null | undefined>}\n */\nexport function checkListItemIndent(state) {\n  const style = state.options.listItemIndent || 'one'\n\n  if (style !== 'tab' && style !== 'one' && style !== 'mixed') {\n    throw new Error(\n      'Cannot serialize items with `' +\n        style +\n        '` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`'\n    )\n  }\n\n  return style\n}\n", "/**\n * @import {Info, Map, State} from 'mdast-util-to-markdown'\n * @import {ListItem, Parents} from 'mdast'\n */\n\nimport {checkBullet} from '../util/check-bullet.js'\nimport {checkListItemIndent} from '../util/check-list-item-indent.js'\n\n/**\n * @param {ListItem} node\n * @param {Parents | undefined} parent\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function listItem(node, parent, state, info) {\n  const listItemIndent = checkListItemIndent(state)\n  let bullet = state.bulletCurrent || checkBullet(state)\n\n  // Add the marker value for ordered lists.\n  if (parent && parent.type === 'list' && parent.ordered) {\n    bullet =\n      (typeof parent.start === 'number' && parent.start > -1\n        ? parent.start\n        : 1) +\n      (state.options.incrementListMarker === false\n        ? 0\n        : parent.children.indexOf(node)) +\n      bullet\n  }\n\n  let size = bullet.length + 1\n\n  if (\n    listItemIndent === 'tab' ||\n    (listItemIndent === 'mixed' &&\n      ((parent && parent.type === 'list' && parent.spread) || node.spread))\n  ) {\n    size = Math.ceil(size / 4) * 4\n  }\n\n  const tracker = state.createTracker(info)\n  tracker.move(bullet + ' '.repeat(size - bullet.length))\n  tracker.shift(size)\n  const exit = state.enter('listItem')\n  const value = state.indentLines(\n    state.containerFlow(node, tracker.current()),\n    map\n  )\n  exit()\n\n  return value\n\n  /** @type {Map} */\n  function map(line, index, blank) {\n    if (index) {\n      return (blank ? '' : ' '.repeat(size)) + line\n    }\n\n    return (blank ? bullet : bullet + ' '.repeat(size - bullet.length)) + line\n  }\n}\n", "/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Paragraph, Parents} from 'mdast'\n */\n\n/**\n * @param {Paragraph} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function paragraph(node, _, state, info) {\n  const exit = state.enter('paragraph')\n  const subexit = state.enter('phrasing')\n  const value = state.containerPhrasing(node, info)\n  subexit()\n  exit()\n  return value\n}\n", "/**\n * @typedef {import('mdast').Html} Html\n * @typedef {import('mdast').PhrasingContent} PhrasingContent\n */\n\nimport {convert} from 'unist-util-is'\n\n/**\n * Check if the given value is *phrasing content*.\n *\n * > 👉 **Note**: Excludes `html`, which can be both phrasing or flow.\n *\n * @param node\n *   Thing to check, typically `Node`.\n * @returns\n *   Whether `value` is phrasing content.\n */\n\nexport const phrasing =\n  /** @type {(node?: unknown) => node is Exclude<PhrasingContent, Html>} */\n  (\n    convert([\n      'break',\n      'delete',\n      'emphasis',\n      // To do: next major: removed since footnotes were added to GFM.\n      'footnote',\n      'footnoteReference',\n      'image',\n      'imageReference',\n      'inlineCode',\n      // Enabled by `mdast-util-math`:\n      'inlineMath',\n      'link',\n      'linkReference',\n      // Enabled by `mdast-util-mdx`:\n      'mdxJsxTextElement',\n      // Enabled by `mdast-util-mdx`:\n      'mdxTextExpression',\n      'strong',\n      'text',\n      // Enabled by `mdast-util-directive`:\n      'textDirective'\n    ])\n  )\n", "/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Parents, Root} from 'mdast'\n */\n\nimport {phrasing} from 'mdast-util-phrasing'\n\n/**\n * @param {Root} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function root(node, _, state, info) {\n  // Note: `html` nodes are ambiguous.\n  const hasPhrasing = node.children.some(function (d) {\n    return phrasing(d)\n  })\n\n  const container = hasPhrasing ? state.containerPhrasing : state.containerFlow\n  return container.call(state, node, info)\n}\n", "/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['strong'], null | undefined>}\n */\nexport function checkStrong(state) {\n  const marker = state.options.strong || '*'\n\n  if (marker !== '*' && marker !== '_') {\n    throw new Error(\n      'Cannot serialize strong with `' +\n        marker +\n        '` for `options.strong`, expected `*`, or `_`'\n    )\n  }\n\n  return marker\n}\n", "/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Parents, Strong} from 'mdast'\n */\n\nimport {checkStrong} from '../util/check-strong.js'\nimport {encodeCharacterReference} from '../util/encode-character-reference.js'\nimport {encodeInfo} from '../util/encode-info.js'\n\nstrong.peek = strongPeek\n\n/**\n * @param {Strong} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function strong(node, _, state, info) {\n  const marker = checkStrong(state)\n  const exit = state.enter('strong')\n  const tracker = state.createTracker(info)\n  const before = tracker.move(marker + marker)\n\n  let between = tracker.move(\n    state.containerPhrasing(node, {\n      after: marker,\n      before,\n      ...tracker.current()\n    })\n  )\n  const betweenHead = between.charCodeAt(0)\n  const open = encodeInfo(\n    info.before.charCodeAt(info.before.length - 1),\n    betweenHead,\n    marker\n  )\n\n  if (open.inside) {\n    between = encodeCharacterReference(betweenHead) + between.slice(1)\n  }\n\n  const betweenTail = between.charCodeAt(between.length - 1)\n  const close = encodeInfo(info.after.charCodeAt(0), betweenTail, marker)\n\n  if (close.inside) {\n    between = between.slice(0, -1) + encodeCharacterReference(betweenTail)\n  }\n\n  const after = tracker.move(marker + marker)\n\n  exit()\n\n  state.attentionEncodeSurroundingInfo = {\n    after: close.outside,\n    before: open.outside\n  }\n  return before + between + after\n}\n\n/**\n * @param {Strong} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @returns {string}\n */\nfunction strongPeek(_, _1, state) {\n  return state.options.strong || '*'\n}\n", "/**\n * @import {Info, State} from 'mdast-util-to-markdown'\n * @import {Parents, Text} from 'mdast'\n */\n\n/**\n * @param {Text} node\n * @param {Parents | undefined} _\n * @param {State} state\n * @param {Info} info\n * @returns {string}\n */\nexport function text(node, _, state, info) {\n  return state.safe(node.value, info)\n}\n", "/**\n * @import {Options, State} from 'mdast-util-to-markdown'\n */\n\n/**\n * @param {State} state\n * @returns {Exclude<Options['ruleRepetition'], null | undefined>}\n */\nexport function checkRuleRepetition(state) {\n  const repetition = state.options.ruleRepetition || 3\n\n  if (repetition < 3) {\n    throw new Error(\n      'Cannot serialize rules with repetition `' +\n        repetition +\n        '` for `options.ruleRepetition`, expected `3` or more'\n    )\n  }\n\n  return repetition\n}\n", "/**\n * @import {State} from 'mdast-util-to-markdown'\n * @import {Parents, ThematicBreak} from 'mdast'\n */\n\nimport {checkRuleRepetition} from '../util/check-rule-repetition.js'\nimport {checkRule} from '../util/check-rule.js'\n\n/**\n * @param {ThematicBreak} _\n * @param {Parents | undefined} _1\n * @param {State} state\n * @returns {string}\n */\nexport function thematicBreak(_, _1, state) {\n  const value = (\n    checkRule(state) + (state.options.ruleSpaces ? ' ' : '')\n  ).repeat(checkRuleRepetition(state))\n\n  return state.options.ruleSpaces ? value.slice(0, -1) : value\n}\n", "import {blockquote} from './blockquote.js'\nimport {hardBreak} from './break.js'\nimport {code} from './code.js'\nimport {definition} from './definition.js'\nimport {emphasis} from './emphasis.js'\nimport {heading} from './heading.js'\nimport {html} from './html.js'\nimport {image} from './image.js'\nimport {imageReference} from './image-reference.js'\nimport {inlineCode} from './inline-code.js'\nimport {link} from './link.js'\nimport {linkReference} from './link-reference.js'\nimport {list} from './list.js'\nimport {listItem} from './list-item.js'\nimport {paragraph} from './paragraph.js'\nimport {root} from './root.js'\nimport {strong} from './strong.js'\nimport {text} from './text.js'\nimport {thematicBreak} from './thematic-break.js'\n\n/**\n * Default (CommonMark) handlers.\n */\nexport const handle = {\n  blockquote,\n  break: hardBreak,\n  code,\n  definition,\n  emphasis,\n  hardBreak,\n  heading,\n  html,\n  image,\n  imageReference,\n  inlineCode,\n  link,\n  linkReference,\n  list,\n  listItem,\n  paragraph,\n  root,\n  strong,\n  text,\n  thematicBreak\n}\n", "/**\n * @typedef {import('mdast').InlineCode} InlineCode\n * @typedef {import('mdast').Table} Table\n * @typedef {import('mdast').TableCell} TableCell\n * @typedef {import('mdast').TableRow} TableRow\n *\n * @typedef {import('markdown-table').Options} MarkdownTableOptions\n *\n * @typedef {import('mdast-util-from-markdown').CompileContext} CompileContext\n * @typedef {import('mdast-util-from-markdown').Extension} FromMarkdownExtension\n * @typedef {import('mdast-util-from-markdown').Handle} FromMarkdownHandle\n *\n * @typedef {import('mdast-util-to-markdown').Options} ToMarkdownExtension\n * @typedef {import('mdast-util-to-markdown').Handle} ToMarkdownHandle\n * @typedef {import('mdast-util-to-markdown').State} State\n * @typedef {import('mdast-util-to-markdown').Info} Info\n */\n\n/**\n * @typedef Options\n *   Configuration.\n * @property {boolean | null | undefined} [tableCellPadding=true]\n *   Whether to add a space of padding between delimiters and cells (default:\n *   `true`).\n * @property {boolean | null | undefined} [tablePipeAlign=true]\n *   Whether to align the delimiters (default: `true`).\n * @property {MarkdownTableOptions['stringLength'] | null | undefined} [stringLength]\n *   Function to detect the length of table cell content, used when aligning\n *   the delimiters between cells (optional).\n */\n\nimport {ok as assert} from 'devlop'\nimport {markdownTable} from 'markdown-table'\nimport {defaultHandlers} from 'mdast-util-to-markdown'\n\n/**\n * Create an extension for `mdast-util-from-markdown` to enable GFM tables in\n * markdown.\n *\n * @returns {FromMarkdownExtension}\n *   Extension for `mdast-util-from-markdown` to enable GFM tables.\n */\nexport function gfmTableFromMarkdown() {\n  return {\n    enter: {\n      table: enterTable,\n      tableData: enterCell,\n      tableHeader: enterCell,\n      tableRow: enterRow\n    },\n    exit: {\n      codeText: exitCodeText,\n      table: exitTable,\n      tableData: exit,\n      tableHeader: exit,\n      tableRow: exit\n    }\n  }\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterTable(token) {\n  const align = token._align\n  assert(align, 'expected `_align` on table')\n  this.enter(\n    {\n      type: 'table',\n      align: align.map(function (d) {\n        return d === 'none' ? null : d\n      }),\n      children: []\n    },\n    token\n  )\n  this.data.inTable = true\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitTable(token) {\n  this.exit(token)\n  this.data.inTable = undefined\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterRow(token) {\n  this.enter({type: 'tableRow', children: []}, token)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exit(token) {\n  this.exit(token)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterCell(token) {\n  this.enter({type: 'tableCell', children: []}, token)\n}\n\n// Overwrite the default code text data handler to unescape escaped pipes when\n// they are in tables.\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitCodeText(token) {\n  let value = this.resume()\n\n  if (this.data.inTable) {\n    value = value.replace(/\\\\([\\\\|])/g, replace)\n  }\n\n  const node = this.stack[this.stack.length - 1]\n  assert(node.type === 'inlineCode')\n  node.value = value\n  this.exit(token)\n}\n\n/**\n * @param {string} $0\n * @param {string} $1\n * @returns {string}\n */\nfunction replace($0, $1) {\n  // Pipes work, backslashes don’t (but can’t escape pipes).\n  return $1 === '|' ? $1 : $0\n}\n\n/**\n * Create an extension for `mdast-util-to-markdown` to enable GFM tables in\n * markdown.\n *\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {ToMarkdownExtension}\n *   Extension for `mdast-util-to-markdown` to enable GFM tables.\n */\nexport function gfmTableToMarkdown(options) {\n  const settings = options || {}\n  const padding = settings.tableCellPadding\n  const alignDelimiters = settings.tablePipeAlign\n  const stringLength = settings.stringLength\n  const around = padding ? ' ' : '|'\n\n  return {\n    unsafe: [\n      {character: '\\r', inConstruct: 'tableCell'},\n      {character: '\\n', inConstruct: 'tableCell'},\n      // A pipe, when followed by a tab or space (padding), or a dash or colon\n      // (unpadded delimiter row), could result in a table.\n      {atBreak: true, character: '|', after: '[\\t :-]'},\n      // A pipe in a cell must be encoded.\n      {character: '|', inConstruct: 'tableCell'},\n      // A colon must be followed by a dash, in which case it could start a\n      // delimiter row.\n      {atBreak: true, character: ':', after: '-'},\n      // A delimiter row can also start with a dash, when followed by more\n      // dashes, a colon, or a pipe.\n      // This is a stricter version than the built in check for lists, thematic\n      // breaks, and setex heading underlines though:\n      // <https://github.com/syntax-tree/mdast-util-to-markdown/blob/51a2038/lib/unsafe.js#L57>\n      {atBreak: true, character: '-', after: '[:|-]'}\n    ],\n    handlers: {\n      inlineCode: inlineCodeWithTable,\n      table: handleTable,\n      tableCell: handleTableCell,\n      tableRow: handleTableRow\n    }\n  }\n\n  /**\n   * @type {ToMarkdownHandle}\n   * @param {Table} node\n   */\n  function handleTable(node, _, state, info) {\n    return serializeData(handleTableAsData(node, state, info), node.align)\n  }\n\n  /**\n   * This function isn’t really used normally, because we handle rows at the\n   * table level.\n   * But, if someone passes in a table row, this ensures we make somewhat sense.\n   *\n   * @type {ToMarkdownHandle}\n   * @param {TableRow} node\n   */\n  function handleTableRow(node, _, state, info) {\n    const row = handleTableRowAsData(node, state, info)\n    const value = serializeData([row])\n    // `markdown-table` will always add an align row\n    return value.slice(0, value.indexOf('\\n'))\n  }\n\n  /**\n   * @type {ToMarkdownHandle}\n   * @param {TableCell} node\n   */\n  function handleTableCell(node, _, state, info) {\n    const exit = state.enter('tableCell')\n    const subexit = state.enter('phrasing')\n    const value = state.containerPhrasing(node, {\n      ...info,\n      before: around,\n      after: around\n    })\n    subexit()\n    exit()\n    return value\n  }\n\n  /**\n   * @param {Array<Array<string>>} matrix\n   * @param {Array<string | null | undefined> | null | undefined} [align]\n   */\n  function serializeData(matrix, align) {\n    return markdownTable(matrix, {\n      align,\n      // @ts-expect-error: `markdown-table` types should support `null`.\n      alignDelimiters,\n      // @ts-expect-error: `markdown-table` types should support `null`.\n      padding,\n      // @ts-expect-error: `markdown-table` types should support `null`.\n      stringLength\n    })\n  }\n\n  /**\n   * @param {Table} node\n   * @param {State} state\n   * @param {Info} info\n   */\n  function handleTableAsData(node, state, info) {\n    const children = node.children\n    let index = -1\n    /** @type {Array<Array<string>>} */\n    const result = []\n    const subexit = state.enter('table')\n\n    while (++index < children.length) {\n      result[index] = handleTableRowAsData(children[index], state, info)\n    }\n\n    subexit()\n\n    return result\n  }\n\n  /**\n   * @param {TableRow} node\n   * @param {State} state\n   * @param {Info} info\n   */\n  function handleTableRowAsData(node, state, info) {\n    const children = node.children\n    let index = -1\n    /** @type {Array<string>} */\n    const result = []\n    const subexit = state.enter('tableRow')\n\n    while (++index < children.length) {\n      // Note: the positional info as used here is incorrect.\n      // Making it correct would be impossible due to aligning cells?\n      // And it would need copy/pasting `markdown-table` into this project.\n      result[index] = handleTableCell(children[index], node, state, info)\n    }\n\n    subexit()\n\n    return result\n  }\n\n  /**\n   * @type {ToMarkdownHandle}\n   * @param {InlineCode} node\n   */\n  function inlineCodeWithTable(node, parent, state) {\n    let value = defaultHandlers.inlineCode(node, parent, state)\n\n    if (state.stack.includes('tableCell')) {\n      value = value.replace(/\\|/g, '\\\\$&')\n    }\n\n    return value\n  }\n}\n", "/**\n * @typedef {import('mdast').ListItem} ListItem\n * @typedef {import('mdast').Paragraph} Paragraph\n * @typedef {import('mdast-util-from-markdown').CompileContext} CompileContext\n * @typedef {import('mdast-util-from-markdown').Extension} FromMarkdownExtension\n * @typedef {import('mdast-util-from-markdown').Handle} FromMarkdownHandle\n * @typedef {import('mdast-util-to-markdown').Options} ToMarkdownExtension\n * @typedef {import('mdast-util-to-markdown').Handle} ToMarkdownHandle\n */\n\nimport {ok as assert} from 'devlop'\nimport {defaultHandlers} from 'mdast-util-to-markdown'\n\n/**\n * Create an extension for `mdast-util-from-markdown` to enable GFM task\n * list items in markdown.\n *\n * @returns {FromMarkdownExtension}\n *   Extension for `mdast-util-from-markdown` to enable GFM task list items.\n */\nexport function gfmTaskListItemFromMarkdown() {\n  return {\n    exit: {\n      taskListCheckValueChecked: exitCheck,\n      taskListCheckValueUnchecked: exitCheck,\n      paragraph: exitParagraphWithTaskListItem\n    }\n  }\n}\n\n/**\n * Create an extension for `mdast-util-to-markdown` to enable GFM task list\n * items in markdown.\n *\n * @returns {ToMarkdownExtension}\n *   Extension for `mdast-util-to-markdown` to enable GFM task list items.\n */\nexport function gfmTaskListItemToMarkdown() {\n  return {\n    unsafe: [{atBreak: true, character: '-', after: '[:|-]'}],\n    handlers: {listItem: listItemWithTaskListItem}\n  }\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitCheck(token) {\n  // We’re always in a paragraph, in a list item.\n  const node = this.stack[this.stack.length - 2]\n  assert(node.type === 'listItem')\n  node.checked = token.type === 'taskListCheckValueChecked'\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitParagraphWithTaskListItem(token) {\n  const parent = this.stack[this.stack.length - 2]\n\n  if (\n    parent &&\n    parent.type === 'listItem' &&\n    typeof parent.checked === 'boolean'\n  ) {\n    const node = this.stack[this.stack.length - 1]\n    assert(node.type === 'paragraph')\n    const head = node.children[0]\n\n    if (head && head.type === 'text') {\n      const siblings = parent.children\n      let index = -1\n      /** @type {Paragraph | undefined} */\n      let firstParaghraph\n\n      while (++index < siblings.length) {\n        const sibling = siblings[index]\n        if (sibling.type === 'paragraph') {\n          firstParaghraph = sibling\n          break\n        }\n      }\n\n      if (firstParaghraph === node) {\n        // Must start with a space or a tab.\n        head.value = head.value.slice(1)\n\n        if (head.value.length === 0) {\n          node.children.shift()\n        } else if (\n          node.position &&\n          head.position &&\n          typeof head.position.start.offset === 'number'\n        ) {\n          head.position.start.column++\n          head.position.start.offset++\n          node.position.start = Object.assign({}, head.position.start)\n        }\n      }\n    }\n  }\n\n  this.exit(token)\n}\n\n/**\n * @type {ToMarkdownHandle}\n * @param {ListItem} node\n */\nfunction listItemWithTaskListItem(node, parent, state, info) {\n  const head = node.children[0]\n  const checkable =\n    typeof node.checked === 'boolean' && head && head.type === 'paragraph'\n  const checkbox = '[' + (node.checked ? 'x' : ' ') + '] '\n  const tracker = state.createTracker(info)\n\n  if (checkable) {\n    tracker.move(checkbox)\n  }\n\n  let value = defaultHandlers.listItem(node, parent, state, {\n    ...info,\n    ...tracker.current()\n  })\n\n  if (checkable) {\n    value = value.replace(/^(?:[*+-]|\\d+\\.)([\\r\\n]| {1,3})/, check)\n  }\n\n  return value\n\n  /**\n   * @param {string} $0\n   * @returns {string}\n   */\n  function check($0) {\n    return $0 + checkbox\n  }\n}\n", "/**\n * @import {Extension as FromMarkdownExtension} from 'mdast-util-from-markdown'\n * @import {Options} from 'mdast-util-gfm'\n * @import {Options as ToMarkdownExtension} from 'mdast-util-to-markdown'\n */\n\nimport {\n  gfmAutolinkLiteralFromMarkdown,\n  gfmAutolinkLiteralToMarkdown\n} from 'mdast-util-gfm-autolink-literal'\nimport {\n  gfmFootnoteFromMarkdown,\n  gfmFootnoteToMarkdown\n} from 'mdast-util-gfm-footnote'\nimport {\n  gfmStrikethroughFromMarkdown,\n  gfmStrikethroughToMarkdown\n} from 'mdast-util-gfm-strikethrough'\nimport {gfmTableFromMarkdown, gfmTableToMarkdown} from 'mdast-util-gfm-table'\nimport {\n  gfmTaskListItemFromMarkdown,\n  gfmTaskListItemToMarkdown\n} from 'mdast-util-gfm-task-list-item'\n\n/**\n * Create an extension for `mdast-util-from-markdown` to enable GFM (autolink\n * literals, footnotes, strikethrough, tables, tasklists).\n *\n * @returns {Array<FromMarkdownExtension>}\n *   Extension for `mdast-util-from-markdown` to enable GFM (autolink literals,\n *   footnotes, strikethrough, tables, tasklists).\n */\nexport function gfmFromMarkdown() {\n  return [\n    gfmAutolinkLiteralFromMarkdown(),\n    gfmFootnoteFromMarkdown(),\n    gfmStrikethroughFromMarkdown(),\n    gfmTableFromMarkdown(),\n    gfmTaskListItemFromMarkdown()\n  ]\n}\n\n/**\n * Create an extension for `mdast-util-to-markdown` to enable GFM (autolink\n * literals, footnotes, strikethrough, tables, tasklists).\n *\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {ToMarkdownExtension}\n *   Extension for `mdast-util-to-markdown` to enable GFM (autolink literals,\n *   footnotes, strikethrough, tables, tasklists).\n */\nexport function gfmToMarkdown(options) {\n  return {\n    extensions: [\n      gfmAutolinkLiteralToMarkdown(),\n      gfmFootnoteToMarkdown(options),\n      gfmStrikethroughToMarkdown(),\n      gfmTableToMarkdown(options),\n      gfmTaskListItemToMarkdown()\n    ]\n  }\n}\n", "/**\n * @import {Code, ConstructRecord, Event, Extension, Previous, State, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\nimport {\n  asciiAlpha,\n  asciiAlphanumeric,\n  asciiControl,\n  markdownLineEndingOrSpace,\n  unicodePunctuation,\n  unicodeWhitespace\n} from 'micromark-util-character'\nimport {codes} from 'micromark-util-symbol'\n\nconst wwwPrefix = {tokenize: tokenizeWwwPrefix, partial: true}\nconst domain = {tokenize: tokenizeDomain, partial: true}\nconst path = {tokenize: tokenizePath, partial: true}\nconst trail = {tokenize: tokenizeTrail, partial: true}\nconst emailDomainDotTrail = {\n  tokenize: tokenizeEmailDomainDotTrail,\n  partial: true\n}\n\nconst wwwAutolink = {\n  name: 'wwwAutolink',\n  tokenize: tokenizeWwwAutolink,\n  previous: previousWww\n}\n\nconst protocolAutolink = {\n  name: 'protocolAutolink',\n  tokenize: tokenizeProtocolAutolink,\n  previous: previousProtocol\n}\n\nconst emailAutolink = {\n  name: 'emailAutolink',\n  tokenize: tokenizeEmailAutolink,\n  previous: previousEmail\n}\n\n/** @type {ConstructRecord} */\nconst text = {}\n\n/**\n * Create an extension for `micromark` to support GitHub autolink literal\n * syntax.\n *\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions` to enable GFM\n *   autolink literal syntax.\n */\nexport function gfmAutolinkLiteral() {\n  return {text}\n}\n\n/** @type {Code} */\nlet code = codes.digit0\n\n// Add alphanumerics.\nwhile (code < codes.leftCurlyBrace) {\n  text[code] = emailAutolink\n  code++\n  if (code === codes.colon) code = codes.uppercaseA\n  else if (code === codes.leftSquareBracket) code = codes.lowercaseA\n}\n\ntext[codes.plusSign] = emailAutolink\ntext[codes.dash] = emailAutolink\ntext[codes.dot] = emailAutolink\ntext[codes.underscore] = emailAutolink\ntext[codes.uppercaseH] = [emailAutolink, protocolAutolink]\ntext[codes.lowercaseH] = [emailAutolink, protocolAutolink]\ntext[codes.uppercaseW] = [emailAutolink, wwwAutolink]\ntext[codes.lowercaseW] = [emailAutolink, wwwAutolink]\n\n// To do: perform email autolink literals on events, afterwards.\n// That’s where `markdown-rs` and `cmark-gfm` perform it.\n// It should look for `@`, then for atext backwards, and then for a label\n// forwards.\n// To do: `mailto:`, `xmpp:` protocol as prefix.\n\n/**\n * Email autolink literal.\n *\n * ```markdown\n * > | a <EMAIL> b\n *       ^^^^^^^^^^^^^^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeEmailAutolink(effects, ok, nok) {\n  const self = this\n  /** @type {boolean | undefined} */\n  let dot\n  /** @type {boolean} */\n  let data\n\n  return start\n\n  /**\n   * Start of email autolink literal.\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (\n      !gfmAtext(code) ||\n      !previousEmail.call(self, self.previous) ||\n      previousUnbalanced(self.events)\n    ) {\n      return nok(code)\n    }\n\n    effects.enter('literalAutolink')\n    effects.enter('literalAutolinkEmail')\n    return atext(code)\n  }\n\n  /**\n   * In email atext.\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atext(code) {\n    if (gfmAtext(code)) {\n      effects.consume(code)\n      return atext\n    }\n\n    if (code === codes.atSign) {\n      effects.consume(code)\n      return emailDomain\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In email domain.\n   *\n   * The reference code is a bit overly complex as it handles the `@`, of which\n   * there may be just one.\n   * Source: <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L318>\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailDomain(code) {\n    // Dot followed by alphanumerical (not `-` or `_`).\n    if (code === codes.dot) {\n      return effects.check(\n        emailDomainDotTrail,\n        emailDomainAfter,\n        emailDomainDot\n      )(code)\n    }\n\n    // Alphanumerical, `-`, and `_`.\n    if (\n      code === codes.dash ||\n      code === codes.underscore ||\n      asciiAlphanumeric(code)\n    ) {\n      data = true\n      effects.consume(code)\n      return emailDomain\n    }\n\n    // To do: `/` if xmpp.\n\n    // Note: normally we’d truncate trailing punctuation from the link.\n    // However, email autolink literals cannot contain any of those markers,\n    // except for `.`, but that can only occur if it isn’t trailing.\n    // So we can ignore truncating!\n    return emailDomainAfter(code)\n  }\n\n  /**\n   * In email domain, on dot that is not a trail.\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *                      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailDomainDot(code) {\n    effects.consume(code)\n    dot = true\n    return emailDomain\n  }\n\n  /**\n   * After email domain.\n   *\n   * ```markdown\n   * > | a <EMAIL> b\n   *                          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailDomainAfter(code) {\n    // Domain must not be empty, must include a dot, and must end in alphabetical.\n    // Source: <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L332>.\n    if (data && dot && asciiAlpha(self.previous)) {\n      effects.exit('literalAutolinkEmail')\n      effects.exit('literalAutolink')\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n\n/**\n * `www` autolink literal.\n *\n * ```markdown\n * > | a www.example.org b\n *       ^^^^^^^^^^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeWwwAutolink(effects, ok, nok) {\n  const self = this\n\n  return wwwStart\n\n  /**\n   * Start of www autolink literal.\n   *\n   * ```markdown\n   * > | www.example.com/a?b#c\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function wwwStart(code) {\n    if (\n      (code !== codes.uppercaseW && code !== codes.lowercaseW) ||\n      !previousWww.call(self, self.previous) ||\n      previousUnbalanced(self.events)\n    ) {\n      return nok(code)\n    }\n\n    effects.enter('literalAutolink')\n    effects.enter('literalAutolinkWww')\n    // Note: we *check*, so we can discard the `www.` we parsed.\n    // If it worked, we consider it as a part of the domain.\n    return effects.check(\n      wwwPrefix,\n      effects.attempt(domain, effects.attempt(path, wwwAfter), nok),\n      nok\n    )(code)\n  }\n\n  /**\n   * After a www autolink literal.\n   *\n   * ```markdown\n   * > | www.example.com/a?b#c\n   *                          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function wwwAfter(code) {\n    effects.exit('literalAutolinkWww')\n    effects.exit('literalAutolink')\n    return ok(code)\n  }\n}\n\n/**\n * Protocol autolink literal.\n *\n * ```markdown\n * > | a https://example.org b\n *       ^^^^^^^^^^^^^^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeProtocolAutolink(effects, ok, nok) {\n  const self = this\n  let buffer = ''\n  let seen = false\n\n  return protocolStart\n\n  /**\n   * Start of protocol autolink literal.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function protocolStart(code) {\n    if (\n      (code === codes.uppercaseH || code === codes.lowercaseH) &&\n      previousProtocol.call(self, self.previous) &&\n      !previousUnbalanced(self.events)\n    ) {\n      effects.enter('literalAutolink')\n      effects.enter('literalAutolinkHttp')\n      buffer += String.fromCodePoint(code)\n      effects.consume(code)\n      return protocolPrefixInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In protocol.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *     ^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function protocolPrefixInside(code) {\n    // `5` is size of `https`\n    if (asciiAlpha(code) && buffer.length < 5) {\n      // @ts-expect-error: definitely number.\n      buffer += String.fromCodePoint(code)\n      effects.consume(code)\n      return protocolPrefixInside\n    }\n\n    if (code === codes.colon) {\n      const protocol = buffer.toLowerCase()\n\n      if (protocol === 'http' || protocol === 'https') {\n        effects.consume(code)\n        return protocolSlashesInside\n      }\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In slashes.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *           ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function protocolSlashesInside(code) {\n    if (code === codes.slash) {\n      effects.consume(code)\n\n      if (seen) {\n        return afterProtocol\n      }\n\n      seen = true\n      return protocolSlashesInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After protocol, before domain.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function afterProtocol(code) {\n    // To do: this is different from `markdown-rs`:\n    // https://github.com/wooorm/markdown-rs/blob/b3a921c761309ae00a51fe348d8a43adbc54b518/src/construct/gfm_autolink_literal.rs#L172-L182\n    return code === codes.eof ||\n      asciiControl(code) ||\n      markdownLineEndingOrSpace(code) ||\n      unicodeWhitespace(code) ||\n      unicodePunctuation(code)\n      ? nok(code)\n      : effects.attempt(domain, effects.attempt(path, protocolAfter), nok)(code)\n  }\n\n  /**\n   * After a protocol autolink literal.\n   *\n   * ```markdown\n   * > | https://example.com/a?b#c\n   *                              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function protocolAfter(code) {\n    effects.exit('literalAutolinkHttp')\n    effects.exit('literalAutolink')\n    return ok(code)\n  }\n}\n\n/**\n * `www` prefix.\n *\n * ```markdown\n * > | a www.example.org b\n *       ^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeWwwPrefix(effects, ok, nok) {\n  let size = 0\n\n  return wwwPrefixInside\n\n  /**\n   * In www prefix.\n   *\n   * ```markdown\n   * > | www.example.com\n   *     ^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function wwwPrefixInside(code) {\n    if ((code === codes.uppercaseW || code === codes.lowercaseW) && size < 3) {\n      size++\n      effects.consume(code)\n      return wwwPrefixInside\n    }\n\n    if (code === codes.dot && size === 3) {\n      effects.consume(code)\n      return wwwPrefixAfter\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After www prefix.\n   *\n   * ```markdown\n   * > | www.example.com\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function wwwPrefixAfter(code) {\n    // If there is *anything*, we can link.\n    return code === codes.eof ? nok(code) : ok(code)\n  }\n}\n\n/**\n * Domain.\n *\n * ```markdown\n * > | a https://example.org b\n *               ^^^^^^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeDomain(effects, ok, nok) {\n  /** @type {boolean | undefined} */\n  let underscoreInLastSegment\n  /** @type {boolean | undefined} */\n  let underscoreInLastLastSegment\n  /** @type {boolean | undefined} */\n  let seen\n\n  return domainInside\n\n  /**\n   * In domain.\n   *\n   * ```markdown\n   * > | https://example.com/a\n   *             ^^^^^^^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function domainInside(code) {\n    // Check whether this marker, which is a trailing punctuation\n    // marker, optionally followed by more trailing markers, and then\n    // followed by an end.\n    if (code === codes.dot || code === codes.underscore) {\n      return effects.check(trail, domainAfter, domainAtPunctuation)(code)\n    }\n\n    // GH documents that only alphanumerics (other than `-`, `.`, and `_`) can\n    // occur, which sounds like ASCII only, but they also support `www.點看.com`,\n    // so that’s Unicode.\n    // Instead of some new production for Unicode alphanumerics, markdown\n    // already has that for Unicode punctuation and whitespace, so use those.\n    // Source: <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L12>.\n    if (\n      code === codes.eof ||\n      markdownLineEndingOrSpace(code) ||\n      unicodeWhitespace(code) ||\n      (code !== codes.dash && unicodePunctuation(code))\n    ) {\n      return domainAfter(code)\n    }\n\n    seen = true\n    effects.consume(code)\n    return domainInside\n  }\n\n  /**\n   * In domain, at potential trailing punctuation, that was not trailing.\n   *\n   * ```markdown\n   * > | https://example.com\n   *                    ^\n   * ```\n   *\n   * @type {State}\n   */\n  function domainAtPunctuation(code) {\n    // There is an underscore in the last segment of the domain\n    if (code === codes.underscore) {\n      underscoreInLastSegment = true\n    }\n    // Otherwise, it’s a `.`: save the last segment underscore in the\n    // penultimate segment slot.\n    else {\n      underscoreInLastLastSegment = underscoreInLastSegment\n      underscoreInLastSegment = undefined\n    }\n\n    effects.consume(code)\n    return domainInside\n  }\n\n  /**\n   * After domain.\n   *\n   * ```markdown\n   * > | https://example.com/a\n   *                        ^\n   * ```\n   *\n   * @type {State} */\n  function domainAfter(code) {\n    // Note: that’s GH says a dot is needed, but it’s not true:\n    // <https://github.com/github/cmark-gfm/issues/279>\n    if (underscoreInLastLastSegment || underscoreInLastSegment || !seen) {\n      return nok(code)\n    }\n\n    return ok(code)\n  }\n}\n\n/**\n * Path.\n *\n * ```markdown\n * > | a https://example.org/stuff b\n *                          ^^^^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizePath(effects, ok) {\n  let sizeOpen = 0\n  let sizeClose = 0\n\n  return pathInside\n\n  /**\n   * In path.\n   *\n   * ```markdown\n   * > | https://example.com/a\n   *                        ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function pathInside(code) {\n    if (code === codes.leftParenthesis) {\n      sizeOpen++\n      effects.consume(code)\n      return pathInside\n    }\n\n    // To do: `markdown-rs` also needs this.\n    // If this is a paren, and there are less closings than openings,\n    // we don’t check for a trail.\n    if (code === codes.rightParenthesis && sizeClose < sizeOpen) {\n      return pathAtPunctuation(code)\n    }\n\n    // Check whether this trailing punctuation marker is optionally\n    // followed by more trailing markers, and then followed\n    // by an end.\n    if (\n      code === codes.exclamationMark ||\n      code === codes.quotationMark ||\n      code === codes.ampersand ||\n      code === codes.apostrophe ||\n      code === codes.rightParenthesis ||\n      code === codes.asterisk ||\n      code === codes.comma ||\n      code === codes.dot ||\n      code === codes.colon ||\n      code === codes.semicolon ||\n      code === codes.lessThan ||\n      code === codes.questionMark ||\n      code === codes.rightSquareBracket ||\n      code === codes.underscore ||\n      code === codes.tilde\n    ) {\n      return effects.check(trail, ok, pathAtPunctuation)(code)\n    }\n\n    if (\n      code === codes.eof ||\n      markdownLineEndingOrSpace(code) ||\n      unicodeWhitespace(code)\n    ) {\n      return ok(code)\n    }\n\n    effects.consume(code)\n    return pathInside\n  }\n\n  /**\n   * In path, at potential trailing punctuation, that was not trailing.\n   *\n   * ```markdown\n   * > | https://example.com/a\"b\n   *                          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function pathAtPunctuation(code) {\n    // Count closing parens.\n    if (code === codes.rightParenthesis) {\n      sizeClose++\n    }\n\n    effects.consume(code)\n    return pathInside\n  }\n}\n\n/**\n * Trail.\n *\n * This calls `ok` if this *is* the trail, followed by an end, which means\n * the entire trail is not part of the link.\n * It calls `nok` if this *is* part of the link.\n *\n * ```markdown\n * > | https://example.com\").\n *                        ^^^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeTrail(effects, ok, nok) {\n  return trail\n\n  /**\n   * In trail of domain or path.\n   *\n   * ```markdown\n   * > | https://example.com\").\n   *                        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function trail(code) {\n    // Regular trailing punctuation.\n    if (\n      code === codes.exclamationMark ||\n      code === codes.quotationMark ||\n      code === codes.apostrophe ||\n      code === codes.rightParenthesis ||\n      code === codes.asterisk ||\n      code === codes.comma ||\n      code === codes.dot ||\n      code === codes.colon ||\n      code === codes.semicolon ||\n      code === codes.questionMark ||\n      code === codes.underscore ||\n      code === codes.tilde\n    ) {\n      effects.consume(code)\n      return trail\n    }\n\n    // `&` followed by one or more alphabeticals and then a `;`, is\n    // as a whole considered as trailing punctuation.\n    // In all other cases, it is considered as continuation of the URL.\n    if (code === codes.ampersand) {\n      effects.consume(code)\n      return trailCharacterReferenceStart\n    }\n\n    // Needed because we allow literals after `[`, as we fix:\n    // <https://github.com/github/cmark-gfm/issues/278>.\n    // Check that it is not followed by `(` or `[`.\n    if (code === codes.rightSquareBracket) {\n      effects.consume(code)\n      return trailBracketAfter\n    }\n\n    if (\n      // `<` is an end.\n      code === codes.lessThan ||\n      // So is whitespace.\n      code === codes.eof ||\n      markdownLineEndingOrSpace(code) ||\n      unicodeWhitespace(code)\n    ) {\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In trail, after `]`.\n   *\n   * > 👉 **Note**: this deviates from `cmark-gfm` to fix a bug.\n   * > See end of <https://github.com/github/cmark-gfm/issues/278> for more.\n   *\n   * ```markdown\n   * > | https://example.com](\n   *                         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function trailBracketAfter(code) {\n    // Whitespace or something that could start a resource or reference is the end.\n    // Switch back to trail otherwise.\n    if (\n      code === codes.eof ||\n      code === codes.leftParenthesis ||\n      code === codes.leftSquareBracket ||\n      markdownLineEndingOrSpace(code) ||\n      unicodeWhitespace(code)\n    ) {\n      return ok(code)\n    }\n\n    return trail(code)\n  }\n\n  /**\n   * In character-reference like trail, after `&`.\n   *\n   * ```markdown\n   * > | https://example.com&amp;).\n   *                         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function trailCharacterReferenceStart(code) {\n    // When non-alpha, it’s not a trail.\n    return asciiAlpha(code) ? trailCharacterReferenceInside(code) : nok(code)\n  }\n\n  /**\n   * In character-reference like trail.\n   *\n   * ```markdown\n   * > | https://example.com&amp;).\n   *                         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function trailCharacterReferenceInside(code) {\n    // Switch back to trail if this is well-formed.\n    if (code === codes.semicolon) {\n      effects.consume(code)\n      return trail\n    }\n\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      return trailCharacterReferenceInside\n    }\n\n    // It’s not a trail.\n    return nok(code)\n  }\n}\n\n/**\n * Dot in email domain trail.\n *\n * This calls `ok` if this *is* the trail, followed by an end, which means\n * the trail is not part of the link.\n * It calls `nok` if this *is* part of the link.\n *\n * ```markdown\n * > | <EMAIL>.\n *                        ^\n * ```\n *\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeEmailDomainDotTrail(effects, ok, nok) {\n  return start\n\n  /**\n   * Dot.\n   *\n   * ```markdown\n   * > | <EMAIL>.\n   *                    ^   ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // Must be dot.\n    effects.consume(code)\n    return after\n  }\n\n  /**\n   * After dot.\n   *\n   * ```markdown\n   * > | <EMAIL>.\n   *                     ^   ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    // Not a trail if alphanumeric.\n    return asciiAlphanumeric(code) ? nok(code) : ok(code)\n  }\n}\n\n/**\n * See:\n * <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L156>.\n *\n * @type {Previous}\n */\nfunction previousWww(code) {\n  return (\n    code === codes.eof ||\n    code === codes.leftParenthesis ||\n    code === codes.asterisk ||\n    code === codes.underscore ||\n    code === codes.leftSquareBracket ||\n    code === codes.rightSquareBracket ||\n    code === codes.tilde ||\n    markdownLineEndingOrSpace(code)\n  )\n}\n\n/**\n * See:\n * <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L214>.\n *\n * @type {Previous}\n */\nfunction previousProtocol(code) {\n  return !asciiAlpha(code)\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Previous}\n */\nfunction previousEmail(code) {\n  // Do not allow a slash “inside” atext.\n  // The reference code is a bit weird, but that’s what it results in.\n  // Source: <https://github.com/github/cmark-gfm/blob/ef1cfcb/extensions/autolink.c#L307>.\n  // Other than slash, every preceding character is allowed.\n  return !(code === codes.slash || gfmAtext(code))\n}\n\n/**\n * @param {Code} code\n * @returns {boolean}\n */\nfunction gfmAtext(code) {\n  return (\n    code === codes.plusSign ||\n    code === codes.dash ||\n    code === codes.dot ||\n    code === codes.underscore ||\n    asciiAlphanumeric(code)\n  )\n}\n\n/**\n * @param {Array<Event>} events\n * @returns {boolean}\n */\nfunction previousUnbalanced(events) {\n  let index = events.length\n  let result = false\n\n  while (index--) {\n    const token = events[index][1]\n\n    if (\n      (token.type === 'labelLink' || token.type === 'labelImage') &&\n      !token._balanced\n    ) {\n      result = true\n      break\n    }\n\n    // If we’ve seen this token, and it was marked as not having any unbalanced\n    // bracket before it, we can exit.\n    if (token._gfmAutolinkLiteralWalkedInto) {\n      result = false\n      break\n    }\n  }\n\n  if (events.length > 0 && !result) {\n    // Mark the last token as “walked into” w/o finding\n    // anything.\n    events[events.length - 1][1]._gfmAutolinkLiteralWalkedInto = true\n  }\n\n  return result\n}\n", "/**\n * @import {Event, Exiter, Extension, Resolver, State, Token, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {blankLine} from 'micromark-core-commonmark'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEndingOrSpace} from 'micromark-util-character'\nimport {normalizeIdentifier} from 'micromark-util-normalize-identifier'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\nconst indent = {tokenize: tokenizeIndent, partial: true}\n\n// To do: micromark should support a `_hiddenGfmFootnoteSupport`, which only\n// affects label start (image).\n// That will let us drop `tokenizePotentialGfmFootnote*`.\n// It currently has a `_hiddenFootnoteSupport`, which affects that and more.\n// That can be removed when `micromark-extension-footnote` is archived.\n\n/**\n * Create an extension for `micromark` to enable GFM footnote syntax.\n *\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions` to\n *   enable GFM footnote syntax.\n */\nexport function gfmFootnote() {\n  /** @type {Extension} */\n  return {\n    document: {\n      [codes.leftSquareBracket]: {\n        name: 'gfmFootnoteDefinition',\n        tokenize: tokenizeDefinitionStart,\n        continuation: {tokenize: tokenizeDefinitionContinuation},\n        exit: gfmFootnoteDefinitionEnd\n      }\n    },\n    text: {\n      [codes.leftSquareBracket]: {\n        name: 'gfmFootnoteCall',\n        tokenize: tokenizeGfmFootnoteCall\n      },\n      [codes.rightSquareBracket]: {\n        name: 'gfmPotentialFootnoteCall',\n        add: 'after',\n        tokenize: tokenizePotentialGfmFootnoteCall,\n        resolveTo: resolveToPotentialGfmFootnoteCall\n      }\n    }\n  }\n}\n\n// To do: remove after micromark update.\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizePotentialGfmFootnoteCall(effects, ok, nok) {\n  const self = this\n  let index = self.events.length\n  const defined = self.parser.gfmFootnotes || (self.parser.gfmFootnotes = [])\n  /** @type {Token} */\n  let labelStart\n\n  // Find an opening.\n  while (index--) {\n    const token = self.events[index][1]\n\n    if (token.type === types.labelImage) {\n      labelStart = token\n      break\n    }\n\n    // Exit if we’ve walked far enough.\n    if (\n      token.type === 'gfmFootnoteCall' ||\n      token.type === types.labelLink ||\n      token.type === types.label ||\n      token.type === types.image ||\n      token.type === types.link\n    ) {\n      break\n    }\n  }\n\n  return start\n\n  /**\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.rightSquareBracket, 'expected `]`')\n\n    if (!labelStart || !labelStart._balanced) {\n      return nok(code)\n    }\n\n    const id = normalizeIdentifier(\n      self.sliceSerialize({start: labelStart.end, end: self.now()})\n    )\n\n    if (id.codePointAt(0) !== codes.caret || !defined.includes(id.slice(1))) {\n      return nok(code)\n    }\n\n    effects.enter('gfmFootnoteCallLabelMarker')\n    effects.consume(code)\n    effects.exit('gfmFootnoteCallLabelMarker')\n    return ok(code)\n  }\n}\n\n// To do: remove after micromark update.\n/** @type {Resolver} */\nfunction resolveToPotentialGfmFootnoteCall(events, context) {\n  let index = events.length\n  /** @type {Token | undefined} */\n  let labelStart\n\n  // Find an opening.\n  while (index--) {\n    if (\n      events[index][1].type === types.labelImage &&\n      events[index][0] === 'enter'\n    ) {\n      labelStart = events[index][1]\n      break\n    }\n  }\n\n  assert(labelStart, 'expected `labelStart` to resolve')\n\n  // Change the `labelImageMarker` to a `data`.\n  events[index + 1][1].type = types.data\n  events[index + 3][1].type = 'gfmFootnoteCallLabelMarker'\n\n  // The whole (without `!`):\n  /** @type {Token} */\n  const call = {\n    type: 'gfmFootnoteCall',\n    start: Object.assign({}, events[index + 3][1].start),\n    end: Object.assign({}, events[events.length - 1][1].end)\n  }\n  // The `^` marker\n  /** @type {Token} */\n  const marker = {\n    type: 'gfmFootnoteCallMarker',\n    start: Object.assign({}, events[index + 3][1].end),\n    end: Object.assign({}, events[index + 3][1].end)\n  }\n  // Increment the end 1 character.\n  marker.end.column++\n  marker.end.offset++\n  marker.end._bufferIndex++\n  /** @type {Token} */\n  const string = {\n    type: 'gfmFootnoteCallString',\n    start: Object.assign({}, marker.end),\n    end: Object.assign({}, events[events.length - 1][1].start)\n  }\n  /** @type {Token} */\n  const chunk = {\n    type: types.chunkString,\n    contentType: 'string',\n    start: Object.assign({}, string.start),\n    end: Object.assign({}, string.end)\n  }\n\n  /** @type {Array<Event>} */\n  const replacement = [\n    // Take the `labelImageMarker` (now `data`, the `!`)\n    events[index + 1],\n    events[index + 2],\n    ['enter', call, context],\n    // The `[`\n    events[index + 3],\n    events[index + 4],\n    // The `^`.\n    ['enter', marker, context],\n    ['exit', marker, context],\n    // Everything in between.\n    ['enter', string, context],\n    ['enter', chunk, context],\n    ['exit', chunk, context],\n    ['exit', string, context],\n    // The ending (`]`, properly parsed and labelled).\n    events[events.length - 2],\n    events[events.length - 1],\n    ['exit', call, context]\n  ]\n\n  events.splice(index, events.length - index + 1, ...replacement)\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeGfmFootnoteCall(effects, ok, nok) {\n  const self = this\n  const defined = self.parser.gfmFootnotes || (self.parser.gfmFootnotes = [])\n  let size = 0\n  /** @type {boolean} */\n  let data\n\n  // Note: the implementation of `markdown-rs` is different, because it houses\n  // core *and* extensions in one project.\n  // Therefore, it can include footnote logic inside `label-end`.\n  // We can’t do that, but luckily, we can parse footnotes in a simpler way than\n  // needed for labels.\n  return start\n\n  /**\n   * Start of footnote label.\n   *\n   * ```markdown\n   * > | a [^b] c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.leftSquareBracket, 'expected `[`')\n    effects.enter('gfmFootnoteCall')\n    effects.enter('gfmFootnoteCallLabelMarker')\n    effects.consume(code)\n    effects.exit('gfmFootnoteCallLabelMarker')\n    return callStart\n  }\n\n  /**\n   * After `[`, at `^`.\n   *\n   * ```markdown\n   * > | a [^b] c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function callStart(code) {\n    if (code !== codes.caret) return nok(code)\n\n    effects.enter('gfmFootnoteCallMarker')\n    effects.consume(code)\n    effects.exit('gfmFootnoteCallMarker')\n    effects.enter('gfmFootnoteCallString')\n    effects.enter('chunkString').contentType = 'string'\n    return callData\n  }\n\n  /**\n   * In label.\n   *\n   * ```markdown\n   * > | a [^b] c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function callData(code) {\n    if (\n      // Too long.\n      size > constants.linkReferenceSizeMax ||\n      // Closing brace with nothing.\n      (code === codes.rightSquareBracket && !data) ||\n      // Space or tab is not supported by GFM for some reason.\n      // `\\n` and `[` not being supported makes sense.\n      code === codes.eof ||\n      code === codes.leftSquareBracket ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      return nok(code)\n    }\n\n    if (code === codes.rightSquareBracket) {\n      effects.exit('chunkString')\n      const token = effects.exit('gfmFootnoteCallString')\n\n      if (!defined.includes(normalizeIdentifier(self.sliceSerialize(token)))) {\n        return nok(code)\n      }\n\n      effects.enter('gfmFootnoteCallLabelMarker')\n      effects.consume(code)\n      effects.exit('gfmFootnoteCallLabelMarker')\n      effects.exit('gfmFootnoteCall')\n      return ok\n    }\n\n    if (!markdownLineEndingOrSpace(code)) {\n      data = true\n    }\n\n    size++\n    effects.consume(code)\n    return code === codes.backslash ? callEscape : callData\n  }\n\n  /**\n   * On character after escape.\n   *\n   * ```markdown\n   * > | a [^b\\c] d\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function callEscape(code) {\n    if (\n      code === codes.leftSquareBracket ||\n      code === codes.backslash ||\n      code === codes.rightSquareBracket\n    ) {\n      effects.consume(code)\n      size++\n      return callData\n    }\n\n    return callData(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeDefinitionStart(effects, ok, nok) {\n  const self = this\n  const defined = self.parser.gfmFootnotes || (self.parser.gfmFootnotes = [])\n  /** @type {string} */\n  let identifier\n  let size = 0\n  /** @type {boolean | undefined} */\n  let data\n\n  return start\n\n  /**\n   * Start of GFM footnote definition.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.leftSquareBracket, 'expected `[`')\n    effects.enter('gfmFootnoteDefinition')._container = true\n    effects.enter('gfmFootnoteDefinitionLabel')\n    effects.enter('gfmFootnoteDefinitionLabelMarker')\n    effects.consume(code)\n    effects.exit('gfmFootnoteDefinitionLabelMarker')\n    return labelAtMarker\n  }\n\n  /**\n   * In label, at caret.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelAtMarker(code) {\n    if (code === codes.caret) {\n      effects.enter('gfmFootnoteDefinitionMarker')\n      effects.consume(code)\n      effects.exit('gfmFootnoteDefinitionMarker')\n      effects.enter('gfmFootnoteDefinitionLabelString')\n      effects.enter('chunkString').contentType = 'string'\n      return labelInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In label.\n   *\n   * > 👉 **Note**: `cmark-gfm` prevents whitespace from occurring in footnote\n   * > definition labels.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelInside(code) {\n    if (\n      // Too long.\n      size > constants.linkReferenceSizeMax ||\n      // Closing brace with nothing.\n      (code === codes.rightSquareBracket && !data) ||\n      // Space or tab is not supported by GFM for some reason.\n      // `\\n` and `[` not being supported makes sense.\n      code === codes.eof ||\n      code === codes.leftSquareBracket ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      return nok(code)\n    }\n\n    if (code === codes.rightSquareBracket) {\n      effects.exit('chunkString')\n      const token = effects.exit('gfmFootnoteDefinitionLabelString')\n      identifier = normalizeIdentifier(self.sliceSerialize(token))\n      effects.enter('gfmFootnoteDefinitionLabelMarker')\n      effects.consume(code)\n      effects.exit('gfmFootnoteDefinitionLabelMarker')\n      effects.exit('gfmFootnoteDefinitionLabel')\n      return labelAfter\n    }\n\n    if (!markdownLineEndingOrSpace(code)) {\n      data = true\n    }\n\n    size++\n    effects.consume(code)\n    return code === codes.backslash ? labelEscape : labelInside\n  }\n\n  /**\n   * After `\\`, at a special character.\n   *\n   * > 👉 **Note**: `cmark-gfm` currently does not support escaped brackets:\n   * > <https://github.com/github/cmark-gfm/issues/240>\n   *\n   * ```markdown\n   * > | [^a\\*b]: c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEscape(code) {\n    if (\n      code === codes.leftSquareBracket ||\n      code === codes.backslash ||\n      code === codes.rightSquareBracket\n    ) {\n      effects.consume(code)\n      size++\n      return labelInside\n    }\n\n    return labelInside(code)\n  }\n\n  /**\n   * After definition label.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelAfter(code) {\n    if (code === codes.colon) {\n      effects.enter('definitionMarker')\n      effects.consume(code)\n      effects.exit('definitionMarker')\n\n      if (!defined.includes(identifier)) {\n        defined.push(identifier)\n      }\n\n      // Any whitespace after the marker is eaten, forming indented code\n      // is not possible.\n      // No space is also fine, just like a block quote marker.\n      return factorySpace(\n        effects,\n        whitespaceAfter,\n        'gfmFootnoteDefinitionWhitespace'\n      )\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After definition prefix.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function whitespaceAfter(code) {\n    // `markdown-rs` has a wrapping token for the prefix that is closed here.\n    return ok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeDefinitionContinuation(effects, ok, nok) {\n  /// Start of footnote definition continuation.\n  ///\n  /// ```markdown\n  ///   | [^a]: b\n  /// > |     c\n  ///     ^\n  /// ```\n  //\n  // Either a blank line, which is okay, or an indented thing.\n  return effects.check(blankLine, ok, effects.attempt(indent, ok, nok))\n}\n\n/** @type {Exiter} */\nfunction gfmFootnoteDefinitionEnd(effects) {\n  effects.exit('gfmFootnoteDefinition')\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeIndent(effects, ok, nok) {\n  const self = this\n\n  return factorySpace(\n    effects,\n    afterPrefix,\n    'gfmFootnoteDefinitionIndent',\n    constants.tabSize + 1\n  )\n\n  /**\n   * @type {State}\n   */\n  function afterPrefix(code) {\n    const tail = self.events[self.events.length - 1]\n    return tail &&\n      tail[1].type === 'gfmFootnoteDefinitionIndent' &&\n      tail[2].sliceSerialize(tail[1], true).length === constants.tabSize\n      ? ok(code)\n      : nok(code)\n  }\n}\n", "/**\n * @import {HtmlOptions as Options} from 'micromark-extension-gfm-footnote'\n * @import {HtmlExtension} from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {normalizeIdentifier} from 'micromark-util-normalize-identifier'\nimport {sanitizeUri} from 'micromark-util-sanitize-uri'\n\nconst own = {}.hasOwnProperty\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Generate the default label that GitHub uses on backreferences.\n *\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {string}\n *   Default label.\n */\nexport function defaultBackLabel(referenceIndex, rereferenceIndex) {\n  return (\n    'Back to reference ' +\n    (referenceIndex + 1) +\n    (rereferenceIndex > 1 ? '-' + rereferenceIndex : '')\n  )\n}\n\n/**\n * Create an extension for `micromark` to support GFM footnotes when\n * serializing to HTML.\n *\n * @param {Options | null | undefined} [options={}]\n *   Configuration (optional).\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GFM footnotes when serializing to HTML.\n */\nexport function gfmFootnoteHtml(options) {\n  const config = options || emptyOptions\n  const label = config.label || 'Footnotes'\n  const labelTagName = config.labelTagName || 'h2'\n  const labelAttributes =\n    config.labelAttributes === null || config.labelAttributes === undefined\n      ? 'class=\"sr-only\"'\n      : config.labelAttributes\n  const backLabel = config.backLabel || defaultBackLabel\n  const clobberPrefix =\n    config.clobberPrefix === null || config.clobberPrefix === undefined\n      ? 'user-content-'\n      : config.clobberPrefix\n  return {\n    enter: {\n      gfmFootnoteDefinition() {\n        const stack = this.getData('tightStack')\n        stack.push(false)\n      },\n      gfmFootnoteDefinitionLabelString() {\n        this.buffer()\n      },\n      gfmFootnoteCallString() {\n        this.buffer()\n      }\n    },\n    exit: {\n      gfmFootnoteDefinition() {\n        let definitions = this.getData('gfmFootnoteDefinitions')\n        const footnoteStack = this.getData('gfmFootnoteDefinitionStack')\n        assert(footnoteStack, 'expected `footnoteStack`')\n        const tightStack = this.getData('tightStack')\n        const current = footnoteStack.pop()\n        const value = this.resume()\n\n        assert(current, 'expected to be in a footnote')\n\n        if (!definitions) {\n          this.setData('gfmFootnoteDefinitions', (definitions = {}))\n        }\n\n        if (!own.call(definitions, current)) definitions[current] = value\n\n        tightStack.pop()\n        this.setData('slurpOneLineEnding', true)\n        // “Hack” to prevent a line ending from showing up if we’re in a definition in\n        // an empty list item.\n        this.setData('lastWasTag')\n      },\n      gfmFootnoteDefinitionLabelString(token) {\n        let footnoteStack = this.getData('gfmFootnoteDefinitionStack')\n\n        if (!footnoteStack) {\n          this.setData('gfmFootnoteDefinitionStack', (footnoteStack = []))\n        }\n\n        footnoteStack.push(normalizeIdentifier(this.sliceSerialize(token)))\n        this.resume() // Drop the label.\n        this.buffer() // Get ready for a value.\n      },\n      gfmFootnoteCallString(token) {\n        let calls = this.getData('gfmFootnoteCallOrder')\n        let counts = this.getData('gfmFootnoteCallCounts')\n        const id = normalizeIdentifier(this.sliceSerialize(token))\n        /** @type {number} */\n        let counter\n\n        this.resume()\n\n        if (!calls) this.setData('gfmFootnoteCallOrder', (calls = []))\n        if (!counts) this.setData('gfmFootnoteCallCounts', (counts = {}))\n\n        const index = calls.indexOf(id)\n        const safeId = sanitizeUri(id.toLowerCase())\n\n        if (index === -1) {\n          calls.push(id)\n          counts[id] = 1\n          counter = calls.length\n        } else {\n          counts[id]++\n          counter = index + 1\n        }\n\n        const reuseCounter = counts[id]\n\n        this.tag(\n          '<sup><a href=\"#' +\n            clobberPrefix +\n            'fn-' +\n            safeId +\n            '\" id=\"' +\n            clobberPrefix +\n            'fnref-' +\n            safeId +\n            (reuseCounter > 1 ? '-' + reuseCounter : '') +\n            '\" data-footnote-ref=\"\" aria-describedby=\"footnote-label\">' +\n            String(counter) +\n            '</a></sup>'\n        )\n      },\n      null() {\n        const calls = this.getData('gfmFootnoteCallOrder') || []\n        const counts = this.getData('gfmFootnoteCallCounts') || {}\n        const definitions = this.getData('gfmFootnoteDefinitions') || {}\n        let index = -1\n\n        if (calls.length > 0) {\n          this.lineEndingIfNeeded()\n          this.tag(\n            '<section data-footnotes=\"\" class=\"footnotes\"><' +\n              labelTagName +\n              ' id=\"footnote-label\"' +\n              (labelAttributes ? ' ' + labelAttributes : '') +\n              '>'\n          )\n          this.raw(this.encode(label))\n          this.tag('</' + labelTagName + '>')\n          this.lineEndingIfNeeded()\n          this.tag('<ol>')\n        }\n\n        while (++index < calls.length) {\n          // Called definitions are always defined.\n          const id = calls[index]\n          const safeId = sanitizeUri(id.toLowerCase())\n          let referenceIndex = 0\n          /** @type {Array<string>} */\n          const references = []\n\n          while (++referenceIndex <= counts[id]) {\n            references.push(\n              '<a href=\"#' +\n                clobberPrefix +\n                'fnref-' +\n                safeId +\n                (referenceIndex > 1 ? '-' + referenceIndex : '') +\n                '\" data-footnote-backref=\"\" aria-label=\"' +\n                this.encode(\n                  typeof backLabel === 'string'\n                    ? backLabel\n                    : backLabel(index, referenceIndex)\n                ) +\n                '\" class=\"data-footnote-backref\">↩' +\n                (referenceIndex > 1\n                  ? '<sup>' + referenceIndex + '</sup>'\n                  : '') +\n                '</a>'\n            )\n          }\n\n          const reference = references.join(' ')\n          let injected = false\n\n          this.lineEndingIfNeeded()\n          this.tag('<li id=\"' + clobberPrefix + 'fn-' + safeId + '\">')\n          this.lineEndingIfNeeded()\n          this.tag(\n            definitions[id].replace(/<\\/p>(?:\\r?\\n|\\r)?$/, function ($0) {\n              injected = true\n              return ' ' + reference + $0\n            })\n          )\n\n          if (!injected) {\n            this.lineEndingIfNeeded()\n            this.tag(reference)\n          }\n\n          this.lineEndingIfNeeded()\n          this.tag('</li>')\n        }\n\n        if (calls.length > 0) {\n          this.lineEndingIfNeeded()\n          this.tag('</ol>')\n          this.lineEndingIfNeeded()\n          this.tag('</section>')\n        }\n      }\n    }\n  }\n}\n", "/**\n * @import {Options} from 'micromark-extension-gfm-strikethrough'\n * @import {Event, Extension, Resolver, State, Token, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {splice} from 'micromark-util-chunked'\nimport {classify<PERSON><PERSON><PERSON>} from 'micromark-util-classify-character'\nimport {resolveAll} from 'micromark-util-resolve-all'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/**\n * Create an extension for `micromark` to enable GFM strikethrough syntax.\n *\n * @param {Options | null | undefined} [options={}]\n *   Configuration.\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions`, to\n *   enable GFM strikethrough syntax.\n */\nexport function gfmStrikethrough(options) {\n  const options_ = options || {}\n  let single = options_.singleTilde\n  const tokenizer = {\n    name: 'strikethrough',\n    tokenize: tokenizeStrikethrough,\n    resolveAll: resolveAllStrikethrough\n  }\n\n  if (single === null || single === undefined) {\n    single = true\n  }\n\n  return {\n    text: {[codes.tilde]: tokenizer},\n    insideSpan: {null: [tokenizer]},\n    attentionMarkers: {null: [codes.tilde]}\n  }\n\n  /**\n   * Take events and resolve strikethrough.\n   *\n   * @type {Resolver}\n   */\n  function resolveAllStrikethrough(events, context) {\n    let index = -1\n\n    // Walk through all events.\n    while (++index < events.length) {\n      // Find a token that can close.\n      if (\n        events[index][0] === 'enter' &&\n        events[index][1].type === 'strikethroughSequenceTemporary' &&\n        events[index][1]._close\n      ) {\n        let open = index\n\n        // Now walk back to find an opener.\n        while (open--) {\n          // Find a token that can open the closer.\n          if (\n            events[open][0] === 'exit' &&\n            events[open][1].type === 'strikethroughSequenceTemporary' &&\n            events[open][1]._open &&\n            // If the sizes are the same:\n            events[index][1].end.offset - events[index][1].start.offset ===\n              events[open][1].end.offset - events[open][1].start.offset\n          ) {\n            events[index][1].type = 'strikethroughSequence'\n            events[open][1].type = 'strikethroughSequence'\n\n            /** @type {Token} */\n            const strikethrough = {\n              type: 'strikethrough',\n              start: Object.assign({}, events[open][1].start),\n              end: Object.assign({}, events[index][1].end)\n            }\n\n            /** @type {Token} */\n            const text = {\n              type: 'strikethroughText',\n              start: Object.assign({}, events[open][1].end),\n              end: Object.assign({}, events[index][1].start)\n            }\n\n            // Opening.\n            /** @type {Array<Event>} */\n            const nextEvents = [\n              ['enter', strikethrough, context],\n              ['enter', events[open][1], context],\n              ['exit', events[open][1], context],\n              ['enter', text, context]\n            ]\n\n            const insideSpan = context.parser.constructs.insideSpan.null\n\n            if (insideSpan) {\n              // Between.\n              splice(\n                nextEvents,\n                nextEvents.length,\n                0,\n                resolveAll(insideSpan, events.slice(open + 1, index), context)\n              )\n            }\n\n            // Closing.\n            splice(nextEvents, nextEvents.length, 0, [\n              ['exit', text, context],\n              ['enter', events[index][1], context],\n              ['exit', events[index][1], context],\n              ['exit', strikethrough, context]\n            ])\n\n            splice(events, open - 1, index - open + 3, nextEvents)\n\n            index = open + nextEvents.length - 2\n            break\n          }\n        }\n      }\n    }\n\n    index = -1\n\n    while (++index < events.length) {\n      if (events[index][1].type === 'strikethroughSequenceTemporary') {\n        events[index][1].type = types.data\n      }\n    }\n\n    return events\n  }\n\n  /**\n   * @this {TokenizeContext}\n   * @type {Tokenizer}\n   */\n  function tokenizeStrikethrough(effects, ok, nok) {\n    const previous = this.previous\n    const events = this.events\n    let size = 0\n\n    return start\n\n    /** @type {State} */\n    function start(code) {\n      assert(code === codes.tilde, 'expected `~`')\n\n      if (\n        previous === codes.tilde &&\n        events[events.length - 1][1].type !== types.characterEscape\n      ) {\n        return nok(code)\n      }\n\n      effects.enter('strikethroughSequenceTemporary')\n      return more(code)\n    }\n\n    /** @type {State} */\n    function more(code) {\n      const before = classifyCharacter(previous)\n\n      if (code === codes.tilde) {\n        // If this is the third marker, exit.\n        if (size > 1) return nok(code)\n        effects.consume(code)\n        size++\n        return more\n      }\n\n      if (size < 2 && !single) return nok(code)\n      const token = effects.exit('strikethroughSequenceTemporary')\n      const after = classifyCharacter(code)\n      token._open =\n        !after || (after === constants.attentionSideAfter && Boolean(before))\n      token._close =\n        !before || (before === constants.attentionSideAfter && Boolean(after))\n      return ok(code)\n    }\n  }\n}\n", "/**\n * @import {Event} from 'micromark-util-types'\n */\n\n// Port of `edit_map.rs` from `markdown-rs`.\n// This should move to `markdown-js` later.\n\n// Deal with several changes in events, batching them together.\n//\n// Preferably, changes should be kept to a minimum.\n// Sometimes, it’s needed to change the list of events, because parsing can be\n// messy, and it helps to expose a cleaner interface of events to the compiler\n// and other users.\n// It can also help to merge many adjacent similar events.\n// And, in other cases, it’s needed to parse subcontent: pass some events\n// through another tokenizer and inject the result.\n\n/**\n * @typedef {[number, number, Array<Event>]} Change\n * @typedef {[number, number, number]} Jump\n */\n\n/**\n * Tracks a bunch of edits.\n */\nexport class EditMap {\n  /**\n   * Create a new edit map.\n   */\n  constructor() {\n    /**\n     * Record of changes.\n     *\n     * @type {Array<Change>}\n     */\n    this.map = []\n  }\n\n  /**\n   * Create an edit: a remove and/or add at a certain place.\n   *\n   * @param {number} index\n   * @param {number} remove\n   * @param {Array<Event>} add\n   * @returns {undefined}\n   */\n  add(index, remove, add) {\n    addImplementation(this, index, remove, add)\n  }\n\n  // To do: add this when moving to `micromark`.\n  // /**\n  //  * Create an edit: but insert `add` before existing additions.\n  //  *\n  //  * @param {number} index\n  //  * @param {number} remove\n  //  * @param {Array<Event>} add\n  //  * @returns {undefined}\n  //  */\n  // addBefore(index, remove, add) {\n  //   addImplementation(this, index, remove, add, true)\n  // }\n\n  /**\n   * Done, change the events.\n   *\n   * @param {Array<Event>} events\n   * @returns {undefined}\n   */\n  consume(events) {\n    this.map.sort(function (a, b) {\n      return a[0] - b[0]\n    })\n\n    /* c8 ignore next 3 -- `resolve` is never called without tables, so without edits. */\n    if (this.map.length === 0) {\n      return\n    }\n\n    // To do: if links are added in events, like they are in `markdown-rs`,\n    // this is needed.\n    // // Calculate jumps: where items in the current list move to.\n    // /** @type {Array<Jump>} */\n    // const jumps = []\n    // let index = 0\n    // let addAcc = 0\n    // let removeAcc = 0\n    // while (index < this.map.length) {\n    //   const [at, remove, add] = this.map[index]\n    //   removeAcc += remove\n    //   addAcc += add.length\n    //   jumps.push([at, removeAcc, addAcc])\n    //   index += 1\n    // }\n    //\n    // . shiftLinks(events, jumps)\n\n    let index = this.map.length\n    /** @type {Array<Array<Event>>} */\n    const vecs = []\n    while (index > 0) {\n      index -= 1\n      vecs.push(\n        events.slice(this.map[index][0] + this.map[index][1]),\n        this.map[index][2]\n      )\n\n      // Truncate rest.\n      events.length = this.map[index][0]\n    }\n\n    vecs.push(events.slice())\n    events.length = 0\n\n    let slice = vecs.pop()\n\n    while (slice) {\n      for (const element of slice) {\n        events.push(element)\n      }\n\n      slice = vecs.pop()\n    }\n\n    // Truncate everything.\n    this.map.length = 0\n  }\n}\n\n/**\n * Create an edit.\n *\n * @param {EditMap} editMap\n * @param {number} at\n * @param {number} remove\n * @param {Array<Event>} add\n * @returns {undefined}\n */\nfunction addImplementation(editMap, at, remove, add) {\n  let index = 0\n\n  /* c8 ignore next 3 -- `resolve` is never called without tables, so without edits. */\n  if (remove === 0 && add.length === 0) {\n    return\n  }\n\n  while (index < editMap.map.length) {\n    if (editMap.map[index][0] === at) {\n      editMap.map[index][1] += remove\n\n      // To do: before not used by tables, use when moving to micromark.\n      // if (before) {\n      //   add.push(...editMap.map[index][2])\n      //   editMap.map[index][2] = add\n      // } else {\n      editMap.map[index][2].push(...add)\n      // }\n\n      return\n    }\n\n    index += 1\n  }\n\n  editMap.map.push([at, remove, add])\n}\n\n// /**\n//  * Shift `previous` and `next` links according to `jumps`.\n//  *\n//  * This fixes links in case there are events removed or added between them.\n//  *\n//  * @param {Array<Event>} events\n//  * @param {Array<Jump>} jumps\n//  */\n// function shiftLinks(events, jumps) {\n//   let jumpIndex = 0\n//   let index = 0\n//   let add = 0\n//   let rm = 0\n\n//   while (index < events.length) {\n//     const rmCurr = rm\n\n//     while (jumpIndex < jumps.length && jumps[jumpIndex][0] <= index) {\n//       add = jumps[jumpIndex][2]\n//       rm = jumps[jumpIndex][1]\n//       jumpIndex += 1\n//     }\n\n//     // Ignore items that will be removed.\n//     if (rm > rmCurr) {\n//       index += rm - rmCurr\n//     } else {\n//       // ?\n//       // if let Some(link) = &events[index].link {\n//       //     if let Some(next) = link.next {\n//       //         events[next].link.as_mut().unwrap().previous = Some(index + add - rm);\n//       //         while jumpIndex < jumps.len() && jumps[jumpIndex].0 <= next {\n//       //             add = jumps[jumpIndex].2;\n//       //             rm = jumps[jumpIndex].1;\n//       //             jumpIndex += 1;\n//       //         }\n//       //         events[index].link.as_mut().unwrap().next = Some(next + add - rm);\n//       //         index = next;\n//       //         continue;\n//       //     }\n//       // }\n//       index += 1\n//     }\n//   }\n// }\n", "/**\n * @import {Event} from 'micromark-util-types'\n */\n\n/**\n * @typedef {'center' | 'left' | 'none' | 'right'} Align\n */\n\nimport {ok as assert} from 'devlop'\n\n/**\n * Figure out the alignment of a GFM table.\n *\n * @param {Readonly<Array<Event>>} events\n *   List of events.\n * @param {number} index\n *   Table enter event.\n * @returns {Array<Align>}\n *   List of aligns.\n */\nexport function gfmTableAlign(events, index) {\n  assert(events[index][1].type === 'table', 'expected table')\n  let inDelimiterRow = false\n  /** @type {Array<Align>} */\n  const align = []\n\n  while (index < events.length) {\n    const event = events[index]\n\n    if (inDelimiterRow) {\n      if (event[0] === 'enter') {\n        // Start of alignment value: set a new column.\n        // To do: `markdown-rs` uses `tableDelimiterCellValue`.\n        if (event[1].type === 'tableContent') {\n          align.push(\n            events[index + 1][1].type === 'tableDelimiterMarker'\n              ? 'left'\n              : 'none'\n          )\n        }\n      }\n      // Exits:\n      // End of alignment value: change the column.\n      // To do: `markdown-rs` uses `tableDelimiterCellValue`.\n      else if (event[1].type === 'tableContent') {\n        if (events[index - 1][1].type === 'tableDelimiterMarker') {\n          const alignIndex = align.length - 1\n\n          align[alignIndex] = align[alignIndex] === 'left' ? 'center' : 'right'\n        }\n      }\n      // Done!\n      else if (event[1].type === 'tableDelimiterRow') {\n        break\n      }\n    } else if (event[0] === 'enter' && event[1].type === 'tableDelimiterRow') {\n      inDelimiterRow = true\n    }\n\n    index += 1\n  }\n\n  return align\n}\n", "/**\n * @import {Event, Extension, Point, Resolver, State, Token, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\n/**\n * @typedef {[number, number, number, number]} Range\n *   Cell info.\n *\n * @typedef {0 | 1 | 2 | 3} RowKind\n *   Where we are: `1` for head row, `2` for delimiter row, `3` for body row.\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {\n  markdownLineEnding,\n  markdownLineEndingOrSpace,\n  markdownSpace\n} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\nimport {EditMap} from './edit-map.js'\nimport {gfmTableAlign} from './infer.js'\n\n/**\n * Create an HTML extension for `micromark` to support GitHub tables syntax.\n *\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions` to enable GFM\n *   table syntax.\n */\nexport function gfmTable() {\n  return {\n    flow: {\n      null: {name: 'table', tokenize: tokenizeTable, resolveAll: resolveTable}\n    }\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeTable(effects, ok, nok) {\n  const self = this\n  let size = 0\n  let sizeB = 0\n  /** @type {boolean | undefined} */\n  let seen\n\n  return start\n\n  /**\n   * Start of a GFM table.\n   *\n   * If there is a valid table row or table head before, then we try to parse\n   * another row.\n   * Otherwise, we try to parse a head.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   * > | | b |\n   *     ^\n   * ```\n   * @type {State}\n   */\n  function start(code) {\n    let index = self.events.length - 1\n\n    while (index > -1) {\n      const type = self.events[index][1].type\n      if (\n        type === types.lineEnding ||\n        // Note: markdown-rs uses `whitespace` instead of `linePrefix`\n        type === types.linePrefix\n      )\n        index--\n      else break\n    }\n\n    const tail = index > -1 ? self.events[index][1].type : null\n\n    const next =\n      tail === 'tableHead' || tail === 'tableRow' ? bodyRowStart : headRowBefore\n\n    // Don’t allow lazy body rows.\n    if (next === bodyRowStart && self.parser.lazy[self.now().line]) {\n      return nok(code)\n    }\n\n    return next(code)\n  }\n\n  /**\n   * Before table head row.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowBefore(code) {\n    effects.enter('tableHead')\n    effects.enter('tableRow')\n    return headRowStart(code)\n  }\n\n  /**\n   * Before table head row, after whitespace.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowStart(code) {\n    if (code === codes.verticalBar) {\n      return headRowBreak(code)\n    }\n\n    // To do: micromark-js should let us parse our own whitespace in extensions,\n    // like `markdown-rs`:\n    //\n    // ```js\n    // // 4+ spaces.\n    // if (markdownSpace(code)) {\n    //   return nok(code)\n    // }\n    // ```\n\n    seen = true\n    // Count the first character, that isn’t a pipe, double.\n    sizeB += 1\n    return headRowBreak(code)\n  }\n\n  /**\n   * At break in table head row.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *       ^\n   *         ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowBreak(code) {\n    if (code === codes.eof) {\n      // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n      return nok(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      // If anything other than one pipe (ignoring whitespace) was used, it’s fine.\n      if (sizeB > 1) {\n        sizeB = 0\n        // To do: check if this works.\n        // Feel free to interrupt:\n        self.interrupt = true\n        effects.exit('tableRow')\n        effects.enter(types.lineEnding)\n        effects.consume(code)\n        effects.exit(types.lineEnding)\n        return headDelimiterStart\n      }\n\n      // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n      return nok(code)\n    }\n\n    if (markdownSpace(code)) {\n      // To do: check if this is fine.\n      // effects.attempt(State::Next(StateName::GfmTableHeadRowBreak), State::Nok)\n      // State::Retry(space_or_tab(tokenizer))\n      return factorySpace(effects, headRowBreak, types.whitespace)(code)\n    }\n\n    sizeB += 1\n\n    if (seen) {\n      seen = false\n      // Header cell count.\n      size += 1\n    }\n\n    if (code === codes.verticalBar) {\n      effects.enter('tableCellDivider')\n      effects.consume(code)\n      effects.exit('tableCellDivider')\n      // Whether a delimiter was seen.\n      seen = true\n      return headRowBreak\n    }\n\n    // Anything else is cell data.\n    effects.enter(types.data)\n    return headRowData(code)\n  }\n\n  /**\n   * In table head row data.\n   *\n   * ```markdown\n   * > | | a |\n   *       ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowData(code) {\n    if (\n      code === codes.eof ||\n      code === codes.verticalBar ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      effects.exit(types.data)\n      return headRowBreak(code)\n    }\n\n    effects.consume(code)\n    return code === codes.backslash ? headRowEscape : headRowData\n  }\n\n  /**\n   * In table head row escape.\n   *\n   * ```markdown\n   * > | | a\\-b |\n   *         ^\n   *   | | ---- |\n   *   | | c    |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowEscape(code) {\n    if (code === codes.backslash || code === codes.verticalBar) {\n      effects.consume(code)\n      return headRowData\n    }\n\n    return headRowData(code)\n  }\n\n  /**\n   * Before delimiter row.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *     ^\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterStart(code) {\n    // Reset `interrupt`.\n    self.interrupt = false\n\n    // Note: in `markdown-rs`, we need to handle piercing here too.\n    if (self.parser.lazy[self.now().line]) {\n      return nok(code)\n    }\n\n    effects.enter('tableDelimiterRow')\n    // Track if we’ve seen a `:` or `|`.\n    seen = false\n\n    if (markdownSpace(code)) {\n      assert(self.parser.constructs.disable.null, 'expected `disabled.null`')\n      return factorySpace(\n        effects,\n        headDelimiterBefore,\n        types.linePrefix,\n        self.parser.constructs.disable.null.includes('codeIndented')\n          ? undefined\n          : constants.tabSize\n      )(code)\n    }\n\n    return headDelimiterBefore(code)\n  }\n\n  /**\n   * Before delimiter row, after optional whitespace.\n   *\n   * Reused when a `|` is found later, to parse another cell.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *     ^\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterBefore(code) {\n    if (code === codes.dash || code === codes.colon) {\n      return headDelimiterValueBefore(code)\n    }\n\n    if (code === codes.verticalBar) {\n      seen = true\n      // If we start with a pipe, we open a cell marker.\n      effects.enter('tableCellDivider')\n      effects.consume(code)\n      effects.exit('tableCellDivider')\n      return headDelimiterCellBefore\n    }\n\n    // More whitespace / empty row not allowed at start.\n    return headDelimiterNok(code)\n  }\n\n  /**\n   * After `|`, before delimiter cell.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterCellBefore(code) {\n    if (markdownSpace(code)) {\n      return factorySpace(\n        effects,\n        headDelimiterValueBefore,\n        types.whitespace\n      )(code)\n    }\n\n    return headDelimiterValueBefore(code)\n  }\n\n  /**\n   * Before delimiter cell value.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterValueBefore(code) {\n    // Align: left.\n    if (code === codes.colon) {\n      sizeB += 1\n      seen = true\n\n      effects.enter('tableDelimiterMarker')\n      effects.consume(code)\n      effects.exit('tableDelimiterMarker')\n      return headDelimiterLeftAlignmentAfter\n    }\n\n    // Align: none.\n    if (code === codes.dash) {\n      sizeB += 1\n      // To do: seems weird that this *isn’t* left aligned, but that state is used?\n      return headDelimiterLeftAlignmentAfter(code)\n    }\n\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return headDelimiterCellAfter(code)\n    }\n\n    return headDelimiterNok(code)\n  }\n\n  /**\n   * After delimiter cell left alignment marker.\n   *\n   * ```markdown\n   *   | | a  |\n   * > | | :- |\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterLeftAlignmentAfter(code) {\n    if (code === codes.dash) {\n      effects.enter('tableDelimiterFiller')\n      return headDelimiterFiller(code)\n    }\n\n    // Anything else is not ok after the left-align colon.\n    return headDelimiterNok(code)\n  }\n\n  /**\n   * In delimiter cell filler.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterFiller(code) {\n    if (code === codes.dash) {\n      effects.consume(code)\n      return headDelimiterFiller\n    }\n\n    // Align is `center` if it was `left`, `right` otherwise.\n    if (code === codes.colon) {\n      seen = true\n      effects.exit('tableDelimiterFiller')\n      effects.enter('tableDelimiterMarker')\n      effects.consume(code)\n      effects.exit('tableDelimiterMarker')\n      return headDelimiterRightAlignmentAfter\n    }\n\n    effects.exit('tableDelimiterFiller')\n    return headDelimiterRightAlignmentAfter(code)\n  }\n\n  /**\n   * After delimiter cell right alignment marker.\n   *\n   * ```markdown\n   *   | |  a |\n   * > | | -: |\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterRightAlignmentAfter(code) {\n    if (markdownSpace(code)) {\n      return factorySpace(\n        effects,\n        headDelimiterCellAfter,\n        types.whitespace\n      )(code)\n    }\n\n    return headDelimiterCellAfter(code)\n  }\n\n  /**\n   * After delimiter cell.\n   *\n   * ```markdown\n   *   | |  a |\n   * > | | -: |\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterCellAfter(code) {\n    if (code === codes.verticalBar) {\n      return headDelimiterBefore(code)\n    }\n\n    if (code === codes.eof || markdownLineEnding(code)) {\n      // Exit when:\n      // * there was no `:` or `|` at all (it’s a thematic break or setext\n      //   underline instead)\n      // * the header cell count is not the delimiter cell count\n      if (!seen || size !== sizeB) {\n        return headDelimiterNok(code)\n      }\n\n      // Note: in markdown-rs`, a reset is needed here.\n      effects.exit('tableDelimiterRow')\n      effects.exit('tableHead')\n      // To do: in `markdown-rs`, resolvers need to be registered manually.\n      // effects.register_resolver(ResolveName::GfmTable)\n      return ok(code)\n    }\n\n    return headDelimiterNok(code)\n  }\n\n  /**\n   * In delimiter row, at a disallowed byte.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | x |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterNok(code) {\n    // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n    return nok(code)\n  }\n\n  /**\n   * Before table body row.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function bodyRowStart(code) {\n    // Note: in `markdown-rs` we need to manually take care of a prefix,\n    // but in `micromark-js` that is done for us, so if we’re here, we’re\n    // never at whitespace.\n    effects.enter('tableRow')\n    return bodyRowBreak(code)\n  }\n\n  /**\n   * At break in table body row.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *     ^\n   *       ^\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function bodyRowBreak(code) {\n    if (code === codes.verticalBar) {\n      effects.enter('tableCellDivider')\n      effects.consume(code)\n      effects.exit('tableCellDivider')\n      return bodyRowBreak\n    }\n\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit('tableRow')\n      return ok(code)\n    }\n\n    if (markdownSpace(code)) {\n      return factorySpace(effects, bodyRowBreak, types.whitespace)(code)\n    }\n\n    // Anything else is cell content.\n    effects.enter(types.data)\n    return bodyRowData(code)\n  }\n\n  /**\n   * In table body row data.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function bodyRowData(code) {\n    if (\n      code === codes.eof ||\n      code === codes.verticalBar ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      effects.exit(types.data)\n      return bodyRowBreak(code)\n    }\n\n    effects.consume(code)\n    return code === codes.backslash ? bodyRowEscape : bodyRowData\n  }\n\n  /**\n   * In table body row escape.\n   *\n   * ```markdown\n   *   | | a    |\n   *   | | ---- |\n   * > | | b\\-c |\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function bodyRowEscape(code) {\n    if (code === codes.backslash || code === codes.verticalBar) {\n      effects.consume(code)\n      return bodyRowData\n    }\n\n    return bodyRowData(code)\n  }\n}\n\n/** @type {Resolver} */\n\nfunction resolveTable(events, context) {\n  let index = -1\n  let inFirstCellAwaitingPipe = true\n  /** @type {RowKind} */\n  let rowKind = 0\n  /** @type {Range} */\n  let lastCell = [0, 0, 0, 0]\n  /** @type {Range} */\n  let cell = [0, 0, 0, 0]\n  let afterHeadAwaitingFirstBodyRow = false\n  let lastTableEnd = 0\n  /** @type {Token | undefined} */\n  let currentTable\n  /** @type {Token | undefined} */\n  let currentBody\n  /** @type {Token | undefined} */\n  let currentCell\n\n  const map = new EditMap()\n\n  while (++index < events.length) {\n    const event = events[index]\n    const token = event[1]\n\n    if (event[0] === 'enter') {\n      // Start of head.\n      if (token.type === 'tableHead') {\n        afterHeadAwaitingFirstBodyRow = false\n\n        // Inject previous (body end and) table end.\n        if (lastTableEnd !== 0) {\n          assert(currentTable, 'there should be a table opening')\n          flushTableEnd(map, context, lastTableEnd, currentTable, currentBody)\n          currentBody = undefined\n          lastTableEnd = 0\n        }\n\n        // Inject table start.\n        currentTable = {\n          type: 'table',\n          start: Object.assign({}, token.start),\n          // Note: correct end is set later.\n          end: Object.assign({}, token.end)\n        }\n        map.add(index, 0, [['enter', currentTable, context]])\n      } else if (\n        token.type === 'tableRow' ||\n        token.type === 'tableDelimiterRow'\n      ) {\n        inFirstCellAwaitingPipe = true\n        currentCell = undefined\n        lastCell = [0, 0, 0, 0]\n        cell = [0, index + 1, 0, 0]\n\n        // Inject table body start.\n        if (afterHeadAwaitingFirstBodyRow) {\n          afterHeadAwaitingFirstBodyRow = false\n          currentBody = {\n            type: 'tableBody',\n            start: Object.assign({}, token.start),\n            // Note: correct end is set later.\n            end: Object.assign({}, token.end)\n          }\n          map.add(index, 0, [['enter', currentBody, context]])\n        }\n\n        rowKind = token.type === 'tableDelimiterRow' ? 2 : currentBody ? 3 : 1\n      }\n      // Cell data.\n      else if (\n        rowKind &&\n        (token.type === types.data ||\n          token.type === 'tableDelimiterMarker' ||\n          token.type === 'tableDelimiterFiller')\n      ) {\n        inFirstCellAwaitingPipe = false\n\n        // First value in cell.\n        if (cell[2] === 0) {\n          if (lastCell[1] !== 0) {\n            cell[0] = cell[1]\n            currentCell = flushCell(\n              map,\n              context,\n              lastCell,\n              rowKind,\n              undefined,\n              currentCell\n            )\n            lastCell = [0, 0, 0, 0]\n          }\n\n          cell[2] = index\n        }\n      } else if (token.type === 'tableCellDivider') {\n        if (inFirstCellAwaitingPipe) {\n          inFirstCellAwaitingPipe = false\n        } else {\n          if (lastCell[1] !== 0) {\n            cell[0] = cell[1]\n            currentCell = flushCell(\n              map,\n              context,\n              lastCell,\n              rowKind,\n              undefined,\n              currentCell\n            )\n          }\n\n          lastCell = cell\n          cell = [lastCell[1], index, 0, 0]\n        }\n      }\n    }\n    // Exit events.\n    else if (token.type === 'tableHead') {\n      afterHeadAwaitingFirstBodyRow = true\n      lastTableEnd = index\n    } else if (\n      token.type === 'tableRow' ||\n      token.type === 'tableDelimiterRow'\n    ) {\n      lastTableEnd = index\n\n      if (lastCell[1] !== 0) {\n        cell[0] = cell[1]\n        currentCell = flushCell(\n          map,\n          context,\n          lastCell,\n          rowKind,\n          index,\n          currentCell\n        )\n      } else if (cell[1] !== 0) {\n        currentCell = flushCell(map, context, cell, rowKind, index, currentCell)\n      }\n\n      rowKind = 0\n    } else if (\n      rowKind &&\n      (token.type === types.data ||\n        token.type === 'tableDelimiterMarker' ||\n        token.type === 'tableDelimiterFiller')\n    ) {\n      cell[3] = index\n    }\n  }\n\n  if (lastTableEnd !== 0) {\n    assert(currentTable, 'expected table opening')\n    flushTableEnd(map, context, lastTableEnd, currentTable, currentBody)\n  }\n\n  map.consume(context.events)\n\n  // To do: move this into `html`, when events are exposed there.\n  // That’s what `markdown-rs` does.\n  // That needs updates to `mdast-util-gfm-table`.\n  index = -1\n  while (++index < context.events.length) {\n    const event = context.events[index]\n    if (event[0] === 'enter' && event[1].type === 'table') {\n      event[1]._align = gfmTableAlign(context.events, index)\n    }\n  }\n\n  return events\n}\n\n/**\n * Generate a cell.\n *\n * @param {EditMap} map\n * @param {Readonly<TokenizeContext>} context\n * @param {Readonly<Range>} range\n * @param {RowKind} rowKind\n * @param {number | undefined} rowEnd\n * @param {Token | undefined} previousCell\n * @returns {Token | undefined}\n */\n// eslint-disable-next-line max-params\nfunction flushCell(map, context, range, rowKind, rowEnd, previousCell) {\n  // `markdown-rs` uses:\n  // rowKind === 2 ? 'tableDelimiterCell' : 'tableCell'\n  const groupName =\n    rowKind === 1\n      ? 'tableHeader'\n      : rowKind === 2\n        ? 'tableDelimiter'\n        : 'tableData'\n  // `markdown-rs` uses:\n  // rowKind === 2 ? 'tableDelimiterCellValue' : 'tableCellText'\n  const valueName = 'tableContent'\n\n  // Insert an exit for the previous cell, if there is one.\n  //\n  // ```markdown\n  // > | | aa | bb | cc |\n  //          ^-- exit\n  //           ^^^^-- this cell\n  // ```\n  if (range[0] !== 0) {\n    assert(previousCell, 'expected previous cell enter')\n    previousCell.end = Object.assign({}, getPoint(context.events, range[0]))\n    map.add(range[0], 0, [['exit', previousCell, context]])\n  }\n\n  // Insert enter of this cell.\n  //\n  // ```markdown\n  // > | | aa | bb | cc |\n  //           ^-- enter\n  //           ^^^^-- this cell\n  // ```\n  const now = getPoint(context.events, range[1])\n  previousCell = {\n    type: groupName,\n    start: Object.assign({}, now),\n    // Note: correct end is set later.\n    end: Object.assign({}, now)\n  }\n  map.add(range[1], 0, [['enter', previousCell, context]])\n\n  // Insert text start at first data start and end at last data end, and\n  // remove events between.\n  //\n  // ```markdown\n  // > | | aa | bb | cc |\n  //            ^-- enter\n  //             ^-- exit\n  //           ^^^^-- this cell\n  // ```\n  if (range[2] !== 0) {\n    const relatedStart = getPoint(context.events, range[2])\n    const relatedEnd = getPoint(context.events, range[3])\n    /** @type {Token} */\n    const valueToken = {\n      type: valueName,\n      start: Object.assign({}, relatedStart),\n      end: Object.assign({}, relatedEnd)\n    }\n    map.add(range[2], 0, [['enter', valueToken, context]])\n    assert(range[3] !== 0)\n\n    if (rowKind !== 2) {\n      // Fix positional info on remaining events\n      const start = context.events[range[2]]\n      const end = context.events[range[3]]\n      start[1].end = Object.assign({}, end[1].end)\n      start[1].type = types.chunkText\n      start[1].contentType = constants.contentTypeText\n\n      // Remove if needed.\n      if (range[3] > range[2] + 1) {\n        const a = range[2] + 1\n        const b = range[3] - range[2] - 1\n        map.add(a, b, [])\n      }\n    }\n\n    map.add(range[3] + 1, 0, [['exit', valueToken, context]])\n  }\n\n  // Insert an exit for the last cell, if at the row end.\n  //\n  // ```markdown\n  // > | | aa | bb | cc |\n  //                    ^-- exit\n  //               ^^^^^^-- this cell (the last one contains two “between” parts)\n  // ```\n  if (rowEnd !== undefined) {\n    previousCell.end = Object.assign({}, getPoint(context.events, rowEnd))\n    map.add(rowEnd, 0, [['exit', previousCell, context]])\n    previousCell = undefined\n  }\n\n  return previousCell\n}\n\n/**\n * Generate table end (and table body end).\n *\n * @param {Readonly<EditMap>} map\n * @param {Readonly<TokenizeContext>} context\n * @param {number} index\n * @param {Token} table\n * @param {Token | undefined} tableBody\n */\n// eslint-disable-next-line max-params\nfunction flushTableEnd(map, context, index, table, tableBody) {\n  /** @type {Array<Event>} */\n  const exits = []\n  const related = getPoint(context.events, index)\n\n  if (tableBody) {\n    tableBody.end = Object.assign({}, related)\n    exits.push(['exit', tableBody, context])\n  }\n\n  table.end = Object.assign({}, related)\n  exits.push(['exit', table, context])\n\n  map.add(index + 1, 0, exits)\n}\n\n/**\n * @param {Readonly<Array<Event>>} events\n * @param {number} index\n * @returns {Readonly<Point>}\n */\nfunction getPoint(events, index) {\n  const event = events[index]\n  const side = event[0] === 'enter' ? 'start' : 'end'\n  return event[1][side]\n}\n", "/**\n * @typedef {import('micromark-util-types').CompileContext} CompileContext\n * @typedef {import('micromark-util-types').HtmlExtension} HtmlExtension\n * @typedef {import('micromark-util-types').Token} Token\n */\n\n// An opening or closing tag start, followed by a case-insensitive specific tag name,\n// followed by HTML whitespace, a greater than, or a slash.\nconst reFlow =\n  /<(\\/?)(iframe|noembed|noframes|plaintext|script|style|title|textarea|xmp)(?=[\\t\\n\\f\\r />])/gi\n\n// As HTML (text) parses tags separately (and very strictly), we don’t need to be\n// global.\nconst reText = new RegExp('^' + reFlow.source, 'i')\n\n/**\n * Create an HTML extension for `micromark` to support GitHubs weird and\n * useless tagfilter when serializing to HTML.\n *\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to support\n *   GitHubs weird and useless tagfilter when serializing to HTML.\n */\nexport function gfmTagfilterHtml() {\n  return {\n    exit: {\n      htmlFlowData(token) {\n        exitHtmlData.call(this, token, reFlow)\n      },\n      htmlTextData(token) {\n        exitHtmlData.call(this, token, reText)\n      }\n    }\n  }\n}\n\n/**\n * @this {CompileContext}\n * @param {Token} token\n * @param {RegExp} filter\n * @returns {undefined}\n */\nfunction exitHtmlData(token, filter) {\n  let value = this.sliceSerialize(token)\n\n  if (this.options.allowDangerousHtml) {\n    value = value.replace(filter, '&lt;$1$2')\n  }\n\n  this.raw(this.encode(value))\n}\n", "/**\n * @import {Extension, State, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {\n  markdownLineEnding,\n  markdownLineEndingOrSpace,\n  markdownSpace\n} from 'micromark-util-character'\nimport {codes, types} from 'micromark-util-symbol'\n\nconst tasklistCheck = {name: 'tasklist<PERSON>heck', tokenize: tokenizeTasklistCheck}\n\n/**\n * Create an HTML extension for `micromark` to support GFM task list items\n * syntax.\n *\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GFM task list items when serializing to HTML.\n */\nexport function gfmTaskListItem() {\n  return {\n    text: {[codes.leftSquareBracket]: tasklistCheck}\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeTasklistCheck(effects, ok, nok) {\n  const self = this\n\n  return open\n\n  /**\n   * At start of task list item check.\n   *\n   * ```markdown\n   * > | * [x] y.\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    assert(code === codes.leftSquareBracket, 'expected `[`')\n\n    if (\n      // Exit if there’s stuff before.\n      self.previous !== codes.eof ||\n      // Exit if not in the first content that is the first child of a list\n      // item.\n      !self._gfmTasklistFirstContentOfListItem\n    ) {\n      return nok(code)\n    }\n\n    effects.enter('taskListCheck')\n    effects.enter('taskListCheckMarker')\n    effects.consume(code)\n    effects.exit('taskListCheckMarker')\n    return inside\n  }\n\n  /**\n   * In task list item check.\n   *\n   * ```markdown\n   * > | * [x] y.\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    // Currently we match how GH works in files.\n    // To match how GH works in comments, use `markdownSpace` (`[\\t ]`) instead\n    // of `markdownLineEndingOrSpace` (`[\\t\\n\\r ]`).\n    if (markdownLineEndingOrSpace(code)) {\n      effects.enter('taskListCheckValueUnchecked')\n      effects.consume(code)\n      effects.exit('taskListCheckValueUnchecked')\n      return close\n    }\n\n    if (code === codes.uppercaseX || code === codes.lowercaseX) {\n      effects.enter('taskListCheckValueChecked')\n      effects.consume(code)\n      effects.exit('taskListCheckValueChecked')\n      return close\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * At close of task list item check.\n   *\n   * ```markdown\n   * > | * [x] y.\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function close(code) {\n    if (code === codes.rightSquareBracket) {\n      effects.enter('taskListCheckMarker')\n      effects.consume(code)\n      effects.exit('taskListCheckMarker')\n      effects.exit('taskListCheck')\n      return after\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * @type {State}\n   */\n  function after(code) {\n    // EOL in paragraph means there must be something else after it.\n    if (markdownLineEnding(code)) {\n      return ok(code)\n    }\n\n    // Space or tab?\n    // Check what comes after.\n    if (markdownSpace(code)) {\n      return effects.check({tokenize: spaceThenNonSpace}, ok, nok)(code)\n    }\n\n    // EOF, or non-whitespace, both wrong.\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction spaceThenNonSpace(effects, ok, nok) {\n  return factorySpace(effects, after, types.whitespace)\n\n  /**\n   * After whitespace, after task list item check.\n   *\n   * ```markdown\n   * > | * [x] y.\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    // EOF means there was nothing, so bad.\n    // EOL means there’s content after it, so good.\n    // Impossible to have more spaces.\n    // Anything else is good.\n    return code === codes.eof ? nok(code) : ok(code)\n  }\n}\n", "/**\n * @typedef {import('micromark-extension-gfm-footnote').HtmlOptions} HtmlOptions\n * @typedef {import('micromark-extension-gfm-strikethrough').Options} Options\n * @typedef {import('micromark-util-types').Extension} Extension\n * @typedef {import('micromark-util-types').HtmlExtension} HtmlExtension\n */\n\nimport {\n  combineExtensions,\n  combineHtmlExtensions\n} from 'micromark-util-combine-extensions'\nimport {\n  gfmAutolinkLiteral,\n  gfmAutolinkLiteralHtml\n} from 'micromark-extension-gfm-autolink-literal'\nimport {gfmFootnote, gfmFootnoteHtml} from 'micromark-extension-gfm-footnote'\nimport {\n  gfmStrikethrough,\n  gfmStrikethroughHtml\n} from 'micromark-extension-gfm-strikethrough'\nimport {gfmTable, gfmTableHtml} from 'micromark-extension-gfm-table'\nimport {gfmTagfilterHtml} from 'micromark-extension-gfm-tagfilter'\nimport {\n  gfmTaskListItem,\n  gfmTaskListItemHtml\n} from 'micromark-extension-gfm-task-list-item'\n\n/**\n * Create an extension for `micromark` to enable GFM syntax.\n *\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n *\n *   Passed to `micromark-extens-gfm-strikethrough`.\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions` to enable GFM\n *   syntax.\n */\nexport function gfm(options) {\n  return combineExtensions([\n    gfmAutolinkLiteral(),\n    gfmFootnote(),\n    gfmStrikethrough(options),\n    gfmTable(),\n    gfmTaskListItem()\n  ])\n}\n\n/**\n * Create an extension for `micromark` to support GFM when serializing to HTML.\n *\n * @param {HtmlOptions | null | undefined} [options]\n *   Configuration (optional).\n *\n *   Passed to `micromark-extens-gfm-footnote`.\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GFM when serializing to HTML.\n */\nexport function gfmHtml(options) {\n  return combineHtmlExtensions([\n    gfmAutolinkLiteralHtml(),\n    gfmFootnoteHtml(options),\n    gfmStrikethroughHtml(),\n    gfmTableHtml(),\n    gfmTagfilterHtml(),\n    gfmTaskListItemHtml()\n  ])\n}\n", "/**\n * @import {Root} from 'mdast'\n * @import {Options} from 'remark-gfm'\n * @import {} from 'remark-parse'\n * @import {} from 'remark-stringify'\n * @import {Processor} from 'unified'\n */\n\nimport {gfmFromMarkdown, gfmToMarkdown} from 'mdast-util-gfm'\nimport {gfm} from 'micromark-extension-gfm'\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Add support GFM (autolink literals, footnotes, strikethrough, tables,\n * tasklists).\n *\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */\nexport default function remarkGfm(options) {\n  // @ts-expect-error: TS is wrong about `this`.\n  // eslint-disable-next-line unicorn/no-this-assignment\n  const self = /** @type {Processor<Root>} */ (this)\n  const settings = options || emptyOptions\n  const data = self.data()\n\n  const micromarkExtensions =\n    data.micromarkExtensions || (data.micromarkExtensions = [])\n  const fromMarkdownExtensions =\n    data.fromMarkdownExtensions || (data.fromMarkdownExtensions = [])\n  const toMarkdownExtensions =\n    data.toMarkdownExtensions || (data.toMarkdownExtensions = [])\n\n  micromarkExtensions.push(gfm(settings))\n  fromMarkdownExtensions.push(gfmFromMarkdown())\n  toMarkdownExtensions.push(gfmToMarkdown(settings))\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUO,SAAS,OAAO,OAAO,WAAW;AACvC,QAAM,SAAS,OAAO,KAAK;AAE3B,MAAI,OAAO,cAAc,UAAU;AACjC,UAAM,IAAI,UAAU,oBAAoB;AAAA,EAC1C;AAEA,MAAI,QAAQ;AACZ,MAAI,QAAQ,OAAO,QAAQ,SAAS;AAEpC,SAAO,UAAU,IAAI;AACnB;AACA,YAAQ,OAAO,QAAQ,WAAW,QAAQ,UAAU,MAAM;AAAA,EAC5D;AAEA,SAAO;AACT;;;AC1Be,SAAR,mBAAoC,QAAQ;AAClD,MAAI,OAAO,WAAW,UAAU;AAC/B,UAAM,IAAI,UAAU,mBAAmB;AAAA,EACxC;AAIA,SAAO,OACL,QAAQ,uBAAuB,MAAM,EACrC,QAAQ,MAAM,OAAO;AACxB;;;ACmEO,SAAS,eAAe,MAAMA,OAAM,SAAS;AAClD,QAAM,WAAW,WAAW,CAAC;AAC7B,QAAM,UAAU,QAAQ,SAAS,UAAU,CAAC,CAAC;AAC7C,QAAM,QAAQ,QAAQA,KAAI;AAC1B,MAAI,YAAY;AAEhB,SAAO,EAAE,YAAY,MAAM,QAAQ;AACjC,iBAAa,MAAM,QAAQ,OAAO;AAAA,EACpC;AAGA,WAAS,QAAQ,MAAM,SAAS;AAC9B,QAAI,QAAQ;AAEZ,QAAI;AAEJ,WAAO,EAAE,QAAQ,QAAQ,QAAQ;AAC/B,YAAM,SAAS,QAAQ,KAAK;AAE5B,YAAM,WAAW,cAAc,YAAY,WAAW;AAEtD,UACE;AAAA,QACE;AAAA,QACA,WAAW,SAAS,QAAQ,MAAM,IAAI;AAAA,QACtC;AAAA,MACF,GACA;AACA;AAAA,MACF;AAEA,oBAAc;AAAA,IAChB;AAEA,QAAI,aAAa;AACf,aAAO,QAAQ,MAAM,OAAO;AAAA,IAC9B;AAAA,EACF;AAYA,WAAS,QAAQ,MAAM,SAAS;AAC9B,UAAM,SAAS,QAAQ,QAAQ,SAAS,CAAC;AACzC,UAAM,OAAO,MAAM,SAAS,EAAE,CAAC;AAC/B,UAAMC,WAAU,MAAM,SAAS,EAAE,CAAC;AAClC,QAAI,QAAQ;AAEZ,UAAM,WAAW,OAAO;AACxB,UAAM,QAAQ,SAAS,QAAQ,IAAI;AACnC,QAAI,SAAS;AAEb,QAAI,QAAQ,CAAC;AAEb,SAAK,YAAY;AAEjB,QAAI,QAAQ,KAAK,KAAK,KAAK,KAAK;AAEhC,WAAO,OAAO;AACZ,YAAM,WAAW,MAAM;AAEvB,YAAM,cAAc;AAAA,QAClB,OAAO,MAAM;AAAA,QACb,OAAO,MAAM;AAAA,QACb,OAAO,CAAC,GAAG,SAAS,IAAI;AAAA,MAC1B;AACA,UAAI,QAAQA,SAAQ,GAAG,OAAO,WAAW;AAEzC,UAAI,OAAO,UAAU,UAAU;AAC7B,gBAAQ,MAAM,SAAS,IAAI,EAAC,MAAM,QAAQ,MAAK,IAAI;AAAA,MACrD;AAGA,UAAI,UAAU,OAAO;AAInB,aAAK,YAAY,WAAW;AAAA,MAC9B,OAAO;AACL,YAAI,UAAU,UAAU;AACtB,gBAAM,KAAK;AAAA,YACT,MAAM;AAAA,YACN,OAAO,KAAK,MAAM,MAAM,OAAO,QAAQ;AAAA,UACzC,CAAC;AAAA,QACH;AAEA,YAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,gBAAM,KAAK,GAAG,KAAK;AAAA,QACrB,WAAW,OAAO;AAChB,gBAAM,KAAK,KAAK;AAAA,QAClB;AAEA,gBAAQ,WAAW,MAAM,CAAC,EAAE;AAC5B,iBAAS;AAAA,MACX;AAEA,UAAI,CAAC,KAAK,QAAQ;AAChB;AAAA,MACF;AAEA,cAAQ,KAAK,KAAK,KAAK,KAAK;AAAA,IAC9B;AAEA,QAAI,QAAQ;AACV,UAAI,QAAQ,KAAK,MAAM,QAAQ;AAC7B,cAAM,KAAK,EAAC,MAAM,QAAQ,OAAO,KAAK,MAAM,MAAM,KAAK,EAAC,CAAC;AAAA,MAC3D;AAEA,aAAO,SAAS,OAAO,OAAO,GAAG,GAAG,KAAK;AAAA,IAC3C,OAAO;AACL,cAAQ,CAAC,IAAI;AAAA,IACf;AAEA,WAAO,QAAQ,MAAM;AAAA,EACvB;AACF;AAUA,SAAS,QAAQ,aAAa;AAE5B,QAAM,SAAS,CAAC;AAEhB,MAAI,CAAC,MAAM,QAAQ,WAAW,GAAG;AAC/B,UAAM,IAAI,UAAU,mDAAmD;AAAA,EACzE;AAIA,QAAMD,QACJ,CAAC,YAAY,CAAC,KAAK,MAAM,QAAQ,YAAY,CAAC,CAAC,IAC3C,cACA,CAAC,WAAW;AAElB,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQA,MAAK,QAAQ;AAC5B,UAAM,QAAQA,MAAK,KAAK;AACxB,WAAO,KAAK,CAAC,aAAa,MAAM,CAAC,CAAC,GAAG,WAAW,MAAM,CAAC,CAAC,CAAC,CAAC;AAAA,EAC5D;AAEA,SAAO;AACT;AAUA,SAAS,aAAa,MAAM;AAC1B,SAAO,OAAO,SAAS,WAAW,IAAI,OAAO,mBAAO,IAAI,GAAG,GAAG,IAAI;AACpE;AAUA,SAAS,WAAWC,UAAS;AAC3B,SAAO,OAAOA,aAAY,aACtBA,WACA,WAAY;AACV,WAAOA;AAAA,EACT;AACN;;;ACvPA,IAAM,cAAc;AAEpB,IAAM,iBAAiB,CAAC,YAAY,QAAQ,SAAS,OAAO;AASrD,SAAS,iCAAiC;AAC/C,SAAO;AAAA,IACL,YAAY,CAAC,4BAA4B;AAAA,IACzC,OAAO;AAAA,MACL,iBAAiB;AAAA,MACjB,sBAAsB;AAAA,MACtB,qBAAqB;AAAA,MACrB,oBAAoB;AAAA,IACtB;AAAA,IACA,MAAM;AAAA,MACJ,iBAAiB;AAAA,MACjB,sBAAsB;AAAA,MACtB,qBAAqB;AAAA,MACrB,oBAAoB;AAAA,IACtB;AAAA,EACF;AACF;AASO,SAAS,+BAA+B;AAC7C,SAAO;AAAA,IACL,QAAQ;AAAA,MACN;AAAA,QACE,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,OAAO;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA;AAAA,QACE,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,OAAO;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,MACA;AAAA,QACE,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,OAAO;AAAA,QACP;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAMA,SAAS,qBAAqB,OAAO;AACnC,OAAK,MAAM,EAAC,MAAM,QAAQ,OAAO,MAAM,KAAK,IAAI,UAAU,CAAC,EAAC,GAAG,KAAK;AACtE;AAMA,SAAS,0BAA0B,OAAO;AACxC,OAAK,OAAO,MAAM,iBAAiB,KAAK,MAAM,KAAK;AACrD;AAMA,SAAS,wBAAwB,OAAO;AACtC,OAAK,OAAO,KAAK,iBAAiB,KAAK,MAAM,KAAK;AACpD;AAMA,SAAS,uBAAuB,OAAO;AACrC,OAAK,OAAO,KAAK,KAAK,KAAK,MAAM,KAAK;AACtC,QAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,KAAO,KAAK,SAAS,MAAM;AAC3B,OAAK,MAAM,YAAY,KAAK,eAAe,KAAK;AAClD;AAMA,SAAS,yBAAyB,OAAO;AACvC,OAAK,OAAO,KAAK,cAAc,KAAK,MAAM,KAAK;AACjD;AAMA,SAAS,oBAAoB,OAAO;AAClC,OAAK,KAAK,KAAK;AACjB;AAGA,SAAS,6BAA6B,MAAM;AAC1C;AAAA,IACE;AAAA,IACA;AAAA,MACE,CAAC,mDAAmD,OAAO;AAAA,MAC3D,CAAC,2DAA2D,SAAS;AAAA,IACvE;AAAA,IACA,EAAC,QAAQ,CAAC,QAAQ,eAAe,EAAC;AAAA,EACpC;AACF;AAYA,SAAS,QAAQ,GAAG,UAAUC,SAAQC,OAAM,OAAO;AACjD,MAAI,SAAS;AAGb,MAAI,CAAC,SAAS,KAAK,GAAG;AACpB,WAAO;AAAA,EACT;AAGA,MAAI,MAAM,KAAK,QAAQ,GAAG;AACxB,IAAAD,UAAS,WAAWA;AACpB,eAAW;AACX,aAAS;AAAA,EACX;AAEA,MAAI,CAAC,gBAAgBA,OAAM,GAAG;AAC5B,WAAO;AAAA,EACT;AAEA,QAAM,QAAQ,SAASA,UAASC,KAAI;AAEpC,MAAI,CAAC,MAAM,CAAC,EAAG,QAAO;AAGtB,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK,SAAS,WAAW,MAAM,CAAC;AAAA,IAChC,UAAU,CAAC,EAAC,MAAM,QAAQ,OAAO,WAAW,MAAM,CAAC,EAAC,CAAC;AAAA,EACvD;AAEA,MAAI,MAAM,CAAC,GAAG;AACZ,WAAO,CAAC,QAAQ,EAAC,MAAM,QAAQ,OAAO,MAAM,CAAC,EAAC,CAAC;AAAA,EACjD;AAEA,SAAO;AACT;AAUA,SAAS,UAAU,GAAG,OAAO,OAAO,OAAO;AACzC;AAAA;AAAA,IAEE,CAAC,SAAS,OAAO,IAAI;AAAA,IAErB,UAAU,KAAK,KAAK;AAAA,IACpB;AACA,WAAO;AAAA,EACT;AAEA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK,YAAY,QAAQ,MAAM;AAAA,IAC/B,UAAU,CAAC,EAAC,MAAM,QAAQ,OAAO,QAAQ,MAAM,MAAK,CAAC;AAAA,EACvD;AACF;AAMA,SAAS,gBAAgBD,SAAQ;AAC/B,QAAM,QAAQA,QAAO,MAAM,GAAG;AAE9B,MACE,MAAM,SAAS,KACd,MAAM,MAAM,SAAS,CAAC,MACpB,IAAI,KAAK,MAAM,MAAM,SAAS,CAAC,CAAC,KAC/B,CAAC,aAAa,KAAK,MAAM,MAAM,SAAS,CAAC,CAAC,MAC7C,MAAM,MAAM,SAAS,CAAC,MACpB,IAAI,KAAK,MAAM,MAAM,SAAS,CAAC,CAAC,KAC/B,CAAC,aAAa,KAAK,MAAM,MAAM,SAAS,CAAC,CAAC,IAC9C;AACA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAMA,SAAS,SAAS,KAAK;AACrB,QAAM,YAAY,sBAAsB,KAAK,GAAG;AAEhD,MAAI,CAAC,WAAW;AACd,WAAO,CAAC,KAAK,MAAS;AAAA,EACxB;AAEA,QAAM,IAAI,MAAM,GAAG,UAAU,KAAK;AAElC,MAAIE,SAAQ,UAAU,CAAC;AACvB,MAAI,oBAAoBA,OAAM,QAAQ,GAAG;AACzC,QAAM,gBAAgB,OAAO,KAAK,GAAG;AACrC,MAAI,gBAAgB,OAAO,KAAK,GAAG;AAEnC,SAAO,sBAAsB,MAAM,gBAAgB,eAAe;AAChE,WAAOA,OAAM,MAAM,GAAG,oBAAoB,CAAC;AAC3C,IAAAA,SAAQA,OAAM,MAAM,oBAAoB,CAAC;AACzC,wBAAoBA,OAAM,QAAQ,GAAG;AACrC;AAAA,EACF;AAEA,SAAO,CAAC,KAAKA,MAAK;AACpB;AAOA,SAAS,SAAS,OAAO,OAAO;AAC9B,QAAMC,QAAO,MAAM,MAAM,WAAW,MAAM,QAAQ,CAAC;AAEnD,UACG,MAAM,UAAU,KACf,kBAAkBA,KAAI,KACtB,mBAAmBA,KAAI;AAAA,GAExB,CAAC,SAASA,UAAS;AAExB;;;ACrQA,kBAAkB,OAAO;AAMzB,SAAS,0BAA0B;AACjC,OAAK,OAAO;AACd;AAMA,SAAS,kBAAkB,OAAO;AAChC,OAAK,MAAM,EAAC,MAAM,qBAAqB,YAAY,IAAI,OAAO,GAAE,GAAG,KAAK;AAC1E;AAMA,SAAS,qCAAqC;AAC5C,OAAK,OAAO;AACd;AAMA,SAAS,wBAAwB,OAAO;AACtC,OAAK;AAAA,IACH,EAAC,MAAM,sBAAsB,YAAY,IAAI,OAAO,IAAI,UAAU,CAAC,EAAC;AAAA,IACpE;AAAA,EACF;AACF;AAMA,SAAS,uBAAuB,OAAO;AACrC,QAAM,QAAQ,KAAK,OAAO;AAC1B,QAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,KAAO,KAAK,SAAS,mBAAmB;AACxC,OAAK,aAAa;AAAA,IAChB,KAAK,eAAe,KAAK;AAAA,EAC3B,EAAE,YAAY;AACd,OAAK,QAAQ;AACf;AAMA,SAAS,iBAAiB,OAAO;AAC/B,OAAK,KAAK,KAAK;AACjB;AAMA,SAAS,kCAAkC,OAAO;AAChD,QAAM,QAAQ,KAAK,OAAO;AAC1B,QAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,KAAO,KAAK,SAAS,oBAAoB;AACzC,OAAK,aAAa;AAAA,IAChB,KAAK,eAAe,KAAK;AAAA,EAC3B,EAAE,YAAY;AACd,OAAK,QAAQ;AACf;AAMA,SAAS,uBAAuB,OAAO;AACrC,OAAK,KAAK,KAAK;AACjB;AAGA,SAAS,wBAAwB;AAC/B,SAAO;AACT;AAMA,SAAS,kBAAkB,MAAM,GAAG,OAAO,MAAM;AAC/C,QAAM,UAAU,MAAM,cAAc,IAAI;AACxC,MAAI,QAAQ,QAAQ,KAAK,IAAI;AAC7B,QAAMC,QAAO,MAAM,MAAM,mBAAmB;AAC5C,QAAM,UAAU,MAAM,MAAM,WAAW;AACvC,WAAS,QAAQ;AAAA,IACf,MAAM,KAAK,MAAM,cAAc,IAAI,GAAG,EAAC,OAAO,KAAK,QAAQ,MAAK,CAAC;AAAA,EACnE;AACA,UAAQ;AACR,EAAAA,MAAK;AACL,WAAS,QAAQ,KAAK,GAAG;AACzB,SAAO;AACT;AASO,SAAS,0BAA0B;AACxC,SAAO;AAAA,IACL,OAAO;AAAA,MACL,uBAAuB;AAAA,MACvB,iBAAiB;AAAA,MACjB,kCAAkC;AAAA,MAClC,uBAAuB;AAAA,IACzB;AAAA,IACA,MAAM;AAAA,MACJ,uBAAuB;AAAA,MACvB,iBAAiB;AAAA,MACjB,kCAAkC;AAAA,MAClC,uBAAuB;AAAA,IACzB;AAAA,EACF;AACF;AAWO,SAAS,sBAAsB,SAAS;AAE7C,MAAI,iBAAiB;AAErB,MAAI,WAAW,QAAQ,gBAAgB;AACrC,qBAAiB;AAAA,EACnB;AAEA,SAAO;AAAA,IACL,UAAU,EAAC,oBAAoB,kBAAiB;AAAA;AAAA,IAEhD,QAAQ,CAAC,EAAC,WAAW,KAAK,aAAa,CAAC,SAAS,YAAY,WAAW,EAAC,CAAC;AAAA,EAC5E;AAMA,WAAS,mBAAmB,MAAM,GAAG,OAAO,MAAM;AAChD,UAAM,UAAU,MAAM,cAAc,IAAI;AACxC,QAAI,QAAQ,QAAQ,KAAK,IAAI;AAC7B,UAAMA,QAAO,MAAM,MAAM,oBAAoB;AAC7C,UAAM,UAAU,MAAM,MAAM,OAAO;AACnC,aAAS,QAAQ;AAAA,MACf,MAAM,KAAK,MAAM,cAAc,IAAI,GAAG,EAAC,QAAQ,OAAO,OAAO,IAAG,CAAC;AAAA,IACnE;AACA,YAAQ;AAER,aAAS,QAAQ,KAAK,IAAI;AAE1B,QAAI,KAAK,YAAY,KAAK,SAAS,SAAS,GAAG;AAC7C,cAAQ,MAAM,CAAC;AAEf,eAAS,QAAQ;AAAA,SACd,iBAAiB,OAAO,OACvB,MAAM;AAAA,UACJ,MAAM,cAAc,MAAM,QAAQ,QAAQ,CAAC;AAAA,UAC3C,iBAAiB,SAAS;AAAA,QAC5B;AAAA,MACJ;AAAA,IACF;AAEA,IAAAA,MAAK;AAEL,WAAO;AAAA,EACT;AACF;AAGA,SAAS,eAAe,MAAM,OAAO,OAAO;AAC1C,SAAO,UAAU,IAAI,OAAO,OAAO,MAAM,OAAO,KAAK;AACvD;AAGA,SAAS,OAAO,MAAM,OAAO,OAAO;AAClC,UAAQ,QAAQ,KAAK,UAAU;AACjC;;;AC7LA,IAAM,iCAAiC;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,aAAa,OAAO;AASb,SAAS,+BAA+B;AAC7C,SAAO;AAAA,IACL,gBAAgB,CAAC,QAAQ;AAAA,IACzB,OAAO,EAAC,eAAe,mBAAkB;AAAA,IACzC,MAAM,EAAC,eAAe,kBAAiB;AAAA,EACzC;AACF;AASO,SAAS,6BAA6B;AAC3C,SAAO;AAAA,IACL,QAAQ;AAAA,MACN;AAAA,QACE,WAAW;AAAA,QACX,aAAa;AAAA,QACb,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,IACA,UAAU,EAAC,QAAQ,aAAY;AAAA,EACjC;AACF;AAMA,SAAS,mBAAmB,OAAO;AACjC,OAAK,MAAM,EAAC,MAAM,UAAU,UAAU,CAAC,EAAC,GAAG,KAAK;AAClD;AAMA,SAAS,kBAAkB,OAAO;AAChC,OAAK,KAAK,KAAK;AACjB;AAMA,SAAS,aAAa,MAAM,GAAG,OAAO,MAAM;AAC1C,QAAM,UAAU,MAAM,cAAc,IAAI;AACxC,QAAMC,QAAO,MAAM,MAAM,eAAe;AACxC,MAAI,QAAQ,QAAQ,KAAK,IAAI;AAC7B,WAAS,MAAM,kBAAkB,MAAM;AAAA,IACrC,GAAG,QAAQ,QAAQ;AAAA,IACnB,QAAQ;AAAA,IACR,OAAO;AAAA,EACT,CAAC;AACD,WAAS,QAAQ,KAAK,IAAI;AAC1B,EAAAA,MAAK;AACL,SAAO;AACT;AAGA,SAAS,aAAa;AACpB,SAAO;AACT;;;ACgDA,SAAS,oBAAoB,OAAO;AAClC,SAAO,MAAM;AACf;AAcO,SAAS,cAAc,OAAO,SAAS;AAC5C,QAAM,WAAW,WAAW,CAAC;AAE7B,QAAM,SAAS,SAAS,SAAS,CAAC,GAAG,OAAO;AAC5C,QAAM,eAAe,SAAS,gBAAgB;AAE9C,QAAM,aAAa,CAAC;AAEpB,QAAM,aAAa,CAAC;AAEpB,QAAM,aAAa,CAAC;AAEpB,QAAM,sBAAsB,CAAC;AAC7B,MAAI,kBAAkB;AACtB,MAAI,WAAW;AAIf,SAAO,EAAE,WAAW,MAAM,QAAQ;AAEhC,UAAMC,OAAM,CAAC;AAEb,UAAMC,SAAQ,CAAC;AACf,QAAIC,eAAc;AAElB,QAAI,MAAM,QAAQ,EAAE,SAAS,iBAAiB;AAC5C,wBAAkB,MAAM,QAAQ,EAAE;AAAA,IACpC;AAEA,WAAO,EAAEA,eAAc,MAAM,QAAQ,EAAE,QAAQ;AAC7C,YAAM,OAAO,UAAU,MAAM,QAAQ,EAAEA,YAAW,CAAC;AAEnD,UAAI,SAAS,oBAAoB,OAAO;AACtC,cAAM,OAAO,aAAa,IAAI;AAC9B,QAAAD,OAAMC,YAAW,IAAI;AAErB,YACE,oBAAoBA,YAAW,MAAM,UACrC,OAAO,oBAAoBA,YAAW,GACtC;AACA,8BAAoBA,YAAW,IAAI;AAAA,QACrC;AAAA,MACF;AAEA,MAAAF,KAAI,KAAK,IAAI;AAAA,IACf;AAEA,eAAW,QAAQ,IAAIA;AACvB,eAAW,QAAQ,IAAIC;AAAA,EACzB;AAGA,MAAI,cAAc;AAElB,MAAI,OAAO,UAAU,YAAY,YAAY,OAAO;AAClD,WAAO,EAAE,cAAc,iBAAiB;AACtC,iBAAW,WAAW,IAAI,YAAY,MAAM,WAAW,CAAC;AAAA,IAC1D;AAAA,EACF,OAAO;AACL,UAAME,QAAO,YAAY,KAAK;AAE9B,WAAO,EAAE,cAAc,iBAAiB;AACtC,iBAAW,WAAW,IAAIA;AAAA,IAC5B;AAAA,EACF;AAGA,gBAAc;AAEd,QAAM,MAAM,CAAC;AAEb,QAAM,QAAQ,CAAC;AAEf,SAAO,EAAE,cAAc,iBAAiB;AACtC,UAAMA,QAAO,WAAW,WAAW;AACnC,QAAI,SAAS;AACb,QAAI,QAAQ;AAEZ,QAAIA,UAAS,IAAc;AACzB,eAAS;AACT,cAAQ;AAAA,IACV,WAAWA,UAAS,KAAe;AACjC,eAAS;AAAA,IACX,WAAWA,UAAS,KAAe;AACjC,cAAQ;AAAA,IACV;AAGA,QAAI,OACF,SAAS,oBAAoB,QACzB,IACA,KAAK;AAAA,MACH;AAAA,MACA,oBAAoB,WAAW,IAAI,OAAO,SAAS,MAAM;AAAA,IAC3D;AAEN,UAAM,OAAO,SAAS,IAAI,OAAO,IAAI,IAAI;AAEzC,QAAI,SAAS,oBAAoB,OAAO;AACtC,aAAO,OAAO,SAAS,OAAO,MAAM;AAEpC,UAAI,OAAO,oBAAoB,WAAW,GAAG;AAC3C,4BAAoB,WAAW,IAAI;AAAA,MACrC;AAEA,YAAM,WAAW,IAAI;AAAA,IACvB;AAEA,QAAI,WAAW,IAAI;AAAA,EACrB;AAGA,aAAW,OAAO,GAAG,GAAG,GAAG;AAC3B,aAAW,OAAO,GAAG,GAAG,KAAK;AAE7B,aAAW;AAEX,QAAM,QAAQ,CAAC;AAEf,SAAO,EAAE,WAAW,WAAW,QAAQ;AACrC,UAAMH,OAAM,WAAW,QAAQ;AAC/B,UAAMC,SAAQ,WAAW,QAAQ;AACjC,kBAAc;AAEd,UAAM,OAAO,CAAC;AAEd,WAAO,EAAE,cAAc,iBAAiB;AACtC,YAAM,OAAOD,KAAI,WAAW,KAAK;AACjC,UAAI,SAAS;AACb,UAAI,QAAQ;AAEZ,UAAI,SAAS,oBAAoB,OAAO;AACtC,cAAM,OACJ,oBAAoB,WAAW,KAAKC,OAAM,WAAW,KAAK;AAC5D,cAAME,QAAO,WAAW,WAAW;AAEnC,YAAIA,UAAS,KAAe;AAC1B,mBAAS,IAAI,OAAO,IAAI;AAAA,QAC1B,WAAWA,UAAS,IAAc;AAChC,cAAI,OAAO,GAAG;AACZ,qBAAS,IAAI,OAAO,OAAO,IAAI,GAAG;AAClC,oBAAQ,IAAI,OAAO,OAAO,IAAI,GAAG;AAAA,UACnC,OAAO;AACL,qBAAS,IAAI,OAAO,OAAO,CAAC;AAC5B,oBAAQ;AAAA,UACV;AAAA,QACF,OAAO;AACL,kBAAQ,IAAI,OAAO,IAAI;AAAA,QACzB;AAAA,MACF;AAEA,UAAI,SAAS,mBAAmB,SAAS,CAAC,aAAa;AACrD,aAAK,KAAK,GAAG;AAAA,MACf;AAEA,UACE,SAAS,YAAY;AAAA;AAAA,MAGrB,EAAE,SAAS,oBAAoB,SAAS,SAAS,QAChD,SAAS,mBAAmB,SAAS,cACtC;AACA,aAAK,KAAK,GAAG;AAAA,MACf;AAEA,UAAI,SAAS,oBAAoB,OAAO;AACtC,aAAK,KAAK,MAAM;AAAA,MAClB;AAEA,WAAK,KAAK,IAAI;AAEd,UAAI,SAAS,oBAAoB,OAAO;AACtC,aAAK,KAAK,KAAK;AAAA,MACjB;AAEA,UAAI,SAAS,YAAY,OAAO;AAC9B,aAAK,KAAK,GAAG;AAAA,MACf;AAEA,UACE,SAAS,iBAAiB,SAC1B,gBAAgB,kBAAkB,GAClC;AACA,aAAK,KAAK,GAAG;AAAA,MACf;AAAA,IACF;AAEA,UAAM;AAAA,MACJ,SAAS,iBAAiB,QACtB,KAAK,KAAK,EAAE,EAAE,QAAQ,OAAO,EAAE,IAC/B,KAAK,KAAK,EAAE;AAAA,IAClB;AAAA,EACF;AAEA,SAAO,MAAM,KAAK,IAAI;AACxB;AAQA,SAAS,UAAU,OAAO;AACxB,SAAO,UAAU,QAAQ,UAAU,SAAY,KAAK,OAAO,KAAK;AAClE;AAQA,SAAS,YAAY,OAAO;AAC1B,QAAMA,QAAO,OAAO,UAAU,WAAW,MAAM,YAAY,CAAC,IAAI;AAEhE,SAAOA,UAAS,MAAgBA,UAAS,KACrC,KACAA,UAAS,MAAgBA,UAAS,MAChC,MACAA,UAAS,MAAgBA,UAAS,MAChC,MACA;AACV;;;ACvVA,IAAM,MAAM,CAAC,EAAE;;;AC7Cf,IAAMC,OAAM,CAAC,EAAE;;;ACQR,SAAS,WAAW,MAAM,GAAG,OAAO,MAAM;AAC/C,QAAMC,QAAO,MAAM,MAAM,YAAY;AACrC,QAAM,UAAU,MAAM,cAAc,IAAI;AACxC,UAAQ,KAAK,IAAI;AACjB,UAAQ,MAAM,CAAC;AACf,QAAM,QAAQ,MAAM;AAAA,IAClB,MAAM,cAAc,MAAM,QAAQ,QAAQ,CAAC;AAAA,IAC3C;AAAA,EACF;AACA,EAAAA,MAAK;AACL,SAAO;AACT;AAGA,SAAS,IAAI,MAAM,GAAG,OAAO;AAC3B,SAAO,OAAO,QAAQ,KAAK,OAAO;AACpC;;;ACnBO,SAAS,eAAe,OAAO,SAAS;AAC7C,SACE,YAAY,OAAO,QAAQ,aAAa,IAAI,KAC5C,CAAC,YAAY,OAAO,QAAQ,gBAAgB,KAAK;AAErD;AAQA,SAAS,YAAY,OAAOC,OAAM,MAAM;AACtC,MAAI,OAAOA,UAAS,UAAU;AAC5B,IAAAA,QAAO,CAACA,KAAI;AAAA,EACd;AAEA,MAAI,CAACA,SAAQA,MAAK,WAAW,GAAG;AAC9B,WAAO;AAAA,EACT;AAEA,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQA,MAAK,QAAQ;AAC5B,QAAI,MAAM,SAASA,MAAK,KAAK,CAAC,GAAG;AAC/B,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;;;AC1BO,SAAS,UAAU,GAAG,IAAI,OAAO,MAAM;AAC5C,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,MAAM,OAAO,QAAQ;AAGpC,QACE,MAAM,OAAO,KAAK,EAAE,cAAc,QAClC,eAAe,MAAM,OAAO,MAAM,OAAO,KAAK,CAAC,GAC/C;AACA,aAAO,QAAQ,KAAK,KAAK,MAAM,IAAI,KAAK;AAAA,IAC1C;AAAA,EACF;AAEA,SAAO;AACT;;;ACnBO,SAAS,cAAc,OAAO,WAAW;AAC9C,QAAM,SAAS,OAAO,KAAK;AAC3B,MAAI,QAAQ,OAAO,QAAQ,SAAS;AACpC,MAAI,WAAW;AACf,MAAI,QAAQ;AACZ,MAAI,MAAM;AAEV,MAAI,OAAO,cAAc,UAAU;AACjC,UAAM,IAAI,UAAU,oBAAoB;AAAA,EAC1C;AAEA,SAAO,UAAU,IAAI;AACnB,QAAI,UAAU,UAAU;AACtB,UAAI,EAAE,QAAQ,KAAK;AACjB,cAAM;AAAA,MACR;AAAA,IACF,OAAO;AACL,cAAQ;AAAA,IACV;AAEA,eAAW,QAAQ,UAAU;AAC7B,YAAQ,OAAO,QAAQ,WAAW,QAAQ;AAAA,EAC5C;AAEA,SAAO;AACT;;;ACzBO,SAAS,qBAAqB,MAAM,OAAO;AAChD,SAAO;AAAA,IACL,MAAM,QAAQ,WAAW,SACvB,KAAK;AAAA,IAEL,CAAC,KAAK;AAAA,IAEN,WAAW,KAAK,KAAK,KAAK;AAAA,IAE1B,CAAC,0CAA0C,KAAK,KAAK,KAAK;AAAA,EAC9D;AACF;;;ACbO,SAAS,WAAW,OAAO;AAChC,QAAM,SAAS,MAAM,QAAQ,SAAS;AAEtC,MAAI,WAAW,OAAO,WAAW,KAAK;AACpC,UAAM,IAAI;AAAA,MACR,iCACE,SACA;AAAA,IACJ;AAAA,EACF;AAEA,SAAO;AACT;;;ACJO,SAAS,KAAK,MAAM,GAAG,OAAO,MAAM;AACzC,QAAM,SAAS,WAAW,KAAK;AAC/B,QAAM,MAAM,KAAK,SAAS;AAC1B,QAAM,SAAS,WAAW,MAAM,gBAAgB;AAEhD,MAAI,qBAAqB,MAAM,KAAK,GAAG;AACrC,UAAMC,QAAO,MAAM,MAAM,cAAc;AACvC,UAAMC,SAAQ,MAAM,YAAY,KAAKC,IAAG;AACxC,IAAAF,MAAK;AACL,WAAOC;AAAA,EACT;AAEA,QAAM,UAAU,MAAM,cAAc,IAAI;AACxC,QAAM,WAAW,OAAO,OAAO,KAAK,IAAI,cAAc,KAAK,MAAM,IAAI,GAAG,CAAC,CAAC;AAC1E,QAAMD,QAAO,MAAM,MAAM,YAAY;AACrC,MAAI,QAAQ,QAAQ,KAAK,QAAQ;AAEjC,MAAI,KAAK,MAAM;AACb,UAAM,UAAU,MAAM,MAAM,iBAAiB,MAAM,EAAE;AACrD,aAAS,QAAQ;AAAA,MACf,MAAM,KAAK,KAAK,MAAM;AAAA,QACpB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,QAAQ,CAAC,GAAG;AAAA,QACZ,GAAG,QAAQ,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH;AACA,YAAQ;AAAA,EACV;AAEA,MAAI,KAAK,QAAQ,KAAK,MAAM;AAC1B,UAAM,UAAU,MAAM,MAAM,iBAAiB,MAAM,EAAE;AACrD,aAAS,QAAQ,KAAK,GAAG;AACzB,aAAS,QAAQ;AAAA,MACf,MAAM,KAAK,KAAK,MAAM;AAAA,QACpB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,QAAQ,CAAC,GAAG;AAAA,QACZ,GAAG,QAAQ,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH;AACA,YAAQ;AAAA,EACV;AAEA,WAAS,QAAQ,KAAK,IAAI;AAE1B,MAAI,KAAK;AACP,aAAS,QAAQ,KAAK,MAAM,IAAI;AAAA,EAClC;AAEA,WAAS,QAAQ,KAAK,QAAQ;AAC9B,EAAAA,MAAK;AACL,SAAO;AACT;AAGA,SAASE,KAAI,MAAM,GAAG,OAAO;AAC3B,UAAQ,QAAQ,KAAK,UAAU;AACjC;;;AClEO,SAAS,WAAW,OAAO;AAChC,QAAM,SAAS,MAAM,QAAQ,SAAS;AAEtC,MAAI,WAAW,OAAO,WAAW,KAAK;AACpC,UAAM,IAAI;AAAA,MACR,kCACE,SACA;AAAA,IACJ;AAAA,EACF;AAEA,SAAO;AACT;;;ACNO,SAAS,WAAW,MAAM,GAAG,OAAO,MAAM;AAC/C,QAAM,QAAQ,WAAW,KAAK;AAC9B,QAAM,SAAS,UAAU,MAAM,UAAU;AACzC,QAAMC,QAAO,MAAM,MAAM,YAAY;AACrC,MAAI,UAAU,MAAM,MAAM,OAAO;AACjC,QAAM,UAAU,MAAM,cAAc,IAAI;AACxC,MAAI,QAAQ,QAAQ,KAAK,GAAG;AAC5B,WAAS,QAAQ;AAAA,IACf,MAAM,KAAK,MAAM,cAAc,IAAI,GAAG;AAAA,MACpC,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,GAAG,QAAQ,QAAQ;AAAA,IACrB,CAAC;AAAA,EACH;AACA,WAAS,QAAQ,KAAK,KAAK;AAE3B,UAAQ;AAER;AAAA;AAAA,IAEE,CAAC,KAAK;AAAA,IAEN,eAAe,KAAK,KAAK,GAAG;AAAA,IAC5B;AACA,cAAU,MAAM,MAAM,oBAAoB;AAC1C,aAAS,QAAQ,KAAK,GAAG;AACzB,aAAS,QAAQ;AAAA,MACf,MAAM,KAAK,KAAK,KAAK,EAAC,QAAQ,OAAO,OAAO,KAAK,GAAG,QAAQ,QAAQ,EAAC,CAAC;AAAA,IACxE;AACA,aAAS,QAAQ,KAAK,GAAG;AAAA,EAC3B,OAAO;AAEL,cAAU,MAAM,MAAM,gBAAgB;AACtC,aAAS,QAAQ;AAAA,MACf,MAAM,KAAK,KAAK,KAAK;AAAA,QACnB,QAAQ;AAAA,QACR,OAAO,KAAK,QAAQ,MAAM;AAAA,QAC1B,GAAG,QAAQ,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH;AAAA,EACF;AAEA,UAAQ;AAER,MAAI,KAAK,OAAO;AACd,cAAU,MAAM,MAAM,QAAQ,MAAM,EAAE;AACtC,aAAS,QAAQ,KAAK,MAAM,KAAK;AACjC,aAAS,QAAQ;AAAA,MACf,MAAM,KAAK,KAAK,OAAO;AAAA,QACrB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,GAAG,QAAQ,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH;AACA,aAAS,QAAQ,KAAK,KAAK;AAC3B,YAAQ;AAAA,EACV;AAEA,EAAAA,MAAK;AAEL,SAAO;AACT;;;ACnEO,SAAS,cAAc,OAAO;AACnC,QAAM,SAAS,MAAM,QAAQ,YAAY;AAEzC,MAAI,WAAW,OAAO,WAAW,KAAK;AACpC,UAAM,IAAI;AAAA,MACR,qCACE,SACA;AAAA,IACJ;AAAA,EACF;AAEA,SAAO;AACT;;;ACZO,SAAS,yBAAyBC,OAAM;AAC7C,SAAO,QAAQA,MAAK,SAAS,EAAE,EAAE,YAAY,IAAI;AACnD;;;AC8BO,SAAS,WAAW,SAAS,QAAQ,QAAQ;AAClD,QAAM,cAAc,kBAAkB,OAAO;AAC7C,QAAM,aAAa,kBAAkB,MAAM;AAG3C,MAAI,gBAAgB,QAAW;AAC7B,WAAO,eAAe;AAAA;AAAA;AAAA;AAAA,MAIlB,WAAW,MACT,EAAC,QAAQ,MAAM,SAAS,KAAI,IAC5B,EAAC,QAAQ,OAAO,SAAS,MAAK;AAAA,QAChC,eAAe;AAAA;AAAA,MAEb,EAAC,QAAQ,MAAM,SAAS,KAAI;AAAA;AAAA;AAAA,MAE5B,EAAC,QAAQ,OAAO,SAAS,KAAI;AAAA;AAAA,EACrC;AAGA,MAAI,gBAAgB,GAAG;AACrB,WAAO,eAAe;AAAA;AAAA,MAElB,EAAC,QAAQ,OAAO,SAAS,MAAK;AAAA,QAC9B,eAAe;AAAA;AAAA,MAEb,EAAC,QAAQ,MAAM,SAAS,KAAI;AAAA;AAAA;AAAA,MAE5B,EAAC,QAAQ,OAAO,SAAS,MAAK;AAAA;AAAA,EACtC;AAGA,SAAO,eAAe;AAAA;AAAA,IAElB,EAAC,QAAQ,OAAO,SAAS,MAAK;AAAA,MAC9B,eAAe;AAAA;AAAA,IAEb,EAAC,QAAQ,MAAM,SAAS,MAAK;AAAA;AAAA;AAAA,IAE7B,EAAC,QAAQ,OAAO,SAAS,MAAK;AAAA;AACtC;;;ACxEA,SAAS,OAAO;AAST,SAAS,SAAS,MAAM,GAAG,OAAO,MAAM;AAC7C,QAAM,SAAS,cAAc,KAAK;AAClC,QAAMC,QAAO,MAAM,MAAM,UAAU;AACnC,QAAM,UAAU,MAAM,cAAc,IAAI;AACxC,QAAM,SAAS,QAAQ,KAAK,MAAM;AAElC,MAAI,UAAU,QAAQ;AAAA,IACpB,MAAM,kBAAkB,MAAM;AAAA,MAC5B,OAAO;AAAA,MACP;AAAA,MACA,GAAG,QAAQ,QAAQ;AAAA,IACrB,CAAC;AAAA,EACH;AACA,QAAM,cAAc,QAAQ,WAAW,CAAC;AACxC,QAAM,OAAO;AAAA,IACX,KAAK,OAAO,WAAW,KAAK,OAAO,SAAS,CAAC;AAAA,IAC7C;AAAA,IACA;AAAA,EACF;AAEA,MAAI,KAAK,QAAQ;AACf,cAAU,yBAAyB,WAAW,IAAI,QAAQ,MAAM,CAAC;AAAA,EACnE;AAEA,QAAM,cAAc,QAAQ,WAAW,QAAQ,SAAS,CAAC;AACzD,QAAM,QAAQ,WAAW,KAAK,MAAM,WAAW,CAAC,GAAG,aAAa,MAAM;AAEtE,MAAI,MAAM,QAAQ;AAChB,cAAU,QAAQ,MAAM,GAAG,EAAE,IAAI,yBAAyB,WAAW;AAAA,EACvE;AAEA,QAAM,QAAQ,QAAQ,KAAK,MAAM;AAEjC,EAAAA,MAAK;AAEL,QAAM,iCAAiC;AAAA,IACrC,OAAO,MAAM;AAAA,IACb,QAAQ,KAAK;AAAA,EACf;AACA,SAAO,SAAS,UAAU;AAC5B;AAQA,SAAS,aAAa,GAAG,IAAI,OAAO;AAClC,SAAO,MAAM,QAAQ,YAAY;AACnC;;;ACvDO,SAAS,sBAAsB,MAAM,OAAO;AACjD,MAAI,mBAAmB;AAIvB,QAAM,MAAM,SAAUC,OAAM;AAC1B,QACG,WAAWA,SAAQ,WAAW,KAAKA,MAAK,KAAK,KAC9CA,MAAK,SAAS,SACd;AACA,yBAAmB;AACnB,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AAED,SAAO;AAAA,KACJ,CAAC,KAAK,SAAS,KAAK,QAAQ,MAC3B,SAAS,IAAI,MACZ,MAAM,QAAQ,UAAU;AAAA,EAC7B;AACF;;;AClBO,SAAS,QAAQ,MAAM,GAAG,OAAO,MAAM;AAC5C,QAAM,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK,SAAS,CAAC,GAAG,CAAC;AACrD,QAAM,UAAU,MAAM,cAAc,IAAI;AAExC,MAAI,sBAAsB,MAAM,KAAK,GAAG;AACtC,UAAMC,QAAO,MAAM,MAAM,eAAe;AACxC,UAAMC,WAAU,MAAM,MAAM,UAAU;AACtC,UAAMC,SAAQ,MAAM,kBAAkB,MAAM;AAAA,MAC1C,GAAG,QAAQ,QAAQ;AAAA,MACnB,QAAQ;AAAA,MACR,OAAO;AAAA,IACT,CAAC;AACD,IAAAD,SAAQ;AACR,IAAAD,MAAK;AAEL,WACEE,SACA,QACC,SAAS,IAAI,MAAM,KAAK;AAAA;AAAA,MAEvBA,OAAM;AAAA;AAAA,OAGH,KAAK,IAAIA,OAAM,YAAY,IAAI,GAAGA,OAAM,YAAY,IAAI,CAAC,IAAI;AAAA,IAClE;AAAA,EAEJ;AAEA,QAAM,WAAW,IAAI,OAAO,IAAI;AAChC,QAAMF,QAAO,MAAM,MAAM,YAAY;AACrC,QAAM,UAAU,MAAM,MAAM,UAAU;AAMtC,UAAQ,KAAK,WAAW,GAAG;AAE3B,MAAI,QAAQ,MAAM,kBAAkB,MAAM;AAAA,IACxC,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,GAAG,QAAQ,QAAQ;AAAA,EACrB,CAAC;AAED,MAAI,SAAS,KAAK,KAAK,GAAG;AAExB,YAAQ,yBAAyB,MAAM,WAAW,CAAC,CAAC,IAAI,MAAM,MAAM,CAAC;AAAA,EACvE;AAEA,UAAQ,QAAQ,WAAW,MAAM,QAAQ;AAEzC,MAAI,MAAM,QAAQ,UAAU;AAC1B,aAAS,MAAM;AAAA,EACjB;AAEA,UAAQ;AACR,EAAAA,MAAK;AAEL,SAAO;AACT;;;ACtEA,KAAK,OAAO;AAML,SAAS,KAAK,MAAM;AACzB,SAAO,KAAK,SAAS;AACvB;AAKA,SAAS,WAAW;AAClB,SAAO;AACT;;;ACZA,MAAM,OAAO;AASN,SAAS,MAAM,MAAM,GAAG,OAAO,MAAM;AAC1C,QAAM,QAAQ,WAAW,KAAK;AAC9B,QAAM,SAAS,UAAU,MAAM,UAAU;AACzC,QAAMG,QAAO,MAAM,MAAM,OAAO;AAChC,MAAI,UAAU,MAAM,MAAM,OAAO;AACjC,QAAM,UAAU,MAAM,cAAc,IAAI;AACxC,MAAI,QAAQ,QAAQ,KAAK,IAAI;AAC7B,WAAS,QAAQ;AAAA,IACf,MAAM,KAAK,KAAK,KAAK,EAAC,QAAQ,OAAO,OAAO,KAAK,GAAG,QAAQ,QAAQ,EAAC,CAAC;AAAA,EACxE;AACA,WAAS,QAAQ,KAAK,IAAI;AAE1B,UAAQ;AAER;AAAA;AAAA,IAEG,CAAC,KAAK,OAAO,KAAK;AAAA,IAEnB,eAAe,KAAK,KAAK,GAAG;AAAA,IAC5B;AACA,cAAU,MAAM,MAAM,oBAAoB;AAC1C,aAAS,QAAQ,KAAK,GAAG;AACzB,aAAS,QAAQ;AAAA,MACf,MAAM,KAAK,KAAK,KAAK,EAAC,QAAQ,OAAO,OAAO,KAAK,GAAG,QAAQ,QAAQ,EAAC,CAAC;AAAA,IACxE;AACA,aAAS,QAAQ,KAAK,GAAG;AAAA,EAC3B,OAAO;AAEL,cAAU,MAAM,MAAM,gBAAgB;AACtC,aAAS,QAAQ;AAAA,MACf,MAAM,KAAK,KAAK,KAAK;AAAA,QACnB,QAAQ;AAAA,QACR,OAAO,KAAK,QAAQ,MAAM;AAAA,QAC1B,GAAG,QAAQ,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH;AAAA,EACF;AAEA,UAAQ;AAER,MAAI,KAAK,OAAO;AACd,cAAU,MAAM,MAAM,QAAQ,MAAM,EAAE;AACtC,aAAS,QAAQ,KAAK,MAAM,KAAK;AACjC,aAAS,QAAQ;AAAA,MACf,MAAM,KAAK,KAAK,OAAO;AAAA,QACrB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,GAAG,QAAQ,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH;AACA,aAAS,QAAQ,KAAK,KAAK;AAC3B,YAAQ;AAAA,EACV;AAEA,WAAS,QAAQ,KAAK,GAAG;AACzB,EAAAA,MAAK;AAEL,SAAO;AACT;AAKA,SAAS,YAAY;AACnB,SAAO;AACT;;;AC5EA,eAAe,OAAO;AASf,SAAS,eAAe,MAAM,GAAG,OAAO,MAAM;AACnD,QAAM,OAAO,KAAK;AAClB,QAAMC,QAAO,MAAM,MAAM,gBAAgB;AACzC,MAAI,UAAU,MAAM,MAAM,OAAO;AACjC,QAAM,UAAU,MAAM,cAAc,IAAI;AACxC,MAAI,QAAQ,QAAQ,KAAK,IAAI;AAC7B,QAAM,MAAM,MAAM,KAAK,KAAK,KAAK;AAAA,IAC/B,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,GAAG,QAAQ,QAAQ;AAAA,EACrB,CAAC;AACD,WAAS,QAAQ,KAAK,MAAM,IAAI;AAEhC,UAAQ;AAER,QAAM,QAAQ,MAAM;AACpB,QAAM,QAAQ,CAAC;AACf,YAAU,MAAM,MAAM,WAAW;AAKjC,QAAM,YAAY,MAAM,KAAK,MAAM,cAAc,IAAI,GAAG;AAAA,IACtD,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,GAAG,QAAQ,QAAQ;AAAA,EACrB,CAAC;AACD,UAAQ;AACR,QAAM,QAAQ;AACd,EAAAA,MAAK;AAEL,MAAI,SAAS,UAAU,CAAC,OAAO,QAAQ,WAAW;AAChD,aAAS,QAAQ,KAAK,YAAY,GAAG;AAAA,EACvC,WAAW,SAAS,YAAY;AAE9B,YAAQ,MAAM,MAAM,GAAG,EAAE;AAAA,EAC3B,OAAO;AACL,aAAS,QAAQ,KAAK,GAAG;AAAA,EAC3B;AAEA,SAAO;AACT;AAKA,SAAS,qBAAqB;AAC5B,SAAO;AACT;;;ACzDA,WAAW,OAAO;AAQX,SAAS,WAAW,MAAM,GAAG,OAAO;AACzC,MAAI,QAAQ,KAAK,SAAS;AAC1B,MAAI,WAAW;AACf,MAAI,QAAQ;AAKZ,SAAO,IAAI,OAAO,aAAa,WAAW,UAAU,EAAE,KAAK,KAAK,GAAG;AACjE,gBAAY;AAAA,EACd;AAIA,MACE,WAAW,KAAK,KAAK,MACnB,WAAW,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK,KAAM,QAAQ,KAAK,KAAK,IACzE;AACA,YAAQ,MAAM,QAAQ;AAAA,EACxB;AASA,SAAO,EAAE,QAAQ,MAAM,OAAO,QAAQ;AACpC,UAAM,UAAU,MAAM,OAAO,KAAK;AAClC,UAAM,aAAa,MAAM,eAAe,OAAO;AAE/C,QAAI;AAKJ,QAAI,CAAC,QAAQ,QAAS;AAEtB,WAAQ,QAAQ,WAAW,KAAK,KAAK,GAAI;AACvC,UAAI,WAAW,MAAM;AAGrB,UACE,MAAM,WAAW,QAAQ,MAAM,MAC/B,MAAM,WAAW,WAAW,CAAC,MAAM,IACnC;AACA;AAAA,MACF;AAEA,cAAQ,MAAM,MAAM,GAAG,QAAQ,IAAI,MAAM,MAAM,MAAM,MAAM,QAAQ,CAAC;AAAA,IACtE;AAAA,EACF;AAEA,SAAO,WAAW,QAAQ;AAC5B;AAKA,SAAS,iBAAiB;AACxB,SAAO;AACT;;;AC/DO,SAAS,qBAAqB,MAAM,OAAO;AAChD,QAAM,MAAM,SAAS,IAAI;AAEzB,SAAO;AAAA,IACL,CAAC,MAAM,QAAQ;AAAA,IAEb,KAAK;AAAA,IAEL,CAAC,KAAK;AAAA,IAEN,KAAK,YACL,KAAK,SAAS,WAAW,KACzB,KAAK,SAAS,CAAC,EAAE,SAAS;AAAA,KAEzB,QAAQ,KAAK,OAAO,YAAY,QAAQ,KAAK;AAAA,IAE9C,oBAAoB,KAAK,KAAK,GAAG;AAAA;AAAA,IAGjC,CAAC,iBAAiB,KAAK,KAAK,GAAG;AAAA,EACnC;AACF;;;ACxBA,KAAK,OAAO;AASL,SAAS,KAAK,MAAM,GAAG,OAAO,MAAM;AACzC,QAAM,QAAQ,WAAW,KAAK;AAC9B,QAAM,SAAS,UAAU,MAAM,UAAU;AACzC,QAAM,UAAU,MAAM,cAAc,IAAI;AAExC,MAAIC;AAEJ,MAAI;AAEJ,MAAI,qBAAqB,MAAM,KAAK,GAAG;AAErC,UAAM,QAAQ,MAAM;AACpB,UAAM,QAAQ,CAAC;AACf,IAAAA,QAAO,MAAM,MAAM,UAAU;AAC7B,QAAIC,SAAQ,QAAQ,KAAK,GAAG;AAC5B,IAAAA,UAAS,QAAQ;AAAA,MACf,MAAM,kBAAkB,MAAM;AAAA,QAC5B,QAAQA;AAAA,QACR,OAAO;AAAA,QACP,GAAG,QAAQ,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH;AACA,IAAAA,UAAS,QAAQ,KAAK,GAAG;AACzB,IAAAD,MAAK;AACL,UAAM,QAAQ;AACd,WAAOC;AAAA,EACT;AAEA,EAAAD,QAAO,MAAM,MAAM,MAAM;AACzB,YAAU,MAAM,MAAM,OAAO;AAC7B,MAAI,QAAQ,QAAQ,KAAK,GAAG;AAC5B,WAAS,QAAQ;AAAA,IACf,MAAM,kBAAkB,MAAM;AAAA,MAC5B,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,GAAG,QAAQ,QAAQ;AAAA,IACrB,CAAC;AAAA,EACH;AACA,WAAS,QAAQ,KAAK,IAAI;AAC1B,UAAQ;AAER;AAAA;AAAA,IAEG,CAAC,KAAK,OAAO,KAAK;AAAA,IAEnB,eAAe,KAAK,KAAK,GAAG;AAAA,IAC5B;AACA,cAAU,MAAM,MAAM,oBAAoB;AAC1C,aAAS,QAAQ,KAAK,GAAG;AACzB,aAAS,QAAQ;AAAA,MACf,MAAM,KAAK,KAAK,KAAK,EAAC,QAAQ,OAAO,OAAO,KAAK,GAAG,QAAQ,QAAQ,EAAC,CAAC;AAAA,IACxE;AACA,aAAS,QAAQ,KAAK,GAAG;AAAA,EAC3B,OAAO;AAEL,cAAU,MAAM,MAAM,gBAAgB;AACtC,aAAS,QAAQ;AAAA,MACf,MAAM,KAAK,KAAK,KAAK;AAAA,QACnB,QAAQ;AAAA,QACR,OAAO,KAAK,QAAQ,MAAM;AAAA,QAC1B,GAAG,QAAQ,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH;AAAA,EACF;AAEA,UAAQ;AAER,MAAI,KAAK,OAAO;AACd,cAAU,MAAM,MAAM,QAAQ,MAAM,EAAE;AACtC,aAAS,QAAQ,KAAK,MAAM,KAAK;AACjC,aAAS,QAAQ;AAAA,MACf,MAAM,KAAK,KAAK,OAAO;AAAA,QACrB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,GAAG,QAAQ,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH;AACA,aAAS,QAAQ,KAAK,KAAK;AAC3B,YAAQ;AAAA,EACV;AAEA,WAAS,QAAQ,KAAK,GAAG;AAEzB,EAAAA,MAAK;AACL,SAAO;AACT;AAQA,SAAS,SAAS,MAAM,GAAG,OAAO;AAChC,SAAO,qBAAqB,MAAM,KAAK,IAAI,MAAM;AACnD;;;AC5GA,cAAc,OAAO;AASd,SAAS,cAAc,MAAM,GAAG,OAAO,MAAM;AAClD,QAAM,OAAO,KAAK;AAClB,QAAME,QAAO,MAAM,MAAM,eAAe;AACxC,MAAI,UAAU,MAAM,MAAM,OAAO;AACjC,QAAM,UAAU,MAAM,cAAc,IAAI;AACxC,MAAI,QAAQ,QAAQ,KAAK,GAAG;AAC5B,QAAMC,QAAO,MAAM,kBAAkB,MAAM;AAAA,IACzC,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,GAAG,QAAQ,QAAQ;AAAA,EACrB,CAAC;AACD,WAAS,QAAQ,KAAKA,QAAO,IAAI;AAEjC,UAAQ;AAER,QAAM,QAAQ,MAAM;AACpB,QAAM,QAAQ,CAAC;AACf,YAAU,MAAM,MAAM,WAAW;AAKjC,QAAM,YAAY,MAAM,KAAK,MAAM,cAAc,IAAI,GAAG;AAAA,IACtD,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,GAAG,QAAQ,QAAQ;AAAA,EACrB,CAAC;AACD,UAAQ;AACR,QAAM,QAAQ;AACd,EAAAD,MAAK;AAEL,MAAI,SAAS,UAAU,CAACC,SAAQA,UAAS,WAAW;AAClD,aAAS,QAAQ,KAAK,YAAY,GAAG;AAAA,EACvC,WAAW,SAAS,YAAY;AAE9B,YAAQ,MAAM,MAAM,GAAG,EAAE;AAAA,EAC3B,OAAO;AACL,aAAS,QAAQ,KAAK,GAAG;AAAA,EAC3B;AAEA,SAAO;AACT;AAKA,SAAS,oBAAoB;AAC3B,SAAO;AACT;;;ACtDO,SAAS,YAAY,OAAO;AACjC,QAAM,SAAS,MAAM,QAAQ,UAAU;AAEvC,MAAI,WAAW,OAAO,WAAW,OAAO,WAAW,KAAK;AACtD,UAAM,IAAI;AAAA,MACR,kCACE,SACA;AAAA,IACJ;AAAA,EACF;AAEA,SAAO;AACT;;;ACVO,SAAS,iBAAiB,OAAO;AACtC,QAAM,SAAS,YAAY,KAAK;AAChC,QAAM,cAAc,MAAM,QAAQ;AAElC,MAAI,CAAC,aAAa;AAChB,WAAO,WAAW,MAAM,MAAM;AAAA,EAChC;AAEA,MAAI,gBAAgB,OAAO,gBAAgB,OAAO,gBAAgB,KAAK;AACrE,UAAM,IAAI;AAAA,MACR,kCACE,cACA;AAAA,IACJ;AAAA,EACF;AAEA,MAAI,gBAAgB,QAAQ;AAC1B,UAAM,IAAI;AAAA,MACR,yBACE,SACA,4BACA,cACA;AAAA,IACJ;AAAA,EACF;AAEA,SAAO;AACT;;;AC7BO,SAAS,mBAAmB,OAAO;AACxC,QAAM,SAAS,MAAM,QAAQ,iBAAiB;AAE9C,MAAI,WAAW,OAAO,WAAW,KAAK;AACpC,UAAM,IAAI;AAAA,MACR,kCACE,SACA;AAAA,IACJ;AAAA,EACF;AAEA,SAAO;AACT;;;ACZO,SAAS,UAAU,OAAO;AAC/B,QAAM,SAAS,MAAM,QAAQ,QAAQ;AAErC,MAAI,WAAW,OAAO,WAAW,OAAO,WAAW,KAAK;AACtD,UAAM,IAAI;AAAA,MACR,kCACE,SACA;AAAA,IACJ;AAAA,EACF;AAEA,SAAO;AACT;;;ACHO,SAAS,KAAK,MAAM,QAAQ,OAAO,MAAM;AAC9C,QAAMC,QAAO,MAAM,MAAM,MAAM;AAC/B,QAAM,gBAAgB,MAAM;AAE5B,MAAI,SAAS,KAAK,UAAU,mBAAmB,KAAK,IAAI,YAAY,KAAK;AAEzE,QAAM,cAAc,KAAK,UACrB,WAAW,MACT,MACA,MACF,iBAAiB,KAAK;AAC1B,MAAI,qBACF,UAAU,MAAM,iBAAiB,WAAW,MAAM,iBAAiB;AAErE,MAAI,CAAC,KAAK,SAAS;AACjB,UAAM,gBAAgB,KAAK,WAAW,KAAK,SAAS,CAAC,IAAI;AAUzD;AAAA;AAAA,OAEG,WAAW,OAAO,WAAW;AAAA,MAE9B,kBACC,CAAC,cAAc,YAAY,CAAC,cAAc,SAAS,CAAC;AAAA,MAErD,MAAM,MAAM,MAAM,MAAM,SAAS,CAAC,MAAM,UACxC,MAAM,MAAM,MAAM,MAAM,SAAS,CAAC,MAAM,cACxC,MAAM,MAAM,MAAM,MAAM,SAAS,CAAC,MAAM,UACxC,MAAM,MAAM,MAAM,MAAM,SAAS,CAAC,MAAM;AAAA,MAExC,MAAM,WAAW,MAAM,WAAW,SAAS,CAAC,MAAM,KAClD,MAAM,WAAW,MAAM,WAAW,SAAS,CAAC,MAAM,KAClD,MAAM,WAAW,MAAM,WAAW,SAAS,CAAC,MAAM;AAAA,MAClD;AACA,2BAAqB;AAAA,IACvB;AAUA,QAAI,UAAU,KAAK,MAAM,UAAU,eAAe;AAChD,UAAI,QAAQ;AAEZ,aAAO,EAAE,QAAQ,KAAK,SAAS,QAAQ;AACrC,cAAM,OAAO,KAAK,SAAS,KAAK;AAEhC,YACE,QACA,KAAK,SAAS,cACd,KAAK,YACL,KAAK,SAAS,CAAC,KACf,KAAK,SAAS,CAAC,EAAE,SAAS,iBAC1B;AACA,+BAAqB;AACrB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,MAAI,oBAAoB;AACtB,aAAS;AAAA,EACX;AAEA,QAAM,gBAAgB;AACtB,QAAM,QAAQ,MAAM,cAAc,MAAM,IAAI;AAC5C,QAAM,iBAAiB;AACvB,QAAM,gBAAgB;AACtB,EAAAA,MAAK;AACL,SAAO;AACT;;;AC3FO,SAAS,oBAAoB,OAAO;AACzC,QAAM,QAAQ,MAAM,QAAQ,kBAAkB;AAE9C,MAAI,UAAU,SAAS,UAAU,SAAS,UAAU,SAAS;AAC3D,UAAM,IAAI;AAAA,MACR,kCACE,QACA;AAAA,IACJ;AAAA,EACF;AAEA,SAAO;AACT;;;ACLO,SAAS,SAAS,MAAM,QAAQ,OAAO,MAAM;AAClD,QAAM,iBAAiB,oBAAoB,KAAK;AAChD,MAAI,SAAS,MAAM,iBAAiB,YAAY,KAAK;AAGrD,MAAI,UAAU,OAAO,SAAS,UAAU,OAAO,SAAS;AACtD,cACG,OAAO,OAAO,UAAU,YAAY,OAAO,QAAQ,KAChD,OAAO,QACP,MACH,MAAM,QAAQ,wBAAwB,QACnC,IACA,OAAO,SAAS,QAAQ,IAAI,KAChC;AAAA,EACJ;AAEA,MAAI,OAAO,OAAO,SAAS;AAE3B,MACE,mBAAmB,SAClB,mBAAmB,YAChB,UAAU,OAAO,SAAS,UAAU,OAAO,UAAW,KAAK,SAC/D;AACA,WAAO,KAAK,KAAK,OAAO,CAAC,IAAI;AAAA,EAC/B;AAEA,QAAM,UAAU,MAAM,cAAc,IAAI;AACxC,UAAQ,KAAK,SAAS,IAAI,OAAO,OAAO,OAAO,MAAM,CAAC;AACtD,UAAQ,MAAM,IAAI;AAClB,QAAMC,QAAO,MAAM,MAAM,UAAU;AACnC,QAAM,QAAQ,MAAM;AAAA,IAClB,MAAM,cAAc,MAAM,QAAQ,QAAQ,CAAC;AAAA,IAC3CC;AAAA,EACF;AACA,EAAAD,MAAK;AAEL,SAAO;AAGP,WAASC,KAAI,MAAM,OAAO,OAAO;AAC/B,QAAI,OAAO;AACT,cAAQ,QAAQ,KAAK,IAAI,OAAO,IAAI,KAAK;AAAA,IAC3C;AAEA,YAAQ,QAAQ,SAAS,SAAS,IAAI,OAAO,OAAO,OAAO,MAAM,KAAK;AAAA,EACxE;AACF;;;ACjDO,SAAS,UAAU,MAAM,GAAG,OAAO,MAAM;AAC9C,QAAMC,QAAO,MAAM,MAAM,WAAW;AACpC,QAAM,UAAU,MAAM,MAAM,UAAU;AACtC,QAAM,QAAQ,MAAM,kBAAkB,MAAM,IAAI;AAChD,UAAQ;AACR,EAAAA,MAAK;AACL,SAAO;AACT;;;ACDO,IAAM;AAAA;AAAA,EAGT,QAAQ;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,EACF,CAAC;AAAA;;;AC7BE,SAAS,KAAK,MAAM,GAAG,OAAO,MAAM;AAEzC,QAAM,cAAc,KAAK,SAAS,KAAK,SAAU,GAAG;AAClD,WAAO,SAAS,CAAC;AAAA,EACnB,CAAC;AAED,QAAM,YAAY,cAAc,MAAM,oBAAoB,MAAM;AAChE,SAAO,UAAU,KAAK,OAAO,MAAM,IAAI;AACzC;;;ACdO,SAAS,YAAY,OAAO;AACjC,QAAM,SAAS,MAAM,QAAQ,UAAU;AAEvC,MAAI,WAAW,OAAO,WAAW,KAAK;AACpC,UAAM,IAAI;AAAA,MACR,mCACE,SACA;AAAA,IACJ;AAAA,EACF;AAEA,SAAO;AACT;;;ACXA,OAAO,OAAO;AASP,SAAS,OAAO,MAAM,GAAG,OAAO,MAAM;AAC3C,QAAM,SAAS,YAAY,KAAK;AAChC,QAAMC,QAAO,MAAM,MAAM,QAAQ;AACjC,QAAM,UAAU,MAAM,cAAc,IAAI;AACxC,QAAM,SAAS,QAAQ,KAAK,SAAS,MAAM;AAE3C,MAAI,UAAU,QAAQ;AAAA,IACpB,MAAM,kBAAkB,MAAM;AAAA,MAC5B,OAAO;AAAA,MACP;AAAA,MACA,GAAG,QAAQ,QAAQ;AAAA,IACrB,CAAC;AAAA,EACH;AACA,QAAM,cAAc,QAAQ,WAAW,CAAC;AACxC,QAAM,OAAO;AAAA,IACX,KAAK,OAAO,WAAW,KAAK,OAAO,SAAS,CAAC;AAAA,IAC7C;AAAA,IACA;AAAA,EACF;AAEA,MAAI,KAAK,QAAQ;AACf,cAAU,yBAAyB,WAAW,IAAI,QAAQ,MAAM,CAAC;AAAA,EACnE;AAEA,QAAM,cAAc,QAAQ,WAAW,QAAQ,SAAS,CAAC;AACzD,QAAM,QAAQ,WAAW,KAAK,MAAM,WAAW,CAAC,GAAG,aAAa,MAAM;AAEtE,MAAI,MAAM,QAAQ;AAChB,cAAU,QAAQ,MAAM,GAAG,EAAE,IAAI,yBAAyB,WAAW;AAAA,EACvE;AAEA,QAAM,QAAQ,QAAQ,KAAK,SAAS,MAAM;AAE1C,EAAAA,MAAK;AAEL,QAAM,iCAAiC;AAAA,IACrC,OAAO,MAAM;AAAA,IACb,QAAQ,KAAK;AAAA,EACf;AACA,SAAO,SAAS,UAAU;AAC5B;AAQA,SAAS,WAAW,GAAG,IAAI,OAAO;AAChC,SAAO,MAAM,QAAQ,UAAU;AACjC;;;ACxDO,SAAS,KAAK,MAAM,GAAG,OAAO,MAAM;AACzC,SAAO,MAAM,KAAK,KAAK,OAAO,IAAI;AACpC;;;ACNO,SAAS,oBAAoB,OAAO;AACzC,QAAM,aAAa,MAAM,QAAQ,kBAAkB;AAEnD,MAAI,aAAa,GAAG;AAClB,UAAM,IAAI;AAAA,MACR,6CACE,aACA;AAAA,IACJ;AAAA,EACF;AAEA,SAAO;AACT;;;ACNO,SAAS,cAAc,GAAG,IAAI,OAAO;AAC1C,QAAM,SACJ,UAAU,KAAK,KAAK,MAAM,QAAQ,aAAa,MAAM,KACrD,OAAO,oBAAoB,KAAK,CAAC;AAEnC,SAAO,MAAM,QAAQ,aAAa,MAAM,MAAM,GAAG,EAAE,IAAI;AACzD;;;ACGO,IAAM,SAAS;AAAA,EACpB;AAAA,EACA,OAAO;AAAA,EACP;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;ACFO,SAAS,uBAAuB;AACrC,SAAO;AAAA,IACL,OAAO;AAAA,MACL,OAAO;AAAA,MACP,WAAW;AAAA,MACX,aAAa;AAAA,MACb,UAAU;AAAA,IACZ;AAAA,IACA,MAAM;AAAA,MACJ,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,MACX,aAAa;AAAA,MACb,UAAU;AAAA,IACZ;AAAA,EACF;AACF;AAMA,SAAS,WAAW,OAAO;AACzB,QAAM,QAAQ,MAAM;AACpB,KAAO,OAAO,4BAA4B;AAC1C,OAAK;AAAA,IACH;AAAA,MACE,MAAM;AAAA,MACN,OAAO,MAAM,IAAI,SAAU,GAAG;AAC5B,eAAO,MAAM,SAAS,OAAO;AAAA,MAC/B,CAAC;AAAA,MACD,UAAU,CAAC;AAAA,IACb;AAAA,IACA;AAAA,EACF;AACA,OAAK,KAAK,UAAU;AACtB;AAMA,SAAS,UAAU,OAAO;AACxB,OAAK,KAAK,KAAK;AACf,OAAK,KAAK,UAAU;AACtB;AAMA,SAAS,SAAS,OAAO;AACvB,OAAK,MAAM,EAAC,MAAM,YAAY,UAAU,CAAC,EAAC,GAAG,KAAK;AACpD;AAMA,SAAS,KAAK,OAAO;AACnB,OAAK,KAAK,KAAK;AACjB;AAMA,SAAS,UAAU,OAAO;AACxB,OAAK,MAAM,EAAC,MAAM,aAAa,UAAU,CAAC,EAAC,GAAG,KAAK;AACrD;AAQA,SAAS,aAAa,OAAO;AAC3B,MAAI,QAAQ,KAAK,OAAO;AAExB,MAAI,KAAK,KAAK,SAAS;AACrB,YAAQ,MAAM,QAAQ,cAAc,OAAO;AAAA,EAC7C;AAEA,QAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,KAAO,KAAK,SAAS,YAAY;AACjC,OAAK,QAAQ;AACb,OAAK,KAAK,KAAK;AACjB;AAOA,SAAS,QAAQ,IAAI,IAAI;AAEvB,SAAO,OAAO,MAAM,KAAK;AAC3B;AAWO,SAAS,mBAAmB,SAAS;AAC1C,QAAM,WAAW,WAAW,CAAC;AAC7B,QAAM,UAAU,SAAS;AACzB,QAAM,kBAAkB,SAAS;AACjC,QAAM,eAAe,SAAS;AAC9B,QAAM,SAAS,UAAU,MAAM;AAE/B,SAAO;AAAA,IACL,QAAQ;AAAA,MACN,EAAC,WAAW,MAAM,aAAa,YAAW;AAAA,MAC1C,EAAC,WAAW,MAAM,aAAa,YAAW;AAAA;AAAA;AAAA,MAG1C,EAAC,SAAS,MAAM,WAAW,KAAK,OAAO,SAAS;AAAA;AAAA,MAEhD,EAAC,WAAW,KAAK,aAAa,YAAW;AAAA;AAAA;AAAA,MAGzC,EAAC,SAAS,MAAM,WAAW,KAAK,OAAO,IAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAM1C,EAAC,SAAS,MAAM,WAAW,KAAK,OAAO,QAAO;AAAA,IAChD;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,IACZ;AAAA,EACF;AAMA,WAAS,YAAY,MAAM,GAAG,OAAO,MAAM;AACzC,WAAO,cAAc,kBAAkB,MAAM,OAAO,IAAI,GAAG,KAAK,KAAK;AAAA,EACvE;AAUA,WAAS,eAAe,MAAM,GAAG,OAAO,MAAM;AAC5C,UAAM,MAAM,qBAAqB,MAAM,OAAO,IAAI;AAClD,UAAM,QAAQ,cAAc,CAAC,GAAG,CAAC;AAEjC,WAAO,MAAM,MAAM,GAAG,MAAM,QAAQ,IAAI,CAAC;AAAA,EAC3C;AAMA,WAAS,gBAAgB,MAAM,GAAG,OAAO,MAAM;AAC7C,UAAMC,QAAO,MAAM,MAAM,WAAW;AACpC,UAAM,UAAU,MAAM,MAAM,UAAU;AACtC,UAAM,QAAQ,MAAM,kBAAkB,MAAM;AAAA,MAC1C,GAAG;AAAA,MACH,QAAQ;AAAA,MACR,OAAO;AAAA,IACT,CAAC;AACD,YAAQ;AACR,IAAAA,MAAK;AACL,WAAO;AAAA,EACT;AAMA,WAAS,cAAc,QAAQ,OAAO;AACpC,WAAO,cAAc,QAAQ;AAAA,MAC3B;AAAA;AAAA,MAEA;AAAA;AAAA,MAEA;AAAA;AAAA,MAEA;AAAA,IACF,CAAC;AAAA,EACH;AAOA,WAAS,kBAAkB,MAAM,OAAO,MAAM;AAC5C,UAAM,WAAW,KAAK;AACtB,QAAI,QAAQ;AAEZ,UAAM,SAAS,CAAC;AAChB,UAAM,UAAU,MAAM,MAAM,OAAO;AAEnC,WAAO,EAAE,QAAQ,SAAS,QAAQ;AAChC,aAAO,KAAK,IAAI,qBAAqB,SAAS,KAAK,GAAG,OAAO,IAAI;AAAA,IACnE;AAEA,YAAQ;AAER,WAAO;AAAA,EACT;AAOA,WAAS,qBAAqB,MAAM,OAAO,MAAM;AAC/C,UAAM,WAAW,KAAK;AACtB,QAAI,QAAQ;AAEZ,UAAM,SAAS,CAAC;AAChB,UAAM,UAAU,MAAM,MAAM,UAAU;AAEtC,WAAO,EAAE,QAAQ,SAAS,QAAQ;AAIhC,aAAO,KAAK,IAAI,gBAAgB,SAAS,KAAK,GAAG,MAAM,OAAO,IAAI;AAAA,IACpE;AAEA,YAAQ;AAER,WAAO;AAAA,EACT;AAMA,WAAS,oBAAoB,MAAM,QAAQ,OAAO;AAChD,QAAI,QAAQ,OAAgB,WAAW,MAAM,QAAQ,KAAK;AAE1D,QAAI,MAAM,MAAM,SAAS,WAAW,GAAG;AACrC,cAAQ,MAAM,QAAQ,OAAO,MAAM;AAAA,IACrC;AAEA,WAAO;AAAA,EACT;AACF;;;ACvRO,SAAS,8BAA8B;AAC5C,SAAO;AAAA,IACL,MAAM;AAAA,MACJ,2BAA2B;AAAA,MAC3B,6BAA6B;AAAA,MAC7B,WAAW;AAAA,IACb;AAAA,EACF;AACF;AASO,SAAS,4BAA4B;AAC1C,SAAO;AAAA,IACL,QAAQ,CAAC,EAAC,SAAS,MAAM,WAAW,KAAK,OAAO,QAAO,CAAC;AAAA,IACxD,UAAU,EAAC,UAAU,yBAAwB;AAAA,EAC/C;AACF;AAMA,SAAS,UAAU,OAAO;AAExB,QAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,KAAO,KAAK,SAAS,UAAU;AAC/B,OAAK,UAAU,MAAM,SAAS;AAChC;AAMA,SAAS,8BAA8B,OAAO;AAC5C,QAAM,SAAS,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAE/C,MACE,UACA,OAAO,SAAS,cAChB,OAAO,OAAO,YAAY,WAC1B;AACA,UAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAO,KAAK,SAAS,WAAW;AAChC,UAAM,OAAO,KAAK,SAAS,CAAC;AAE5B,QAAI,QAAQ,KAAK,SAAS,QAAQ;AAChC,YAAM,WAAW,OAAO;AACxB,UAAI,QAAQ;AAEZ,UAAI;AAEJ,aAAO,EAAE,QAAQ,SAAS,QAAQ;AAChC,cAAM,UAAU,SAAS,KAAK;AAC9B,YAAI,QAAQ,SAAS,aAAa;AAChC,4BAAkB;AAClB;AAAA,QACF;AAAA,MACF;AAEA,UAAI,oBAAoB,MAAM;AAE5B,aAAK,QAAQ,KAAK,MAAM,MAAM,CAAC;AAE/B,YAAI,KAAK,MAAM,WAAW,GAAG;AAC3B,eAAK,SAAS,MAAM;AAAA,QACtB,WACE,KAAK,YACL,KAAK,YACL,OAAO,KAAK,SAAS,MAAM,WAAW,UACtC;AACA,eAAK,SAAS,MAAM;AACpB,eAAK,SAAS,MAAM;AACpB,eAAK,SAAS,QAAQ,OAAO,OAAO,CAAC,GAAG,KAAK,SAAS,KAAK;AAAA,QAC7D;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,OAAK,KAAK,KAAK;AACjB;AAMA,SAAS,yBAAyB,MAAM,QAAQ,OAAO,MAAM;AAC3D,QAAM,OAAO,KAAK,SAAS,CAAC;AAC5B,QAAM,YACJ,OAAO,KAAK,YAAY,aAAa,QAAQ,KAAK,SAAS;AAC7D,QAAM,WAAW,OAAO,KAAK,UAAU,MAAM,OAAO;AACpD,QAAM,UAAU,MAAM,cAAc,IAAI;AAExC,MAAI,WAAW;AACb,YAAQ,KAAK,QAAQ;AAAA,EACvB;AAEA,MAAI,QAAQ,OAAgB,SAAS,MAAM,QAAQ,OAAO;AAAA,IACxD,GAAG;AAAA,IACH,GAAG,QAAQ,QAAQ;AAAA,EACrB,CAAC;AAED,MAAI,WAAW;AACb,YAAQ,MAAM,QAAQ,mCAAmC,KAAK;AAAA,EAChE;AAEA,SAAO;AAMP,WAAS,MAAM,IAAI;AACjB,WAAO,KAAK;AAAA,EACd;AACF;;;AC5GO,SAAS,kBAAkB;AAChC,SAAO;AAAA,IACL,+BAA+B;AAAA,IAC/B,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,qBAAqB;AAAA,IACrB,4BAA4B;AAAA,EAC9B;AACF;AAYO,SAAS,cAAc,SAAS;AACrC,SAAO;AAAA,IACL,YAAY;AAAA,MACV,6BAA6B;AAAA,MAC7B,sBAAsB,OAAO;AAAA,MAC7B,2BAA2B;AAAA,MAC3B,mBAAmB,OAAO;AAAA,MAC1B,0BAA0B;AAAA,IAC5B;AAAA,EACF;AACF;;;AChDA,IAAM,YAAY,EAAC,UAAU,mBAAmB,SAAS,KAAI;AAC7D,IAAM,SAAS,EAAC,UAAU,gBAAgB,SAAS,KAAI;AACvD,IAAM,OAAO,EAAC,UAAU,cAAc,SAAS,KAAI;AACnD,IAAM,QAAQ,EAAC,UAAU,eAAe,SAAS,KAAI;AACrD,IAAM,sBAAsB;AAAA,EAC1B,UAAU;AAAA,EACV,SAAS;AACX;AAEA,IAAM,cAAc;AAAA,EAClB,MAAM;AAAA,EACN,UAAU;AAAA,EACV,UAAU;AACZ;AAEA,IAAM,mBAAmB;AAAA,EACvB,MAAM;AAAA,EACN,UAAU;AAAA,EACV,UAAU;AACZ;AAEA,IAAM,gBAAgB;AAAA,EACpB,MAAM;AAAA,EACN,UAAU;AAAA,EACV,UAAU;AACZ;AAGA,IAAMC,QAAO,CAAC;AAUP,SAAS,qBAAqB;AACnC,SAAO,EAAC,MAAAA,MAAI;AACd;AAGA,IAAIC,QAAO,MAAM;AAGjB,OAAOA,QAAO,MAAM,gBAAgB;AAClC,EAAAD,MAAKC,KAAI,IAAI;AACb,EAAAA;AACA,MAAIA,UAAS,MAAM,MAAO,CAAAA,QAAO,MAAM;AAAA,WAC9BA,UAAS,MAAM,kBAAmB,CAAAA,QAAO,MAAM;AAC1D;AAEAD,MAAK,MAAM,QAAQ,IAAI;AACvBA,MAAK,MAAM,IAAI,IAAI;AACnBA,MAAK,MAAM,GAAG,IAAI;AAClBA,MAAK,MAAM,UAAU,IAAI;AACzBA,MAAK,MAAM,UAAU,IAAI,CAAC,eAAe,gBAAgB;AACzDA,MAAK,MAAM,UAAU,IAAI,CAAC,eAAe,gBAAgB;AACzDA,MAAK,MAAM,UAAU,IAAI,CAAC,eAAe,WAAW;AACpDA,MAAK,MAAM,UAAU,IAAI,CAAC,eAAe,WAAW;AAmBpD,SAAS,sBAAsB,SAASE,KAAI,KAAK;AAC/C,QAAM,OAAO;AAEb,MAAI;AAEJ,MAAI;AAEJ,SAAO;AAYP,WAAS,MAAMD,OAAM;AACnB,QACE,CAAC,SAASA,KAAI,KACd,CAAC,cAAc,KAAK,MAAM,KAAK,QAAQ,KACvC,mBAAmB,KAAK,MAAM,GAC9B;AACA,aAAO,IAAIA,KAAI;AAAA,IACjB;AAEA,YAAQ,MAAM,iBAAiB;AAC/B,YAAQ,MAAM,sBAAsB;AACpC,WAAO,MAAMA,KAAI;AAAA,EACnB;AAYA,WAAS,MAAMA,OAAM;AACnB,QAAI,SAASA,KAAI,GAAG;AAClB,cAAQ,QAAQA,KAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAIA,UAAS,MAAM,QAAQ;AACzB,cAAQ,QAAQA,KAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,IAAIA,KAAI;AAAA,EACjB;AAgBA,WAAS,YAAYA,OAAM;AAEzB,QAAIA,UAAS,MAAM,KAAK;AACtB,aAAO,QAAQ;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,MACF,EAAEA,KAAI;AAAA,IACR;AAGA,QACEA,UAAS,MAAM,QACfA,UAAS,MAAM,cACf,kBAAkBA,KAAI,GACtB;AACA,aAAO;AACP,cAAQ,QAAQA,KAAI;AACpB,aAAO;AAAA,IACT;AAQA,WAAO,iBAAiBA,KAAI;AAAA,EAC9B;AAYA,WAAS,eAAeA,OAAM;AAC5B,YAAQ,QAAQA,KAAI;AACpB,UAAM;AACN,WAAO;AAAA,EACT;AAYA,WAAS,iBAAiBA,OAAM;AAG9B,QAAI,QAAQ,OAAO,WAAW,KAAK,QAAQ,GAAG;AAC5C,cAAQ,KAAK,sBAAsB;AACnC,cAAQ,KAAK,iBAAiB;AAC9B,aAAOC,IAAGD,KAAI;AAAA,IAChB;AAEA,WAAO,IAAIA,KAAI;AAAA,EACjB;AACF;AAaA,SAAS,oBAAoB,SAASC,KAAI,KAAK;AAC7C,QAAM,OAAO;AAEb,SAAO;AAYP,WAAS,SAASD,OAAM;AACtB,QACGA,UAAS,MAAM,cAAcA,UAAS,MAAM,cAC7C,CAAC,YAAY,KAAK,MAAM,KAAK,QAAQ,KACrC,mBAAmB,KAAK,MAAM,GAC9B;AACA,aAAO,IAAIA,KAAI;AAAA,IACjB;AAEA,YAAQ,MAAM,iBAAiB;AAC/B,YAAQ,MAAM,oBAAoB;AAGlC,WAAO,QAAQ;AAAA,MACb;AAAA,MACA,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,QAAQ,GAAG,GAAG;AAAA,MAC5D;AAAA,IACF,EAAEA,KAAI;AAAA,EACR;AAYA,WAAS,SAASA,OAAM;AACtB,YAAQ,KAAK,oBAAoB;AACjC,YAAQ,KAAK,iBAAiB;AAC9B,WAAOC,IAAGD,KAAI;AAAA,EAChB;AACF;AAaA,SAAS,yBAAyB,SAASC,KAAI,KAAK;AAClD,QAAM,OAAO;AACb,MAAI,SAAS;AACb,MAAI,OAAO;AAEX,SAAO;AAYP,WAAS,cAAcD,OAAM;AAC3B,SACGA,UAAS,MAAM,cAAcA,UAAS,MAAM,eAC7C,iBAAiB,KAAK,MAAM,KAAK,QAAQ,KACzC,CAAC,mBAAmB,KAAK,MAAM,GAC/B;AACA,cAAQ,MAAM,iBAAiB;AAC/B,cAAQ,MAAM,qBAAqB;AACnC,gBAAU,OAAO,cAAcA,KAAI;AACnC,cAAQ,QAAQA,KAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,IAAIA,KAAI;AAAA,EACjB;AAYA,WAAS,qBAAqBA,OAAM;AAElC,QAAI,WAAWA,KAAI,KAAK,OAAO,SAAS,GAAG;AAEzC,gBAAU,OAAO,cAAcA,KAAI;AACnC,cAAQ,QAAQA,KAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAIA,UAAS,MAAM,OAAO;AACxB,YAAM,WAAW,OAAO,YAAY;AAEpC,UAAI,aAAa,UAAU,aAAa,SAAS;AAC/C,gBAAQ,QAAQA,KAAI;AACpB,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO,IAAIA,KAAI;AAAA,EACjB;AAYA,WAAS,sBAAsBA,OAAM;AACnC,QAAIA,UAAS,MAAM,OAAO;AACxB,cAAQ,QAAQA,KAAI;AAEpB,UAAI,MAAM;AACR,eAAO;AAAA,MACT;AAEA,aAAO;AACP,aAAO;AAAA,IACT;AAEA,WAAO,IAAIA,KAAI;AAAA,EACjB;AAYA,WAAS,cAAcA,OAAM;AAG3B,WAAOA,UAAS,MAAM,OACpB,aAAaA,KAAI,KACjB,0BAA0BA,KAAI,KAC9B,kBAAkBA,KAAI,KACtB,mBAAmBA,KAAI,IACrB,IAAIA,KAAI,IACR,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,aAAa,GAAG,GAAG,EAAEA,KAAI;AAAA,EAC7E;AAYA,WAAS,cAAcA,OAAM;AAC3B,YAAQ,KAAK,qBAAqB;AAClC,YAAQ,KAAK,iBAAiB;AAC9B,WAAOC,IAAGD,KAAI;AAAA,EAChB;AACF;AAaA,SAAS,kBAAkB,SAASC,KAAI,KAAK;AAC3C,MAAI,OAAO;AAEX,SAAO;AAYP,WAAS,gBAAgBD,OAAM;AAC7B,SAAKA,UAAS,MAAM,cAAcA,UAAS,MAAM,eAAe,OAAO,GAAG;AACxE;AACA,cAAQ,QAAQA,KAAI;AACpB,aAAO;AAAA,IACT;AAEA,QAAIA,UAAS,MAAM,OAAO,SAAS,GAAG;AACpC,cAAQ,QAAQA,KAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,IAAIA,KAAI;AAAA,EACjB;AAYA,WAAS,eAAeA,OAAM;AAE5B,WAAOA,UAAS,MAAM,MAAM,IAAIA,KAAI,IAAIC,IAAGD,KAAI;AAAA,EACjD;AACF;AAaA,SAAS,eAAe,SAASC,KAAI,KAAK;AAExC,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAEJ,SAAO;AAYP,WAAS,aAAaD,OAAM;AAI1B,QAAIA,UAAS,MAAM,OAAOA,UAAS,MAAM,YAAY;AACnD,aAAO,QAAQ,MAAM,OAAO,aAAa,mBAAmB,EAAEA,KAAI;AAAA,IACpE;AAQA,QACEA,UAAS,MAAM,OACf,0BAA0BA,KAAI,KAC9B,kBAAkBA,KAAI,KACrBA,UAAS,MAAM,QAAQ,mBAAmBA,KAAI,GAC/C;AACA,aAAO,YAAYA,KAAI;AAAA,IACzB;AAEA,WAAO;AACP,YAAQ,QAAQA,KAAI;AACpB,WAAO;AAAA,EACT;AAYA,WAAS,oBAAoBA,OAAM;AAEjC,QAAIA,UAAS,MAAM,YAAY;AAC7B,gCAA0B;AAAA,IAC5B,OAGK;AACH,oCAA8B;AAC9B,gCAA0B;AAAA,IAC5B;AAEA,YAAQ,QAAQA,KAAI;AACpB,WAAO;AAAA,EACT;AAWA,WAAS,YAAYA,OAAM;AAGzB,QAAI,+BAA+B,2BAA2B,CAAC,MAAM;AACnE,aAAO,IAAIA,KAAI;AAAA,IACjB;AAEA,WAAOC,IAAGD,KAAI;AAAA,EAChB;AACF;AAaA,SAAS,aAAa,SAASC,KAAI;AACjC,MAAI,WAAW;AACf,MAAI,YAAY;AAEhB,SAAO;AAYP,WAAS,WAAWD,OAAM;AACxB,QAAIA,UAAS,MAAM,iBAAiB;AAClC;AACA,cAAQ,QAAQA,KAAI;AACpB,aAAO;AAAA,IACT;AAKA,QAAIA,UAAS,MAAM,oBAAoB,YAAY,UAAU;AAC3D,aAAO,kBAAkBA,KAAI;AAAA,IAC/B;AAKA,QACEA,UAAS,MAAM,mBACfA,UAAS,MAAM,iBACfA,UAAS,MAAM,aACfA,UAAS,MAAM,cACfA,UAAS,MAAM,oBACfA,UAAS,MAAM,YACfA,UAAS,MAAM,SACfA,UAAS,MAAM,OACfA,UAAS,MAAM,SACfA,UAAS,MAAM,aACfA,UAAS,MAAM,YACfA,UAAS,MAAM,gBACfA,UAAS,MAAM,sBACfA,UAAS,MAAM,cACfA,UAAS,MAAM,OACf;AACA,aAAO,QAAQ,MAAM,OAAOC,KAAI,iBAAiB,EAAED,KAAI;AAAA,IACzD;AAEA,QACEA,UAAS,MAAM,OACf,0BAA0BA,KAAI,KAC9B,kBAAkBA,KAAI,GACtB;AACA,aAAOC,IAAGD,KAAI;AAAA,IAChB;AAEA,YAAQ,QAAQA,KAAI;AACpB,WAAO;AAAA,EACT;AAYA,WAAS,kBAAkBA,OAAM;AAE/B,QAAIA,UAAS,MAAM,kBAAkB;AACnC;AAAA,IACF;AAEA,YAAQ,QAAQA,KAAI;AACpB,WAAO;AAAA,EACT;AACF;AAiBA,SAAS,cAAc,SAASC,KAAI,KAAK;AACvC,SAAOC;AAYP,WAASA,OAAMF,OAAM;AAEnB,QACEA,UAAS,MAAM,mBACfA,UAAS,MAAM,iBACfA,UAAS,MAAM,cACfA,UAAS,MAAM,oBACfA,UAAS,MAAM,YACfA,UAAS,MAAM,SACfA,UAAS,MAAM,OACfA,UAAS,MAAM,SACfA,UAAS,MAAM,aACfA,UAAS,MAAM,gBACfA,UAAS,MAAM,cACfA,UAAS,MAAM,OACf;AACA,cAAQ,QAAQA,KAAI;AACpB,aAAOE;AAAA,IACT;AAKA,QAAIF,UAAS,MAAM,WAAW;AAC5B,cAAQ,QAAQA,KAAI;AACpB,aAAO;AAAA,IACT;AAKA,QAAIA,UAAS,MAAM,oBAAoB;AACrC,cAAQ,QAAQA,KAAI;AACpB,aAAO;AAAA,IACT;AAEA;AAAA;AAAA,MAEEA,UAAS,MAAM;AAAA,MAEfA,UAAS,MAAM,OACf,0BAA0BA,KAAI,KAC9B,kBAAkBA,KAAI;AAAA,MACtB;AACA,aAAOC,IAAGD,KAAI;AAAA,IAChB;AAEA,WAAO,IAAIA,KAAI;AAAA,EACjB;AAeA,WAAS,kBAAkBA,OAAM;AAG/B,QACEA,UAAS,MAAM,OACfA,UAAS,MAAM,mBACfA,UAAS,MAAM,qBACf,0BAA0BA,KAAI,KAC9B,kBAAkBA,KAAI,GACtB;AACA,aAAOC,IAAGD,KAAI;AAAA,IAChB;AAEA,WAAOE,OAAMF,KAAI;AAAA,EACnB;AAYA,WAAS,6BAA6BA,OAAM;AAE1C,WAAO,WAAWA,KAAI,IAAI,8BAA8BA,KAAI,IAAI,IAAIA,KAAI;AAAA,EAC1E;AAYA,WAAS,8BAA8BA,OAAM;AAE3C,QAAIA,UAAS,MAAM,WAAW;AAC5B,cAAQ,QAAQA,KAAI;AACpB,aAAOE;AAAA,IACT;AAEA,QAAI,WAAWF,KAAI,GAAG;AACpB,cAAQ,QAAQA,KAAI;AACpB,aAAO;AAAA,IACT;AAGA,WAAO,IAAIA,KAAI;AAAA,EACjB;AACF;AAiBA,SAAS,4BAA4B,SAASC,KAAI,KAAK;AACrD,SAAO;AAYP,WAAS,MAAMD,OAAM;AAEnB,YAAQ,QAAQA,KAAI;AACpB,WAAO;AAAA,EACT;AAYA,WAAS,MAAMA,OAAM;AAEnB,WAAO,kBAAkBA,KAAI,IAAI,IAAIA,KAAI,IAAIC,IAAGD,KAAI;AAAA,EACtD;AACF;AAQA,SAAS,YAAYA,OAAM;AACzB,SACEA,UAAS,MAAM,OACfA,UAAS,MAAM,mBACfA,UAAS,MAAM,YACfA,UAAS,MAAM,cACfA,UAAS,MAAM,qBACfA,UAAS,MAAM,sBACfA,UAAS,MAAM,SACf,0BAA0BA,KAAI;AAElC;AAQA,SAAS,iBAAiBA,OAAM;AAC9B,SAAO,CAAC,WAAWA,KAAI;AACzB;AAMA,SAAS,cAAcA,OAAM;AAK3B,SAAO,EAAEA,UAAS,MAAM,SAAS,SAASA,KAAI;AAChD;AAMA,SAAS,SAASA,OAAM;AACtB,SACEA,UAAS,MAAM,YACfA,UAAS,MAAM,QACfA,UAAS,MAAM,OACfA,UAAS,MAAM,cACf,kBAAkBA,KAAI;AAE1B;AAMA,SAAS,mBAAmB,QAAQ;AAClC,MAAI,QAAQ,OAAO;AACnB,MAAI,SAAS;AAEb,SAAO,SAAS;AACd,UAAM,QAAQ,OAAO,KAAK,EAAE,CAAC;AAE7B,SACG,MAAM,SAAS,eAAe,MAAM,SAAS,iBAC9C,CAAC,MAAM,WACP;AACA,eAAS;AACT;AAAA,IACF;AAIA,QAAI,MAAM,+BAA+B;AACvC,eAAS;AACT;AAAA,IACF;AAAA,EACF;AAEA,MAAI,OAAO,SAAS,KAAK,CAAC,QAAQ;AAGhC,WAAO,OAAO,SAAS,CAAC,EAAE,CAAC,EAAE,gCAAgC;AAAA,EAC/D;AAEA,SAAO;AACT;;;AC38BA,IAAM,SAAS,EAAC,UAAU,gBAAgB,SAAS,KAAI;AAehD,SAAS,cAAc;AAE5B,SAAO;AAAA,IACL,UAAU;AAAA,MACR,CAAC,MAAM,iBAAiB,GAAG;AAAA,QACzB,MAAM;AAAA,QACN,UAAU;AAAA,QACV,cAAc,EAAC,UAAU,+BAA8B;AAAA,QACvD,MAAM;AAAA,MACR;AAAA,IACF;AAAA,IACA,MAAM;AAAA,MACJ,CAAC,MAAM,iBAAiB,GAAG;AAAA,QACzB,MAAM;AAAA,QACN,UAAU;AAAA,MACZ;AAAA,MACA,CAAC,MAAM,kBAAkB,GAAG;AAAA,QAC1B,MAAM;AAAA,QACN,KAAK;AAAA,QACL,UAAU;AAAA,QACV,WAAW;AAAA,MACb;AAAA,IACF;AAAA,EACF;AACF;AAOA,SAAS,iCAAiC,SAASG,KAAI,KAAK;AAC1D,QAAM,OAAO;AACb,MAAI,QAAQ,KAAK,OAAO;AACxB,QAAM,UAAU,KAAK,OAAO,iBAAiB,KAAK,OAAO,eAAe,CAAC;AAEzE,MAAI;AAGJ,SAAO,SAAS;AACd,UAAM,QAAQ,KAAK,OAAO,KAAK,EAAE,CAAC;AAElC,QAAI,MAAM,SAAS,MAAM,YAAY;AACnC,mBAAa;AACb;AAAA,IACF;AAGA,QACE,MAAM,SAAS,qBACf,MAAM,SAAS,MAAM,aACrB,MAAM,SAAS,MAAM,SACrB,MAAM,SAAS,MAAM,SACrB,MAAM,SAAS,MAAM,MACrB;AACA;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AAKP,WAAS,MAAMC,OAAM;AACnB,OAAOA,UAAS,MAAM,oBAAoB,cAAc;AAExD,QAAI,CAAC,cAAc,CAAC,WAAW,WAAW;AACxC,aAAO,IAAIA,KAAI;AAAA,IACjB;AAEA,UAAM,KAAK;AAAA,MACT,KAAK,eAAe,EAAC,OAAO,WAAW,KAAK,KAAK,KAAK,IAAI,EAAC,CAAC;AAAA,IAC9D;AAEA,QAAI,GAAG,YAAY,CAAC,MAAM,MAAM,SAAS,CAAC,QAAQ,SAAS,GAAG,MAAM,CAAC,CAAC,GAAG;AACvE,aAAO,IAAIA,KAAI;AAAA,IACjB;AAEA,YAAQ,MAAM,4BAA4B;AAC1C,YAAQ,QAAQA,KAAI;AACpB,YAAQ,KAAK,4BAA4B;AACzC,WAAOD,IAAGC,KAAI;AAAA,EAChB;AACF;AAIA,SAAS,kCAAkC,QAAQ,SAAS;AAC1D,MAAI,QAAQ,OAAO;AAEnB,MAAI;AAGJ,SAAO,SAAS;AACd,QACE,OAAO,KAAK,EAAE,CAAC,EAAE,SAAS,MAAM,cAChC,OAAO,KAAK,EAAE,CAAC,MAAM,SACrB;AACA,mBAAa,OAAO,KAAK,EAAE,CAAC;AAC5B;AAAA,IACF;AAAA,EACF;AAEA,KAAO,YAAY,kCAAkC;AAGrD,SAAO,QAAQ,CAAC,EAAE,CAAC,EAAE,OAAO,MAAM;AAClC,SAAO,QAAQ,CAAC,EAAE,CAAC,EAAE,OAAO;AAI5B,QAAM,OAAO;AAAA,IACX,MAAM;AAAA,IACN,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,QAAQ,CAAC,EAAE,CAAC,EAAE,KAAK;AAAA,IACnD,KAAK,OAAO,OAAO,CAAC,GAAG,OAAO,OAAO,SAAS,CAAC,EAAE,CAAC,EAAE,GAAG;AAAA,EACzD;AAGA,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG;AAAA,IACjD,KAAK,OAAO,OAAO,CAAC,GAAG,OAAO,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG;AAAA,EACjD;AAEA,SAAO,IAAI;AACX,SAAO,IAAI;AACX,SAAO,IAAI;AAEX,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG;AAAA,IACnC,KAAK,OAAO,OAAO,CAAC,GAAG,OAAO,OAAO,SAAS,CAAC,EAAE,CAAC,EAAE,KAAK;AAAA,EAC3D;AAEA,QAAM,QAAQ;AAAA,IACZ,MAAM,MAAM;AAAA,IACZ,aAAa;AAAA,IACb,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,KAAK;AAAA,IACrC,KAAK,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG;AAAA,EACnC;AAGA,QAAM,cAAc;AAAA;AAAA,IAElB,OAAO,QAAQ,CAAC;AAAA,IAChB,OAAO,QAAQ,CAAC;AAAA,IAChB,CAAC,SAAS,MAAM,OAAO;AAAA;AAAA,IAEvB,OAAO,QAAQ,CAAC;AAAA,IAChB,OAAO,QAAQ,CAAC;AAAA;AAAA,IAEhB,CAAC,SAAS,QAAQ,OAAO;AAAA,IACzB,CAAC,QAAQ,QAAQ,OAAO;AAAA;AAAA,IAExB,CAAC,SAAS,QAAQ,OAAO;AAAA,IACzB,CAAC,SAAS,OAAO,OAAO;AAAA,IACxB,CAAC,QAAQ,OAAO,OAAO;AAAA,IACvB,CAAC,QAAQ,QAAQ,OAAO;AAAA;AAAA,IAExB,OAAO,OAAO,SAAS,CAAC;AAAA,IACxB,OAAO,OAAO,SAAS,CAAC;AAAA,IACxB,CAAC,QAAQ,MAAM,OAAO;AAAA,EACxB;AAEA,SAAO,OAAO,OAAO,OAAO,SAAS,QAAQ,GAAG,GAAG,WAAW;AAE9D,SAAO;AACT;AAMA,SAAS,wBAAwB,SAASD,KAAI,KAAK;AACjD,QAAM,OAAO;AACb,QAAM,UAAU,KAAK,OAAO,iBAAiB,KAAK,OAAO,eAAe,CAAC;AACzE,MAAI,OAAO;AAEX,MAAI;AAOJ,SAAO;AAYP,WAAS,MAAMC,OAAM;AACnB,OAAOA,UAAS,MAAM,mBAAmB,cAAc;AACvD,YAAQ,MAAM,iBAAiB;AAC/B,YAAQ,MAAM,4BAA4B;AAC1C,YAAQ,QAAQA,KAAI;AACpB,YAAQ,KAAK,4BAA4B;AACzC,WAAO;AAAA,EACT;AAYA,WAAS,UAAUA,OAAM;AACvB,QAAIA,UAAS,MAAM,MAAO,QAAO,IAAIA,KAAI;AAEzC,YAAQ,MAAM,uBAAuB;AACrC,YAAQ,QAAQA,KAAI;AACpB,YAAQ,KAAK,uBAAuB;AACpC,YAAQ,MAAM,uBAAuB;AACrC,YAAQ,MAAM,aAAa,EAAE,cAAc;AAC3C,WAAO;AAAA,EACT;AAYA,WAAS,SAASA,OAAM;AACtB;AAAA;AAAA,MAEE,OAAO,UAAU;AAAA,MAEhBA,UAAS,MAAM,sBAAsB,CAAC;AAAA;AAAA,MAGvCA,UAAS,MAAM,OACfA,UAAS,MAAM,qBACf,0BAA0BA,KAAI;AAAA,MAC9B;AACA,aAAO,IAAIA,KAAI;AAAA,IACjB;AAEA,QAAIA,UAAS,MAAM,oBAAoB;AACrC,cAAQ,KAAK,aAAa;AAC1B,YAAM,QAAQ,QAAQ,KAAK,uBAAuB;AAElD,UAAI,CAAC,QAAQ,SAAS,oBAAoB,KAAK,eAAe,KAAK,CAAC,CAAC,GAAG;AACtE,eAAO,IAAIA,KAAI;AAAA,MACjB;AAEA,cAAQ,MAAM,4BAA4B;AAC1C,cAAQ,QAAQA,KAAI;AACpB,cAAQ,KAAK,4BAA4B;AACzC,cAAQ,KAAK,iBAAiB;AAC9B,aAAOD;AAAA,IACT;AAEA,QAAI,CAAC,0BAA0BC,KAAI,GAAG;AACpC,aAAO;AAAA,IACT;AAEA;AACA,YAAQ,QAAQA,KAAI;AACpB,WAAOA,UAAS,MAAM,YAAY,aAAa;AAAA,EACjD;AAYA,WAAS,WAAWA,OAAM;AACxB,QACEA,UAAS,MAAM,qBACfA,UAAS,MAAM,aACfA,UAAS,MAAM,oBACf;AACA,cAAQ,QAAQA,KAAI;AACpB;AACA,aAAO;AAAA,IACT;AAEA,WAAO,SAASA,KAAI;AAAA,EACtB;AACF;AAMA,SAAS,wBAAwB,SAASD,KAAI,KAAK;AACjD,QAAM,OAAO;AACb,QAAM,UAAU,KAAK,OAAO,iBAAiB,KAAK,OAAO,eAAe,CAAC;AAEzE,MAAI;AACJ,MAAI,OAAO;AAEX,MAAI;AAEJ,SAAO;AAYP,WAAS,MAAMC,OAAM;AACnB,OAAOA,UAAS,MAAM,mBAAmB,cAAc;AACvD,YAAQ,MAAM,uBAAuB,EAAE,aAAa;AACpD,YAAQ,MAAM,4BAA4B;AAC1C,YAAQ,MAAM,kCAAkC;AAChD,YAAQ,QAAQA,KAAI;AACpB,YAAQ,KAAK,kCAAkC;AAC/C,WAAO;AAAA,EACT;AAYA,WAAS,cAAcA,OAAM;AAC3B,QAAIA,UAAS,MAAM,OAAO;AACxB,cAAQ,MAAM,6BAA6B;AAC3C,cAAQ,QAAQA,KAAI;AACpB,cAAQ,KAAK,6BAA6B;AAC1C,cAAQ,MAAM,kCAAkC;AAChD,cAAQ,MAAM,aAAa,EAAE,cAAc;AAC3C,aAAO;AAAA,IACT;AAEA,WAAO,IAAIA,KAAI;AAAA,EACjB;AAeA,WAAS,YAAYA,OAAM;AACzB;AAAA;AAAA,MAEE,OAAO,UAAU;AAAA,MAEhBA,UAAS,MAAM,sBAAsB,CAAC;AAAA;AAAA,MAGvCA,UAAS,MAAM,OACfA,UAAS,MAAM,qBACf,0BAA0BA,KAAI;AAAA,MAC9B;AACA,aAAO,IAAIA,KAAI;AAAA,IACjB;AAEA,QAAIA,UAAS,MAAM,oBAAoB;AACrC,cAAQ,KAAK,aAAa;AAC1B,YAAM,QAAQ,QAAQ,KAAK,kCAAkC;AAC7D,mBAAa,oBAAoB,KAAK,eAAe,KAAK,CAAC;AAC3D,cAAQ,MAAM,kCAAkC;AAChD,cAAQ,QAAQA,KAAI;AACpB,cAAQ,KAAK,kCAAkC;AAC/C,cAAQ,KAAK,4BAA4B;AACzC,aAAO;AAAA,IACT;AAEA,QAAI,CAAC,0BAA0BA,KAAI,GAAG;AACpC,aAAO;AAAA,IACT;AAEA;AACA,YAAQ,QAAQA,KAAI;AACpB,WAAOA,UAAS,MAAM,YAAY,cAAc;AAAA,EAClD;AAeA,WAAS,YAAYA,OAAM;AACzB,QACEA,UAAS,MAAM,qBACfA,UAAS,MAAM,aACfA,UAAS,MAAM,oBACf;AACA,cAAQ,QAAQA,KAAI;AACpB;AACA,aAAO;AAAA,IACT;AAEA,WAAO,YAAYA,KAAI;AAAA,EACzB;AAYA,WAAS,WAAWA,OAAM;AACxB,QAAIA,UAAS,MAAM,OAAO;AACxB,cAAQ,MAAM,kBAAkB;AAChC,cAAQ,QAAQA,KAAI;AACpB,cAAQ,KAAK,kBAAkB;AAE/B,UAAI,CAAC,QAAQ,SAAS,UAAU,GAAG;AACjC,gBAAQ,KAAK,UAAU;AAAA,MACzB;AAKA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAEA,WAAO,IAAIA,KAAI;AAAA,EACjB;AAYA,WAAS,gBAAgBA,OAAM;AAE7B,WAAOD,IAAGC,KAAI;AAAA,EAChB;AACF;AAMA,SAAS,+BAA+B,SAASD,KAAI,KAAK;AAUxD,SAAO,QAAQ,MAAM,WAAWA,KAAI,QAAQ,QAAQ,QAAQA,KAAI,GAAG,CAAC;AACtE;AAGA,SAAS,yBAAyB,SAAS;AACzC,UAAQ,KAAK,uBAAuB;AACtC;AAMA,SAAS,eAAe,SAASA,KAAI,KAAK;AACxC,QAAM,OAAO;AAEb,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,UAAU;AAAA,EACtB;AAKA,WAAS,YAAYC,OAAM;AACzB,UAAM,OAAO,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC;AAC/C,WAAO,QACL,KAAK,CAAC,EAAE,SAAS,iCACjB,KAAK,CAAC,EAAE,eAAe,KAAK,CAAC,GAAG,IAAI,EAAE,WAAW,UAAU,UACzDD,IAAGC,KAAI,IACP,IAAIA,KAAI;AAAA,EACd;AACF;;;ACpiBA,IAAMC,OAAM,CAAC,EAAE;;;ACWR,SAAS,iBAAiB,SAAS;AACxC,QAAM,WAAW,WAAW,CAAC;AAC7B,MAAI,SAAS,SAAS;AACtB,QAAM,YAAY;AAAA,IAChB,MAAM;AAAA,IACN,UAAU;AAAA,IACV,YAAY;AAAA,EACd;AAEA,MAAI,WAAW,QAAQ,WAAW,QAAW;AAC3C,aAAS;AAAA,EACX;AAEA,SAAO;AAAA,IACL,MAAM,EAAC,CAAC,MAAM,KAAK,GAAG,UAAS;AAAA,IAC/B,YAAY,EAAC,MAAM,CAAC,SAAS,EAAC;AAAA,IAC9B,kBAAkB,EAAC,MAAM,CAAC,MAAM,KAAK,EAAC;AAAA,EACxC;AAOA,WAAS,wBAAwB,QAAQ,SAAS;AAChD,QAAI,QAAQ;AAGZ,WAAO,EAAE,QAAQ,OAAO,QAAQ;AAE9B,UACE,OAAO,KAAK,EAAE,CAAC,MAAM,WACrB,OAAO,KAAK,EAAE,CAAC,EAAE,SAAS,oCAC1B,OAAO,KAAK,EAAE,CAAC,EAAE,QACjB;AACA,YAAI,OAAO;AAGX,eAAO,QAAQ;AAEb,cACE,OAAO,IAAI,EAAE,CAAC,MAAM,UACpB,OAAO,IAAI,EAAE,CAAC,EAAE,SAAS,oCACzB,OAAO,IAAI,EAAE,CAAC,EAAE;AAAA,UAEhB,OAAO,KAAK,EAAE,CAAC,EAAE,IAAI,SAAS,OAAO,KAAK,EAAE,CAAC,EAAE,MAAM,WACnD,OAAO,IAAI,EAAE,CAAC,EAAE,IAAI,SAAS,OAAO,IAAI,EAAE,CAAC,EAAE,MAAM,QACrD;AACA,mBAAO,KAAK,EAAE,CAAC,EAAE,OAAO;AACxB,mBAAO,IAAI,EAAE,CAAC,EAAE,OAAO;AAGvB,kBAAM,gBAAgB;AAAA,cACpB,MAAM;AAAA,cACN,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,IAAI,EAAE,CAAC,EAAE,KAAK;AAAA,cAC9C,KAAK,OAAO,OAAO,CAAC,GAAG,OAAO,KAAK,EAAE,CAAC,EAAE,GAAG;AAAA,YAC7C;AAGA,kBAAMC,QAAO;AAAA,cACX,MAAM;AAAA,cACN,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,IAAI,EAAE,CAAC,EAAE,GAAG;AAAA,cAC5C,KAAK,OAAO,OAAO,CAAC,GAAG,OAAO,KAAK,EAAE,CAAC,EAAE,KAAK;AAAA,YAC/C;AAIA,kBAAM,aAAa;AAAA,cACjB,CAAC,SAAS,eAAe,OAAO;AAAA,cAChC,CAAC,SAAS,OAAO,IAAI,EAAE,CAAC,GAAG,OAAO;AAAA,cAClC,CAAC,QAAQ,OAAO,IAAI,EAAE,CAAC,GAAG,OAAO;AAAA,cACjC,CAAC,SAASA,OAAM,OAAO;AAAA,YACzB;AAEA,kBAAM,aAAa,QAAQ,OAAO,WAAW,WAAW;AAExD,gBAAI,YAAY;AAEd;AAAA,gBACE;AAAA,gBACA,WAAW;AAAA,gBACX;AAAA,gBACA,WAAW,YAAY,OAAO,MAAM,OAAO,GAAG,KAAK,GAAG,OAAO;AAAA,cAC/D;AAAA,YACF;AAGA,mBAAO,YAAY,WAAW,QAAQ,GAAG;AAAA,cACvC,CAAC,QAAQA,OAAM,OAAO;AAAA,cACtB,CAAC,SAAS,OAAO,KAAK,EAAE,CAAC,GAAG,OAAO;AAAA,cACnC,CAAC,QAAQ,OAAO,KAAK,EAAE,CAAC,GAAG,OAAO;AAAA,cAClC,CAAC,QAAQ,eAAe,OAAO;AAAA,YACjC,CAAC;AAED,mBAAO,QAAQ,OAAO,GAAG,QAAQ,OAAO,GAAG,UAAU;AAErD,oBAAQ,OAAO,WAAW,SAAS;AACnC;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,YAAQ;AAER,WAAO,EAAE,QAAQ,OAAO,QAAQ;AAC9B,UAAI,OAAO,KAAK,EAAE,CAAC,EAAE,SAAS,kCAAkC;AAC9D,eAAO,KAAK,EAAE,CAAC,EAAE,OAAO,MAAM;AAAA,MAChC;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAMA,WAAS,sBAAsB,SAASC,KAAI,KAAK;AAC/C,UAAMC,YAAW,KAAK;AACtB,UAAM,SAAS,KAAK;AACpB,QAAI,OAAO;AAEX,WAAO;AAGP,aAAS,MAAMC,OAAM;AACnB,SAAOA,UAAS,MAAM,OAAO,cAAc;AAE3C,UACED,cAAa,MAAM,SACnB,OAAO,OAAO,SAAS,CAAC,EAAE,CAAC,EAAE,SAAS,MAAM,iBAC5C;AACA,eAAO,IAAIC,KAAI;AAAA,MACjB;AAEA,cAAQ,MAAM,gCAAgC;AAC9C,aAAO,KAAKA,KAAI;AAAA,IAClB;AAGA,aAAS,KAAKA,OAAM;AAClB,YAAM,SAAS,kBAAkBD,SAAQ;AAEzC,UAAIC,UAAS,MAAM,OAAO;AAExB,YAAI,OAAO,EAAG,QAAO,IAAIA,KAAI;AAC7B,gBAAQ,QAAQA,KAAI;AACpB;AACA,eAAO;AAAA,MACT;AAEA,UAAI,OAAO,KAAK,CAAC,OAAQ,QAAO,IAAIA,KAAI;AACxC,YAAM,QAAQ,QAAQ,KAAK,gCAAgC;AAC3D,YAAM,QAAQ,kBAAkBA,KAAI;AACpC,YAAM,QACJ,CAAC,SAAU,UAAU,UAAU,sBAAsB,QAAQ,MAAM;AACrE,YAAM,SACJ,CAAC,UAAW,WAAW,UAAU,sBAAsB,QAAQ,KAAK;AACtE,aAAOF,IAAGE,KAAI;AAAA,IAChB;AAAA,EACF;AACF;;;AC7JO,IAAM,UAAN,MAAc;AAAA;AAAA;AAAA;AAAA,EAInB,cAAc;AAMZ,SAAK,MAAM,CAAC;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,IAAI,OAAO,QAAQ,KAAK;AACtB,sBAAkB,MAAM,OAAO,QAAQ,GAAG;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBA,QAAQ,QAAQ;AACd,SAAK,IAAI,KAAK,SAAU,GAAG,GAAG;AAC5B,aAAO,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IACnB,CAAC;AAGD,QAAI,KAAK,IAAI,WAAW,GAAG;AACzB;AAAA,IACF;AAoBA,QAAI,QAAQ,KAAK,IAAI;AAErB,UAAM,OAAO,CAAC;AACd,WAAO,QAAQ,GAAG;AAChB,eAAS;AACT,WAAK;AAAA,QACH,OAAO,MAAM,KAAK,IAAI,KAAK,EAAE,CAAC,IAAI,KAAK,IAAI,KAAK,EAAE,CAAC,CAAC;AAAA,QACpD,KAAK,IAAI,KAAK,EAAE,CAAC;AAAA,MACnB;AAGA,aAAO,SAAS,KAAK,IAAI,KAAK,EAAE,CAAC;AAAA,IACnC;AAEA,SAAK,KAAK,OAAO,MAAM,CAAC;AACxB,WAAO,SAAS;AAEhB,QAAI,QAAQ,KAAK,IAAI;AAErB,WAAO,OAAO;AACZ,iBAAW,WAAW,OAAO;AAC3B,eAAO,KAAK,OAAO;AAAA,MACrB;AAEA,cAAQ,KAAK,IAAI;AAAA,IACnB;AAGA,SAAK,IAAI,SAAS;AAAA,EACpB;AACF;AAWA,SAAS,kBAAkB,SAAS,IAAI,QAAQ,KAAK;AACnD,MAAI,QAAQ;AAGZ,MAAI,WAAW,KAAK,IAAI,WAAW,GAAG;AACpC;AAAA,EACF;AAEA,SAAO,QAAQ,QAAQ,IAAI,QAAQ;AACjC,QAAI,QAAQ,IAAI,KAAK,EAAE,CAAC,MAAM,IAAI;AAChC,cAAQ,IAAI,KAAK,EAAE,CAAC,KAAK;AAOzB,cAAQ,IAAI,KAAK,EAAE,CAAC,EAAE,KAAK,GAAG,GAAG;AAGjC;AAAA,IACF;AAEA,aAAS;AAAA,EACX;AAEA,UAAQ,IAAI,KAAK,CAAC,IAAI,QAAQ,GAAG,CAAC;AACpC;;;ACjJO,SAAS,cAAc,QAAQ,OAAO;AAC3C,KAAO,OAAO,KAAK,EAAE,CAAC,EAAE,SAAS,SAAS,gBAAgB;AAC1D,MAAI,iBAAiB;AAErB,QAAM,QAAQ,CAAC;AAEf,SAAO,QAAQ,OAAO,QAAQ;AAC5B,UAAM,QAAQ,OAAO,KAAK;AAE1B,QAAI,gBAAgB;AAClB,UAAI,MAAM,CAAC,MAAM,SAAS;AAGxB,YAAI,MAAM,CAAC,EAAE,SAAS,gBAAgB;AACpC,gBAAM;AAAA,YACJ,OAAO,QAAQ,CAAC,EAAE,CAAC,EAAE,SAAS,yBAC1B,SACA;AAAA,UACN;AAAA,QACF;AAAA,MACF,WAIS,MAAM,CAAC,EAAE,SAAS,gBAAgB;AACzC,YAAI,OAAO,QAAQ,CAAC,EAAE,CAAC,EAAE,SAAS,wBAAwB;AACxD,gBAAM,aAAa,MAAM,SAAS;AAElC,gBAAM,UAAU,IAAI,MAAM,UAAU,MAAM,SAAS,WAAW;AAAA,QAChE;AAAA,MACF,WAES,MAAM,CAAC,EAAE,SAAS,qBAAqB;AAC9C;AAAA,MACF;AAAA,IACF,WAAW,MAAM,CAAC,MAAM,WAAW,MAAM,CAAC,EAAE,SAAS,qBAAqB;AACxE,uBAAiB;AAAA,IACnB;AAEA,aAAS;AAAA,EACX;AAEA,SAAO;AACT;;;ACjCO,SAAS,WAAW;AACzB,SAAO;AAAA,IACL,MAAM;AAAA,MACJ,MAAM,EAAC,MAAM,SAAS,UAAU,eAAe,YAAY,aAAY;AAAA,IACzE;AAAA,EACF;AACF;AAMA,SAAS,cAAc,SAASC,KAAI,KAAK;AACvC,QAAM,OAAO;AACb,MAAI,OAAO;AACX,MAAI,QAAQ;AAEZ,MAAI;AAEJ,SAAO;AAkBP,WAAS,MAAMC,OAAM;AACnB,QAAI,QAAQ,KAAK,OAAO,SAAS;AAEjC,WAAO,QAAQ,IAAI;AACjB,YAAM,OAAO,KAAK,OAAO,KAAK,EAAE,CAAC,EAAE;AACnC,UACE,SAAS,MAAM;AAAA,MAEf,SAAS,MAAM;AAEf;AAAA,UACG;AAAA,IACP;AAEA,UAAM,OAAO,QAAQ,KAAK,KAAK,OAAO,KAAK,EAAE,CAAC,EAAE,OAAO;AAEvD,UAAM,OACJ,SAAS,eAAe,SAAS,aAAa,eAAe;AAG/D,QAAI,SAAS,gBAAgB,KAAK,OAAO,KAAK,KAAK,IAAI,EAAE,IAAI,GAAG;AAC9D,aAAO,IAAIA,KAAI;AAAA,IACjB;AAEA,WAAO,KAAKA,KAAI;AAAA,EAClB;AAcA,WAAS,cAAcA,OAAM;AAC3B,YAAQ,MAAM,WAAW;AACzB,YAAQ,MAAM,UAAU;AACxB,WAAO,aAAaA,KAAI;AAAA,EAC1B;AAcA,WAAS,aAAaA,OAAM;AAC1B,QAAIA,UAAS,MAAM,aAAa;AAC9B,aAAO,aAAaA,KAAI;AAAA,IAC1B;AAYA,WAAO;AAEP,aAAS;AACT,WAAO,aAAaA,KAAI;AAAA,EAC1B;AAgBA,WAAS,aAAaA,OAAM;AAC1B,QAAIA,UAAS,MAAM,KAAK;AAEtB,aAAO,IAAIA,KAAI;AAAA,IACjB;AAEA,QAAI,mBAAmBA,KAAI,GAAG;AAE5B,UAAI,QAAQ,GAAG;AACb,gBAAQ;AAGR,aAAK,YAAY;AACjB,gBAAQ,KAAK,UAAU;AACvB,gBAAQ,MAAM,MAAM,UAAU;AAC9B,gBAAQ,QAAQA,KAAI;AACpB,gBAAQ,KAAK,MAAM,UAAU;AAC7B,eAAO;AAAA,MACT;AAGA,aAAO,IAAIA,KAAI;AAAA,IACjB;AAEA,QAAI,cAAcA,KAAI,GAAG;AAIvB,aAAO,aAAa,SAAS,cAAc,MAAM,UAAU,EAAEA,KAAI;AAAA,IACnE;AAEA,aAAS;AAET,QAAI,MAAM;AACR,aAAO;AAEP,cAAQ;AAAA,IACV;AAEA,QAAIA,UAAS,MAAM,aAAa;AAC9B,cAAQ,MAAM,kBAAkB;AAChC,cAAQ,QAAQA,KAAI;AACpB,cAAQ,KAAK,kBAAkB;AAE/B,aAAO;AACP,aAAO;AAAA,IACT;AAGA,YAAQ,MAAM,MAAM,IAAI;AACxB,WAAO,YAAYA,KAAI;AAAA,EACzB;AAcA,WAAS,YAAYA,OAAM;AACzB,QACEA,UAAS,MAAM,OACfA,UAAS,MAAM,eACf,0BAA0BA,KAAI,GAC9B;AACA,cAAQ,KAAK,MAAM,IAAI;AACvB,aAAO,aAAaA,KAAI;AAAA,IAC1B;AAEA,YAAQ,QAAQA,KAAI;AACpB,WAAOA,UAAS,MAAM,YAAY,gBAAgB;AAAA,EACpD;AAcA,WAAS,cAAcA,OAAM;AAC3B,QAAIA,UAAS,MAAM,aAAaA,UAAS,MAAM,aAAa;AAC1D,cAAQ,QAAQA,KAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,YAAYA,KAAI;AAAA,EACzB;AAcA,WAAS,mBAAmBA,OAAM;AAEhC,SAAK,YAAY;AAGjB,QAAI,KAAK,OAAO,KAAK,KAAK,IAAI,EAAE,IAAI,GAAG;AACrC,aAAO,IAAIA,KAAI;AAAA,IACjB;AAEA,YAAQ,MAAM,mBAAmB;AAEjC,WAAO;AAEP,QAAI,cAAcA,KAAI,GAAG;AACvB,SAAO,KAAK,OAAO,WAAW,QAAQ,MAAM,0BAA0B;AACtE,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,MAAM;AAAA,QACN,KAAK,OAAO,WAAW,QAAQ,KAAK,SAAS,cAAc,IACvD,SACA,UAAU;AAAA,MAChB,EAAEA,KAAI;AAAA,IACR;AAEA,WAAO,oBAAoBA,KAAI;AAAA,EACjC;AAgBA,WAAS,oBAAoBA,OAAM;AACjC,QAAIA,UAAS,MAAM,QAAQA,UAAS,MAAM,OAAO;AAC/C,aAAO,yBAAyBA,KAAI;AAAA,IACtC;AAEA,QAAIA,UAAS,MAAM,aAAa;AAC9B,aAAO;AAEP,cAAQ,MAAM,kBAAkB;AAChC,cAAQ,QAAQA,KAAI;AACpB,cAAQ,KAAK,kBAAkB;AAC/B,aAAO;AAAA,IACT;AAGA,WAAO,iBAAiBA,KAAI;AAAA,EAC9B;AAaA,WAAS,wBAAwBA,OAAM;AACrC,QAAI,cAAcA,KAAI,GAAG;AACvB,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR,EAAEA,KAAI;AAAA,IACR;AAEA,WAAO,yBAAyBA,KAAI;AAAA,EACtC;AAaA,WAAS,yBAAyBA,OAAM;AAEtC,QAAIA,UAAS,MAAM,OAAO;AACxB,eAAS;AACT,aAAO;AAEP,cAAQ,MAAM,sBAAsB;AACpC,cAAQ,QAAQA,KAAI;AACpB,cAAQ,KAAK,sBAAsB;AACnC,aAAO;AAAA,IACT;AAGA,QAAIA,UAAS,MAAM,MAAM;AACvB,eAAS;AAET,aAAO,gCAAgCA,KAAI;AAAA,IAC7C;AAEA,QAAIA,UAAS,MAAM,OAAO,mBAAmBA,KAAI,GAAG;AAClD,aAAO,uBAAuBA,KAAI;AAAA,IACpC;AAEA,WAAO,iBAAiBA,KAAI;AAAA,EAC9B;AAaA,WAAS,gCAAgCA,OAAM;AAC7C,QAAIA,UAAS,MAAM,MAAM;AACvB,cAAQ,MAAM,sBAAsB;AACpC,aAAO,oBAAoBA,KAAI;AAAA,IACjC;AAGA,WAAO,iBAAiBA,KAAI;AAAA,EAC9B;AAaA,WAAS,oBAAoBA,OAAM;AACjC,QAAIA,UAAS,MAAM,MAAM;AACvB,cAAQ,QAAQA,KAAI;AACpB,aAAO;AAAA,IACT;AAGA,QAAIA,UAAS,MAAM,OAAO;AACxB,aAAO;AACP,cAAQ,KAAK,sBAAsB;AACnC,cAAQ,MAAM,sBAAsB;AACpC,cAAQ,QAAQA,KAAI;AACpB,cAAQ,KAAK,sBAAsB;AACnC,aAAO;AAAA,IACT;AAEA,YAAQ,KAAK,sBAAsB;AACnC,WAAO,iCAAiCA,KAAI;AAAA,EAC9C;AAaA,WAAS,iCAAiCA,OAAM;AAC9C,QAAI,cAAcA,KAAI,GAAG;AACvB,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR,EAAEA,KAAI;AAAA,IACR;AAEA,WAAO,uBAAuBA,KAAI;AAAA,EACpC;AAaA,WAAS,uBAAuBA,OAAM;AACpC,QAAIA,UAAS,MAAM,aAAa;AAC9B,aAAO,oBAAoBA,KAAI;AAAA,IACjC;AAEA,QAAIA,UAAS,MAAM,OAAO,mBAAmBA,KAAI,GAAG;AAKlD,UAAI,CAAC,QAAQ,SAAS,OAAO;AAC3B,eAAO,iBAAiBA,KAAI;AAAA,MAC9B;AAGA,cAAQ,KAAK,mBAAmB;AAChC,cAAQ,KAAK,WAAW;AAGxB,aAAOD,IAAGC,KAAI;AAAA,IAChB;AAEA,WAAO,iBAAiBA,KAAI;AAAA,EAC9B;AAaA,WAAS,iBAAiBA,OAAM;AAE9B,WAAO,IAAIA,KAAI;AAAA,EACjB;AAcA,WAAS,aAAaA,OAAM;AAI1B,YAAQ,MAAM,UAAU;AACxB,WAAO,aAAaA,KAAI;AAAA,EAC1B;AAgBA,WAAS,aAAaA,OAAM;AAC1B,QAAIA,UAAS,MAAM,aAAa;AAC9B,cAAQ,MAAM,kBAAkB;AAChC,cAAQ,QAAQA,KAAI;AACpB,cAAQ,KAAK,kBAAkB;AAC/B,aAAO;AAAA,IACT;AAEA,QAAIA,UAAS,MAAM,OAAO,mBAAmBA,KAAI,GAAG;AAClD,cAAQ,KAAK,UAAU;AACvB,aAAOD,IAAGC,KAAI;AAAA,IAChB;AAEA,QAAI,cAAcA,KAAI,GAAG;AACvB,aAAO,aAAa,SAAS,cAAc,MAAM,UAAU,EAAEA,KAAI;AAAA,IACnE;AAGA,YAAQ,MAAM,MAAM,IAAI;AACxB,WAAO,YAAYA,KAAI;AAAA,EACzB;AAcA,WAAS,YAAYA,OAAM;AACzB,QACEA,UAAS,MAAM,OACfA,UAAS,MAAM,eACf,0BAA0BA,KAAI,GAC9B;AACA,cAAQ,KAAK,MAAM,IAAI;AACvB,aAAO,aAAaA,KAAI;AAAA,IAC1B;AAEA,YAAQ,QAAQA,KAAI;AACpB,WAAOA,UAAS,MAAM,YAAY,gBAAgB;AAAA,EACpD;AAcA,WAAS,cAAcA,OAAM;AAC3B,QAAIA,UAAS,MAAM,aAAaA,UAAS,MAAM,aAAa;AAC1D,cAAQ,QAAQA,KAAI;AACpB,aAAO;AAAA,IACT;AAEA,WAAO,YAAYA,KAAI;AAAA,EACzB;AACF;AAIA,SAAS,aAAa,QAAQ,SAAS;AACrC,MAAI,QAAQ;AACZ,MAAI,0BAA0B;AAE9B,MAAI,UAAU;AAEd,MAAI,WAAW,CAAC,GAAG,GAAG,GAAG,CAAC;AAE1B,MAAI,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AACtB,MAAI,gCAAgC;AACpC,MAAI,eAAe;AAEnB,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAEJ,QAAMC,OAAM,IAAI,QAAQ;AAExB,SAAO,EAAE,QAAQ,OAAO,QAAQ;AAC9B,UAAM,QAAQ,OAAO,KAAK;AAC1B,UAAM,QAAQ,MAAM,CAAC;AAErB,QAAI,MAAM,CAAC,MAAM,SAAS;AAExB,UAAI,MAAM,SAAS,aAAa;AAC9B,wCAAgC;AAGhC,YAAI,iBAAiB,GAAG;AACtB,aAAO,cAAc,iCAAiC;AACtD,wBAAcA,MAAK,SAAS,cAAc,cAAc,WAAW;AACnE,wBAAc;AACd,yBAAe;AAAA,QACjB;AAGA,uBAAe;AAAA,UACb,MAAM;AAAA,UACN,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,KAAK;AAAA;AAAA,UAEpC,KAAK,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG;AAAA,QAClC;AACA,QAAAA,KAAI,IAAI,OAAO,GAAG,CAAC,CAAC,SAAS,cAAc,OAAO,CAAC,CAAC;AAAA,MACtD,WACE,MAAM,SAAS,cACf,MAAM,SAAS,qBACf;AACA,kCAA0B;AAC1B,sBAAc;AACd,mBAAW,CAAC,GAAG,GAAG,GAAG,CAAC;AACtB,eAAO,CAAC,GAAG,QAAQ,GAAG,GAAG,CAAC;AAG1B,YAAI,+BAA+B;AACjC,0CAAgC;AAChC,wBAAc;AAAA,YACZ,MAAM;AAAA,YACN,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,KAAK;AAAA;AAAA,YAEpC,KAAK,OAAO,OAAO,CAAC,GAAG,MAAM,GAAG;AAAA,UAClC;AACA,UAAAA,KAAI,IAAI,OAAO,GAAG,CAAC,CAAC,SAAS,aAAa,OAAO,CAAC,CAAC;AAAA,QACrD;AAEA,kBAAU,MAAM,SAAS,sBAAsB,IAAI,cAAc,IAAI;AAAA,MACvE,WAGE,YACC,MAAM,SAAS,MAAM,QACpB,MAAM,SAAS,0BACf,MAAM,SAAS,yBACjB;AACA,kCAA0B;AAG1B,YAAI,KAAK,CAAC,MAAM,GAAG;AACjB,cAAI,SAAS,CAAC,MAAM,GAAG;AACrB,iBAAK,CAAC,IAAI,KAAK,CAAC;AAChB,0BAAc;AAAA,cACZA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF;AACA,uBAAW,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,UACxB;AAEA,eAAK,CAAC,IAAI;AAAA,QACZ;AAAA,MACF,WAAW,MAAM,SAAS,oBAAoB;AAC5C,YAAI,yBAAyB;AAC3B,oCAA0B;AAAA,QAC5B,OAAO;AACL,cAAI,SAAS,CAAC,MAAM,GAAG;AACrB,iBAAK,CAAC,IAAI,KAAK,CAAC;AAChB,0BAAc;AAAA,cACZA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAEA,qBAAW;AACX,iBAAO,CAAC,SAAS,CAAC,GAAG,OAAO,GAAG,CAAC;AAAA,QAClC;AAAA,MACF;AAAA,IACF,WAES,MAAM,SAAS,aAAa;AACnC,sCAAgC;AAChC,qBAAe;AAAA,IACjB,WACE,MAAM,SAAS,cACf,MAAM,SAAS,qBACf;AACA,qBAAe;AAEf,UAAI,SAAS,CAAC,MAAM,GAAG;AACrB,aAAK,CAAC,IAAI,KAAK,CAAC;AAChB,sBAAc;AAAA,UACZA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF,WAAW,KAAK,CAAC,MAAM,GAAG;AACxB,sBAAc,UAAUA,MAAK,SAAS,MAAM,SAAS,OAAO,WAAW;AAAA,MACzE;AAEA,gBAAU;AAAA,IACZ,WACE,YACC,MAAM,SAAS,MAAM,QACpB,MAAM,SAAS,0BACf,MAAM,SAAS,yBACjB;AACA,WAAK,CAAC,IAAI;AAAA,IACZ;AAAA,EACF;AAEA,MAAI,iBAAiB,GAAG;AACtB,OAAO,cAAc,wBAAwB;AAC7C,kBAAcA,MAAK,SAAS,cAAc,cAAc,WAAW;AAAA,EACrE;AAEA,EAAAA,KAAI,QAAQ,QAAQ,MAAM;AAK1B,UAAQ;AACR,SAAO,EAAE,QAAQ,QAAQ,OAAO,QAAQ;AACtC,UAAM,QAAQ,QAAQ,OAAO,KAAK;AAClC,QAAI,MAAM,CAAC,MAAM,WAAW,MAAM,CAAC,EAAE,SAAS,SAAS;AACrD,YAAM,CAAC,EAAE,SAAS,cAAc,QAAQ,QAAQ,KAAK;AAAA,IACvD;AAAA,EACF;AAEA,SAAO;AACT;AAcA,SAAS,UAAUA,MAAK,SAAS,OAAO,SAAS,QAAQ,cAAc;AAGrE,QAAM,YACJ,YAAY,IACR,gBACA,YAAY,IACV,mBACA;AAGR,QAAM,YAAY;AASlB,MAAI,MAAM,CAAC,MAAM,GAAG;AAClB,OAAO,cAAc,8BAA8B;AACnD,iBAAa,MAAM,OAAO,OAAO,CAAC,GAAG,SAAS,QAAQ,QAAQ,MAAM,CAAC,CAAC,CAAC;AACvE,IAAAA,KAAI,IAAI,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,cAAc,OAAO,CAAC,CAAC;AAAA,EACxD;AASA,QAAM,MAAM,SAAS,QAAQ,QAAQ,MAAM,CAAC,CAAC;AAC7C,iBAAe;AAAA,IACb,MAAM;AAAA,IACN,OAAO,OAAO,OAAO,CAAC,GAAG,GAAG;AAAA;AAAA,IAE5B,KAAK,OAAO,OAAO,CAAC,GAAG,GAAG;AAAA,EAC5B;AACA,EAAAA,KAAI,IAAI,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,SAAS,cAAc,OAAO,CAAC,CAAC;AAWvD,MAAI,MAAM,CAAC,MAAM,GAAG;AAClB,UAAM,eAAe,SAAS,QAAQ,QAAQ,MAAM,CAAC,CAAC;AACtD,UAAM,aAAa,SAAS,QAAQ,QAAQ,MAAM,CAAC,CAAC;AAEpD,UAAM,aAAa;AAAA,MACjB,MAAM;AAAA,MACN,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY;AAAA,MACrC,KAAK,OAAO,OAAO,CAAC,GAAG,UAAU;AAAA,IACnC;AACA,IAAAA,KAAI,IAAI,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,SAAS,YAAY,OAAO,CAAC,CAAC;AACrD,OAAO,MAAM,CAAC,MAAM,CAAC;AAErB,QAAI,YAAY,GAAG;AAEjB,YAAM,QAAQ,QAAQ,OAAO,MAAM,CAAC,CAAC;AACrC,YAAM,MAAM,QAAQ,OAAO,MAAM,CAAC,CAAC;AACnC,YAAM,CAAC,EAAE,MAAM,OAAO,OAAO,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG;AAC3C,YAAM,CAAC,EAAE,OAAO,MAAM;AACtB,YAAM,CAAC,EAAE,cAAc,UAAU;AAGjC,UAAI,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG;AAC3B,cAAM,IAAI,MAAM,CAAC,IAAI;AACrB,cAAM,IAAI,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI;AAChC,QAAAA,KAAI,IAAI,GAAG,GAAG,CAAC,CAAC;AAAA,MAClB;AAAA,IACF;AAEA,IAAAA,KAAI,IAAI,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,QAAQ,YAAY,OAAO,CAAC,CAAC;AAAA,EAC1D;AASA,MAAI,WAAW,QAAW;AACxB,iBAAa,MAAM,OAAO,OAAO,CAAC,GAAG,SAAS,QAAQ,QAAQ,MAAM,CAAC;AACrE,IAAAA,KAAI,IAAI,QAAQ,GAAG,CAAC,CAAC,QAAQ,cAAc,OAAO,CAAC,CAAC;AACpD,mBAAe;AAAA,EACjB;AAEA,SAAO;AACT;AAYA,SAAS,cAAcA,MAAK,SAAS,OAAO,OAAO,WAAW;AAE5D,QAAM,QAAQ,CAAC;AACf,QAAM,UAAU,SAAS,QAAQ,QAAQ,KAAK;AAE9C,MAAI,WAAW;AACb,cAAU,MAAM,OAAO,OAAO,CAAC,GAAG,OAAO;AACzC,UAAM,KAAK,CAAC,QAAQ,WAAW,OAAO,CAAC;AAAA,EACzC;AAEA,QAAM,MAAM,OAAO,OAAO,CAAC,GAAG,OAAO;AACrC,QAAM,KAAK,CAAC,QAAQ,OAAO,OAAO,CAAC;AAEnC,EAAAA,KAAI,IAAI,QAAQ,GAAG,GAAG,KAAK;AAC7B;AAOA,SAAS,SAAS,QAAQ,OAAO;AAC/B,QAAM,QAAQ,OAAO,KAAK;AAC1B,QAAM,OAAO,MAAM,CAAC,MAAM,UAAU,UAAU;AAC9C,SAAO,MAAM,CAAC,EAAE,IAAI;AACtB;;;ACp6BA,IAAM,SACJ;AAIF,IAAM,SAAS,IAAI,OAAO,MAAM,OAAO,QAAQ,GAAG;;;ACAlD,IAAM,gBAAgB,EAAC,MAAM,iBAAiB,UAAU,sBAAqB;AAUtE,SAAS,kBAAkB;AAChC,SAAO;AAAA,IACL,MAAM,EAAC,CAAC,MAAM,iBAAiB,GAAG,cAAa;AAAA,EACjD;AACF;AAMA,SAAS,sBAAsB,SAASC,KAAI,KAAK;AAC/C,QAAM,OAAO;AAEb,SAAO;AAYP,WAAS,KAAKC,OAAM;AAClB,OAAOA,UAAS,MAAM,mBAAmB,cAAc;AAEvD;AAAA;AAAA,MAEE,KAAK,aAAa,MAAM;AAAA;AAAA,MAGxB,CAAC,KAAK;AAAA,MACN;AACA,aAAO,IAAIA,KAAI;AAAA,IACjB;AAEA,YAAQ,MAAM,eAAe;AAC7B,YAAQ,MAAM,qBAAqB;AACnC,YAAQ,QAAQA,KAAI;AACpB,YAAQ,KAAK,qBAAqB;AAClC,WAAO;AAAA,EACT;AAYA,WAAS,OAAOA,OAAM;AAIpB,QAAI,0BAA0BA,KAAI,GAAG;AACnC,cAAQ,MAAM,6BAA6B;AAC3C,cAAQ,QAAQA,KAAI;AACpB,cAAQ,KAAK,6BAA6B;AAC1C,aAAO;AAAA,IACT;AAEA,QAAIA,UAAS,MAAM,cAAcA,UAAS,MAAM,YAAY;AAC1D,cAAQ,MAAM,2BAA2B;AACzC,cAAQ,QAAQA,KAAI;AACpB,cAAQ,KAAK,2BAA2B;AACxC,aAAO;AAAA,IACT;AAEA,WAAO,IAAIA,KAAI;AAAA,EACjB;AAYA,WAAS,MAAMA,OAAM;AACnB,QAAIA,UAAS,MAAM,oBAAoB;AACrC,cAAQ,MAAM,qBAAqB;AACnC,cAAQ,QAAQA,KAAI;AACpB,cAAQ,KAAK,qBAAqB;AAClC,cAAQ,KAAK,eAAe;AAC5B,aAAO;AAAA,IACT;AAEA,WAAO,IAAIA,KAAI;AAAA,EACjB;AAKA,WAAS,MAAMA,OAAM;AAEnB,QAAI,mBAAmBA,KAAI,GAAG;AAC5B,aAAOD,IAAGC,KAAI;AAAA,IAChB;AAIA,QAAI,cAAcA,KAAI,GAAG;AACvB,aAAO,QAAQ,MAAM,EAAC,UAAU,kBAAiB,GAAGD,KAAI,GAAG,EAAEC,KAAI;AAAA,IACnE;AAGA,WAAO,IAAIA,KAAI;AAAA,EACjB;AACF;AAMA,SAAS,kBAAkB,SAASD,KAAI,KAAK;AAC3C,SAAO,aAAa,SAAS,OAAO,MAAM,UAAU;AAYpD,WAAS,MAAMC,OAAM;AAKnB,WAAOA,UAAS,MAAM,MAAM,IAAIA,KAAI,IAAID,IAAGC,KAAI;AAAA,EACjD;AACF;;;AC/HO,SAAS,IAAI,SAAS;AAC3B,SAAO,kBAAkB;AAAA,IACvB,mBAAmB;AAAA,IACnB,YAAY;AAAA,IACZ,iBAAiB,OAAO;AAAA,IACxB,SAAS;AAAA,IACT,gBAAgB;AAAA,EAClB,CAAC;AACH;;;AClCA,IAAM,eAAe,CAAC;AAWP,SAAR,UAA2B,SAAS;AAGzC,QAAM;AAAA;AAAA,IAAuC;AAAA;AAC7C,QAAM,WAAW,WAAW;AAC5B,QAAM,OAAO,KAAK,KAAK;AAEvB,QAAM,sBACJ,KAAK,wBAAwB,KAAK,sBAAsB,CAAC;AAC3D,QAAM,yBACJ,KAAK,2BAA2B,KAAK,yBAAyB,CAAC;AACjE,QAAM,uBACJ,KAAK,yBAAyB,KAAK,uBAAuB,CAAC;AAE7D,sBAAoB,KAAK,IAAI,QAAQ,CAAC;AACtC,yBAAuB,KAAK,gBAAgB,CAAC;AAC7C,uBAAqB,KAAK,cAAc,QAAQ,CAAC;AACnD;", "names": ["list", "replace", "domain", "path", "trail", "code", "exit", "exit", "row", "sizes", "columnIndex", "code", "own", "exit", "list", "exit", "value", "map", "exit", "code", "exit", "node", "exit", "subexit", "value", "exit", "exit", "exit", "value", "exit", "text", "exit", "exit", "map", "exit", "exit", "exit", "text", "code", "ok", "trail", "ok", "code", "own", "text", "ok", "previous", "code", "ok", "code", "map", "ok", "code"]}