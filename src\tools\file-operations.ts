import { readFile, writeFile, readdir, stat, mkdir, unlink, rmdir } from 'fs/promises';
import { join, dirname } from 'path';
import { v4 as uuidv4 } from 'uuid';
import type { Tool, ToolResult, FileInfo, DirectoryListing } from '../types';

const readFileTool: Tool = {
  name: 'read_file',
  description: 'Read the contents of a file',
  schema: {
    name: 'read_file',
    description: 'Read the contents of a file',
    parameters: {
      type: 'object',
      properties: {
        path: {
          type: 'string',
          description: 'Path to the file to read'
        },
        encoding: {
          type: 'string',
          description: 'File encoding (default: utf8)',
          enum: ['utf8', 'ascii', 'base64', 'hex']
        }
      },
      required: ['path']
    }
  },

  async execute(args: Record<string, any>): Promise<ToolResult> {
    const { path, encoding = 'utf8' } = args;
    const id = uuidv4();
    const startTime = Date.now();

    try {
      const content = await readFile(path, encoding as BufferEncoding);
      
      return {
        id,
        success: true,
        output: content,
        executionTime: Date.now() - startTime
      };
    } catch (error: any) {
      return {
        id,
        success: false,
        output: '',
        error: `Failed to read file: ${error.message}`,
        executionTime: Date.now() - startTime
      };
    }
  }
};

const writeFileTool: Tool = {
  name: 'write_file',
  description: 'Write content to a file',
  schema: {
    name: 'write_file',
    description: 'Write content to a file',
    parameters: {
      type: 'object',
      properties: {
        path: {
          type: 'string',
          description: 'Path to the file to write'
        },
        content: {
          type: 'string',
          description: 'Content to write to the file'
        },
        encoding: {
          type: 'string',
          description: 'File encoding (default: utf8)',
          enum: ['utf8', 'ascii', 'base64', 'hex']
        },
        createDirectories: {
          type: 'boolean',
          description: 'Create parent directories if they don\'t exist (default: false)'
        }
      },
      required: ['path', 'content']
    }
  },

  async execute(args: Record<string, any>): Promise<ToolResult> {
    const { path, content, encoding = 'utf8', createDirectories = false } = args;
    const id = uuidv4();
    const startTime = Date.now();

    try {
      if (createDirectories) {
        const dir = dirname(path);
        await mkdir(dir, { recursive: true });
      }

      await writeFile(path, content, encoding as BufferEncoding);
      
      return {
        id,
        success: true,
        output: `File written successfully: ${path}`,
        executionTime: Date.now() - startTime
      };
    } catch (error: any) {
      return {
        id,
        success: false,
        output: '',
        error: `Failed to write file: ${error.message}`,
        executionTime: Date.now() - startTime
      };
    }
  }
};

const listDirectoryTool: Tool = {
  name: 'list_directory',
  description: 'List files and directories in a given path',
  schema: {
    name: 'list_directory',
    description: 'List files and directories in a given path',
    parameters: {
      type: 'object',
      properties: {
        path: {
          type: 'string',
          description: 'Path to the directory to list'
        },
        recursive: {
          type: 'boolean',
          description: 'List files recursively (default: false)'
        },
        showHidden: {
          type: 'boolean',
          description: 'Show hidden files and directories (default: false)'
        },
        details: {
          type: 'boolean',
          description: 'Include detailed file information (default: false)'
        }
      },
      required: ['path']
    }
  },

  async execute(args: Record<string, any>): Promise<ToolResult> {
    const { path, recursive = false, showHidden = false, details = false } = args;
    const id = uuidv4();
    const startTime = Date.now();

    try {
      const listing = await listDirectory(path, recursive, showHidden, details);
      
      return {
        id,
        success: true,
        output: JSON.stringify(listing, null, 2),
        executionTime: Date.now() - startTime
      };
    } catch (error: any) {
      return {
        id,
        success: false,
        output: '',
        error: `Failed to list directory: ${error.message}`,
        executionTime: Date.now() - startTime
      };
    }
  }
};

const replaceInFileTool: Tool = {
  name: 'replace_in_file',
  description: 'Replace text in a file with new content',
  schema: {
    name: 'replace_in_file',
    description: 'Replace text in a file with new content',
    parameters: {
      type: 'object',
      properties: {
        path: {
          type: 'string',
          description: 'Path to the file to modify'
        },
        oldText: {
          type: 'string',
          description: 'Text to replace (exact match required)'
        },
        newText: {
          type: 'string',
          description: 'New text to replace with'
        },
        backup: {
          type: 'boolean',
          description: 'Create a backup file before modification (default: true)'
        }
      },
      required: ['path', 'oldText', 'newText']
    }
  },

  async execute(args: Record<string, any>): Promise<ToolResult> {
    const { path, oldText, newText, backup = true } = args;
    const id = uuidv4();
    const startTime = Date.now();

    try {
      // Read the current file content
      const originalContent = await readFile(path, 'utf8');
      
      // Check if the old text exists
      if (!originalContent.includes(oldText)) {
        return {
          id,
          success: false,
          output: '',
          error: 'Text to replace not found in file',
          executionTime: Date.now() - startTime
        };
      }

      // Create backup if requested
      if (backup) {
        const backupPath = `${path}.backup.${Date.now()}`;
        await writeFile(backupPath, originalContent, 'utf8');
      }

      // Replace the text
      const newContent = originalContent.replace(oldText, newText);
      
      // Write the modified content
      await writeFile(path, newContent, 'utf8');
      
      return {
        id,
        success: true,
        output: `Text replaced successfully in ${path}`,
        executionTime: Date.now() - startTime
      };
    } catch (error: any) {
      return {
        id,
        success: false,
        output: '',
        error: `Failed to replace text in file: ${error.message}`,
        executionTime: Date.now() - startTime
      };
    }
  }
};

const deleteFileTool: Tool = {
  name: 'delete_file',
  description: 'Delete a file or directory',
  schema: {
    name: 'delete_file',
    description: 'Delete a file or directory',
    parameters: {
      type: 'object',
      properties: {
        path: {
          type: 'string',
          description: 'Path to the file or directory to delete'
        },
        recursive: {
          type: 'boolean',
          description: 'Delete directories recursively (default: false)'
        }
      },
      required: ['path']
    }
  },

  async execute(args: Record<string, any>): Promise<ToolResult> {
    const { path, recursive = false } = args;
    const id = uuidv4();
    const startTime = Date.now();

    try {
      const stats = await stat(path);
      
      if (stats.isDirectory()) {
        await rmdir(path, { recursive });
      } else {
        await unlink(path);
      }
      
      return {
        id,
        success: true,
        output: `Successfully deleted: ${path}`,
        executionTime: Date.now() - startTime
      };
    } catch (error: any) {
      return {
        id,
        success: false,
        output: '',
        error: `Failed to delete: ${error.message}`,
        executionTime: Date.now() - startTime
      };
    }
  }
};

// Helper function to list directory contents
async function listDirectory(
  path: string,
  recursive: boolean,
  showHidden: boolean,
  details: boolean
): Promise<DirectoryListing> {
  const items = await readdir(path);
  const files: FileInfo[] = [];
  const directories: FileInfo[] = [];

  for (const item of items) {
    if (!showHidden && item.startsWith('.')) {
      continue;
    }

    const itemPath = join(path, item);
    const stats = await stat(itemPath);
    
    const fileInfo: FileInfo = {
      path: itemPath,
      name: item,
      size: stats.size,
      isDirectory: stats.isDirectory(),
      lastModified: stats.mtime
    };

    if (stats.isDirectory()) {
      directories.push(fileInfo);
      
      if (recursive) {
        try {
          const subListing = await listDirectory(itemPath, recursive, showHidden, details);
          files.push(...subListing.files);
          directories.push(...subListing.directories);
        } catch (error) {
          // Skip directories we can't read
          console.warn(`Cannot read directory: ${itemPath}`);
        }
      }
    } else {
      files.push(fileInfo);
    }
  }

  return {
    path,
    files: files.sort((a, b) => a.name.localeCompare(b.name)),
    directories: directories.sort((a, b) => a.name.localeCompare(b.name))
  };
}

// Export all tools individually and as default
export { readFileTool, writeFileTool, listDirectoryTool, replaceInFileTool, deleteFileTool };

// Export all tools as default for tool registry
export default [readFileTool, writeFileTool, listDirectoryTool, replaceInFileTool, deleteFileTool];
