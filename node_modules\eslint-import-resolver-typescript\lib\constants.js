export const defaultConditionNames = [
    'types',
    'import',
    'esm2020',
    'es2020',
    'es2015',
    'require',
    'node',
    'node-addons',
    'browser',
    'default',
];
export const defaultExtensions = [
    '.ts',
    '.tsx',
    '.d.ts',
    '.js',
    '.jsx',
    '.json',
    '.node',
];
export const defaultExtensionAlias = {
    '.js': [
        '.ts',
        '.tsx',
        '.d.ts',
        '.js',
    ],
    '.ts': ['.ts', '.d.ts', '.js'],
    '.jsx': ['.tsx', '.d.ts', '.jsx'],
    '.tsx': [
        '.tsx',
        '.d.ts',
        '.jsx',
        '.js',
    ],
    '.cjs': ['.cts', '.d.cts', '.cjs'],
    '.cts': ['.cts', '.d.cts', '.cjs'],
    '.mjs': ['.mts', '.d.mts', '.mjs'],
    '.mts': ['.mts', '.d.mts', '.mjs'],
};
export const defaultMainFields = [
    'types',
    'typings',
    'fesm2020',
    'fesm2015',
    'esm2020',
    'es2020',
    'module',
    'jsnext:main',
    'main',
];
export const JS_EXT_PATTERN = /\.(?:[cm]js|jsx?)$/;
export const IMPORT_RESOLVER_NAME = 'eslint-import-resolver-typescript';
export const interfaceVersion = 2;
export const DEFAULT_TSCONFIG = 'tsconfig.json';
export const DEFAULT_JSCONFIG = 'jsconfig.json';
export const DEFAULT_CONFIGS = [DEFAULT_TSCONFIG, DEFAULT_JSCONFIG];
export const DEFAULT_TRY_PATHS = ['', ...DEFAULT_CONFIGS];
export const MATCH_ALL = '**';
export const DEFAULT_IGNORE = [MATCH_ALL, 'node_modules', MATCH_ALL].join('/');
export const TSCONFIG_NOT_FOUND_REGEXP = /^Tsconfig not found\b/;
//# sourceMappingURL=constants.js.map