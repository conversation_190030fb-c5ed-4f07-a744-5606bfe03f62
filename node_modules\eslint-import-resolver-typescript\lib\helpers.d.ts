export declare function mangleScopedPackage(moduleName: string): string;
export declare function removeQuerystring(id: string): string;
export declare const tryFile: (filename?: string[] | string, includeDir?: boolean, base?: string) => string;
export declare const sortProjectsByAffinity: (projects: string[], file: string) => string[];
export declare const toGlobPath: (pathname: string) => string;
export declare const toNativePath: (pathname: string) => string;
