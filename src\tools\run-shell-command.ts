import { exec, spawn, ChildProcess } from 'child_process';
import { promisify } from 'util';
import { v4 as uuidv4 } from 'uuid';
import type { Tool, ToolResult } from '../types';

const execAsync = promisify(exec);

// Store running processes
const runningProcesses = new Map<string, ChildProcess>();

const runShellCommandTool: Tool = {
  name: 'run_shell_command',
  description: 'Execute shell commands with support for different shells and process management',
  schema: {
    name: 'run_shell_command',
    description: 'Execute shell commands with support for different shells and process management',
    parameters: {
      type: 'object',
      properties: {
        command: {
          type: 'string',
          description: 'The shell command to execute'
        },
        shell: {
          type: 'string',
          description: 'Shell to use for execution',
          enum: ['cmd', 'powershell', 'bash', 'zsh', 'sh']
        },
        cwd: {
          type: 'string',
          description: 'Working directory for the command'
        },
        timeout: {
          type: 'number',
          description: 'Timeout in milliseconds (default: 30000)'
        },
        background: {
          type: 'boolean',
          description: 'Run command in background and return process ID'
        },
        env: {
          type: 'object',
          description: 'Environment variables to set'
        }
      },
      required: ['command']
    }
  },

  async execute(args: Record<string, any>): Promise<ToolResult> {
    const {
      command,
      shell = process.platform === 'win32' ? 'powershell' : 'bash',
      cwd = process.cwd(),
      timeout = 30000,
      background = false,
      env = {}
    } = args;

    const id = uuidv4();

    try {
      if (background) {
        return await executeInBackground(id, command, shell, cwd, env);
      } else {
        return await executeSync(id, command, shell, cwd, timeout, env);
      }
    } catch (error: any) {
      return {
        id,
        success: false,
        output: '',
        error: error.message || 'Command execution failed',
        executionTime: 0
      };
    }
  }
};

async function executeSync(
  id: string,
  command: string,
  shell: string,
  cwd: string,
  timeout: number,
  env: Record<string, string>
): Promise<ToolResult> {
  const startTime = Date.now();

  try {
    const execOptions = {
      cwd,
      timeout,
      env: { ...process.env, ...env },
      shell: getShellPath(shell),
      maxBuffer: 1024 * 1024 * 10 // 10MB buffer
    };

    const { stdout, stderr } = await execAsync(command, execOptions);
    
    return {
      id,
      success: true,
      output: stdout + (stderr ? `\nSTDERR:\n${stderr}` : ''),
      executionTime: Date.now() - startTime
    };
  } catch (error: any) {
    return {
      id,
      success: false,
      output: error.stdout || '',
      error: `Command failed: ${error.message}\n${error.stderr || ''}`,
      executionTime: Date.now() - startTime
    };
  }
}

async function executeInBackground(
  id: string,
  command: string,
  shell: string,
  cwd: string,
  env: Record<string, string>
): Promise<ToolResult> {
  const startTime = Date.now();

  try {
    const shellPath = getShellPath(shell);
    const shellArgs = getShellArgs(shell, command);
    
    const childProcess = spawn(shellPath, shellArgs, {
      cwd,
      env: { ...process.env, ...env },
      detached: true,
      stdio: ['ignore', 'pipe', 'pipe']
    });

    runningProcesses.set(id, childProcess);

    // Don't wait for the process to finish
    childProcess.unref();

    return {
      id,
      success: true,
      output: `Process started with ID: ${id}, PID: ${childProcess.pid}`,
      executionTime: Date.now() - startTime
    };
  } catch (error: any) {
    return {
      id,
      success: false,
      output: '',
      error: `Failed to start background process: ${error.message}`,
      executionTime: Date.now() - startTime
    };
  }
}

function getShellPath(shell: string): string {
  switch (shell.toLowerCase()) {
    case 'cmd':
      return 'cmd.exe';
    case 'powershell':
      return 'powershell.exe';
    case 'bash':
      return 'bash';
    case 'zsh':
      return 'zsh';
    case 'sh':
      return 'sh';
    default:
      return process.platform === 'win32' ? 'powershell.exe' : 'bash';
  }
}

function getShellArgs(shell: string, command: string): string[] {
  switch (shell.toLowerCase()) {
    case 'cmd':
      return ['/c', command];
    case 'powershell':
      return ['-Command', command];
    case 'bash':
    case 'zsh':
    case 'sh':
      return ['-c', command];
    default:
      return process.platform === 'win32' ? ['-Command', command] : ['-c', command];
  }
}

// Additional tool for process management
const killProcessTool: Tool = {
  name: 'kill_process',
  description: 'Kill a background process by its ID',
  schema: {
    name: 'kill_process',
    description: 'Kill a background process by its ID',
    parameters: {
      type: 'object',
      properties: {
        processId: {
          type: 'string',
          description: 'The process ID returned from run_shell_command with background=true'
        },
        signal: {
          type: 'string',
          description: 'Signal to send to the process',
          enum: ['SIGTERM', 'SIGKILL', 'SIGINT']
        }
      },
      required: ['processId']
    }
  },

  async execute(args: Record<string, any>): Promise<ToolResult> {
    const { processId, signal = 'SIGTERM' } = args;
    const id = uuidv4();
    const startTime = Date.now();

    const process = runningProcesses.get(processId);
    
    if (!process) {
      return {
        id,
        success: false,
        output: '',
        error: `Process with ID ${processId} not found`,
        executionTime: Date.now() - startTime
      };
    }

    try {
      process.kill(signal);
      runningProcesses.delete(processId);
      
      return {
        id,
        success: true,
        output: `Process ${processId} killed with signal ${signal}`,
        executionTime: Date.now() - startTime
      };
    } catch (error: any) {
      return {
        id,
        success: false,
        output: '',
        error: `Failed to kill process: ${error.message}`,
        executionTime: Date.now() - startTime
      };
    }
  }
};

const listProcessesTool: Tool = {
  name: 'list_processes',
  description: 'List all running background processes',
  schema: {
    name: 'list_processes',
    description: 'List all running background processes',
    parameters: {
      type: 'object',
      properties: {},
      required: []
    }
  },

  async execute(): Promise<ToolResult> {
    const id = uuidv4();
    const startTime = Date.now();

    const processes = Array.from(runningProcesses.entries()).map(([processId, process]) => ({
      id: processId,
      pid: process.pid,
      killed: process.killed,
      connected: process.connected
    }));

    return {
      id,
      success: true,
      output: JSON.stringify(processes, null, 2),
      executionTime: Date.now() - startTime
    };
  }
};

// Export all tools individually and as default
export { runShellCommandTool, killProcessTool, listProcessesTool };

// Export all tools as default for tool registry
export default [runShellCommandTool, killProcessTool, listProcessesTool];
