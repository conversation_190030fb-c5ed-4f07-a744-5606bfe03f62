{"version": 3, "sources": ["../../react-hotkeys-hook/packages/react-hotkeys-hook/dist/index.js"], "sourcesContent": ["import { createContext as W, useContext as z, useState as A, use<PERSON>allback as v, useRef as H, useLayoutEffect as _, useEffect as m } from \"react\";\nimport { jsx as q } from \"react/jsx-runtime\";\nconst G = [\"shift\", \"alt\", \"meta\", \"mod\", \"ctrl\", \"control\"], O = {\n  esc: \"escape\",\n  return: \"enter\",\n  left: \"arrowleft\",\n  right: \"arrowright\",\n  up: \"arrowup\",\n  down: \"arrowdown\",\n  ShiftLeft: \"shift\",\n  ShiftRight: \"shift\",\n  AltLeft: \"alt\",\n  AltRight: \"alt\",\n  MetaLeft: \"meta\",\n  MetaRight: \"meta\",\n  OSLeft: \"meta\",\n  OSRight: \"meta\",\n  ControlLeft: \"ctrl\",\n  ControlRight: \"ctrl\"\n};\nfunction S(t) {\n  return (O[t.trim()] || t.trim()).toLowerCase().replace(/key|digit|numpad/, \"\");\n}\nfunction J(t) {\n  return G.includes(t);\n}\nfunction b(t, r = \",\") {\n  return t.toLowerCase().split(r);\n}\nfunction R(t, r = \"+\", n = \">\", f = !1, l) {\n  let u = [], c = !1;\n  t.includes(n) ? (c = !0, u = t.toLocaleLowerCase().split(n).map((i) => S(i))) : u = t.toLocaleLowerCase().split(r).map((i) => S(i));\n  const d = {\n    alt: u.includes(\"alt\"),\n    ctrl: u.includes(\"ctrl\") || u.includes(\"control\"),\n    shift: u.includes(\"shift\"),\n    meta: u.includes(\"meta\"),\n    mod: u.includes(\"mod\"),\n    useKey: f\n  }, a = u.filter((i) => !G.includes(i));\n  return {\n    ...d,\n    keys: a,\n    description: l,\n    isSequence: c\n  };\n}\ntypeof document < \"u\" && (document.addEventListener(\"keydown\", (t) => {\n  t.code !== void 0 && Q([S(t.code)]);\n}), document.addEventListener(\"keyup\", (t) => {\n  t.code !== void 0 && U([S(t.code)]);\n})), typeof window < \"u\" && (window.addEventListener(\"blur\", () => {\n  E.clear();\n}), window.addEventListener(\"contextmenu\", () => {\n  setTimeout(() => {\n    E.clear();\n  }, 0);\n}));\nconst E = /* @__PURE__ */ new Set();\nfunction B(t) {\n  return Array.isArray(t);\n}\nfunction ee(t, r = \",\") {\n  return (B(t) ? t : t.split(r)).every((f) => E.has(f.trim().toLowerCase()));\n}\nfunction Q(t) {\n  const r = Array.isArray(t) ? t : [t];\n  E.has(\"meta\") && E.forEach((n) => !J(n) && E.delete(n.toLowerCase())), r.forEach((n) => E.add(n.toLowerCase()));\n}\nfunction U(t) {\n  const r = Array.isArray(t) ? t : [t];\n  t === \"meta\" ? E.clear() : r.forEach((n) => E.delete(n.toLowerCase()));\n}\nfunction te(t, r, n) {\n  (typeof n == \"function\" && n(t, r) || n === !0) && t.preventDefault();\n}\nfunction re(t, r, n) {\n  return typeof n == \"function\" ? n(t, r) : n === !0 || n === void 0;\n}\nfunction ne(t) {\n  return V(t, [\"input\", \"textarea\", \"select\"]);\n}\nfunction V(t, r = !1) {\n  const { target: n, composed: f } = t;\n  let l;\n  return ce(n) && f ? l = t.composedPath()[0] && t.composedPath()[0].tagName : l = n && n.tagName, B(r) ? !!(l && r && r.some((u) => u.toLowerCase() === l.toLowerCase())) : !!(l && r && r);\n}\nfunction ce(t) {\n  return !!t.tagName && !t.tagName.startsWith(\"-\") && t.tagName.includes(\"-\");\n}\nfunction ue(t, r) {\n  return t.length === 0 && r ? (console.warn(\n    'A hotkey has the \"scopes\" option set, however no active scopes were found. If you want to use the global scopes feature, you need to wrap your app in a <HotkeysProvider>'\n  ), !0) : r ? t.some((n) => r.includes(n)) || t.includes(\"*\") : !0;\n}\nconst oe = (t, r, n = !1) => {\n  const { alt: f, meta: l, mod: u, shift: c, ctrl: d, keys: a, useKey: i } = r, { code: w, key: e, ctrlKey: s, metaKey: y, shiftKey: k, altKey: K } = t, h = S(w);\n  if (i && (a == null ? void 0 : a.length) === 1 && a.includes(e))\n    return !0;\n  if (!(a != null && a.includes(h)) && ![\"ctrl\", \"control\", \"unknown\", \"meta\", \"alt\", \"shift\", \"os\"].includes(h))\n    return !1;\n  if (!n) {\n    if (f !== K && h !== \"alt\" || c !== k && h !== \"shift\")\n      return !1;\n    if (u) {\n      if (!y && !s)\n        return !1;\n    } else if (l !== y && h !== \"meta\" && h !== \"os\" || d !== s && h !== \"ctrl\" && h !== \"control\")\n      return !1;\n  }\n  return a && a.length === 1 && a.includes(h) ? !0 : a ? ee(a) : !a;\n}, X = W(void 0), ae = () => z(X);\nfunction fe({ addHotkey: t, removeHotkey: r, children: n }) {\n  return /* @__PURE__ */ q(X.Provider, { value: { addHotkey: t, removeHotkey: r }, children: n });\n}\nfunction N(t, r) {\n  return t && r && typeof t == \"object\" && typeof r == \"object\" ? Object.keys(t).length === Object.keys(r).length && // @ts-expect-error TS7053\n  Object.keys(t).reduce((n, f) => n && N(t[f], r[f]), !0) : t === r;\n}\nconst Y = W({\n  hotkeys: [],\n  activeScopes: [],\n  // This array has to be empty instead of containing '*' as default, to check if the provider is set or not\n  toggleScope: () => {\n  },\n  enableScope: () => {\n  },\n  disableScope: () => {\n  }\n}), le = () => z(Y), he = ({ initiallyActiveScopes: t = [\"*\"], children: r }) => {\n  const [n, f] = A(t), [l, u] = A([]), c = v((e) => {\n    f((s) => s.includes(\"*\") ? [e] : Array.from(/* @__PURE__ */ new Set([...s, e])));\n  }, []), d = v((e) => {\n    f((s) => s.filter((y) => y !== e));\n  }, []), a = v((e) => {\n    f((s) => s.includes(e) ? s.filter((y) => y !== e) : s.includes(\"*\") ? [e] : Array.from(/* @__PURE__ */ new Set([...s, e])));\n  }, []), i = v((e) => {\n    u((s) => [...s, e]);\n  }, []), w = v((e) => {\n    u((s) => s.filter((y) => !N(y, e)));\n  }, []);\n  return /* @__PURE__ */ q(\n    Y.Provider,\n    {\n      value: { activeScopes: n, hotkeys: l, enableScope: c, disableScope: d, toggleScope: a },\n      children: /* @__PURE__ */ q(fe, { addHotkey: i, removeHotkey: w, children: r })\n    }\n  );\n};\nfunction se(t) {\n  const r = H(void 0);\n  return N(r.current, t) || (r.current = t), r.current;\n}\nconst F = (t) => {\n  t.stopPropagation(), t.preventDefault(), t.stopImmediatePropagation();\n}, ie = typeof window < \"u\" ? _ : m;\nfunction we(t, r, n, f) {\n  const l = H(null), u = H(!1), c = n instanceof Array ? f instanceof Array ? void 0 : f : n, d = B(t) ? t.join(c == null ? void 0 : c.delimiter) : t, a = n instanceof Array ? n : f instanceof Array ? f : void 0, i = v(r, a ?? []), w = H(i);\n  a ? w.current = i : w.current = r;\n  const e = se(c), { activeScopes: s } = le(), y = ae();\n  return ie(() => {\n    if ((e == null ? void 0 : e.enabled) === !1 || !ue(s, e == null ? void 0 : e.scopes))\n      return;\n    let k = [], K;\n    const h = (o, M = !1) => {\n      var j;\n      if (!(ne(o) && !V(o, e == null ? void 0 : e.enableOnFormTags))) {\n        if (l.current !== null) {\n          const L = l.current.getRootNode();\n          if ((L instanceof Document || L instanceof ShadowRoot) && L.activeElement !== l.current && !l.current.contains(L.activeElement)) {\n            F(o);\n            return;\n          }\n        }\n        (j = o.target) != null && j.isContentEditable && !(e != null && e.enableOnContentEditable) || b(d, e == null ? void 0 : e.delimiter).forEach((L) => {\n          var D, I, p, $;\n          if (L.includes((e == null ? void 0 : e.splitKey) ?? \"+\") && L.includes((e == null ? void 0 : e.sequenceSplitKey) ?? \">\")) {\n            console.warn(`Hotkey ${L} contains both ${(e == null ? void 0 : e.splitKey) ?? \"+\"} and ${(e == null ? void 0 : e.sequenceSplitKey) ?? \">\"} which is not supported.`);\n            return;\n          }\n          const g = R(L, e == null ? void 0 : e.splitKey, e == null ? void 0 : e.sequenceSplitKey, e == null ? void 0 : e.useKey, e == null ? void 0 : e.description);\n          if (g.isSequence) {\n            K = setTimeout(() => {\n              k = [];\n            }, (e == null ? void 0 : e.sequenceTimeoutMs) ?? 1e3);\n            const P = g.useKey ? o.key : S(o.code);\n            if (J(P.toLowerCase()))\n              return;\n            k.push(P);\n            const Z = (D = g.keys) == null ? void 0 : D[k.length - 1];\n            if (P !== Z) {\n              k = [], K && clearTimeout(K);\n              return;\n            }\n            k.length === ((I = g.keys) == null ? void 0 : I.length) && (w.current(o, g), K && clearTimeout(K), k = []);\n          } else if (oe(o, g, e == null ? void 0 : e.ignoreModifiers) || (p = g.keys) != null && p.includes(\"*\")) {\n            if (($ = e == null ? void 0 : e.ignoreEventWhen) != null && $.call(e, o) || M && u.current)\n              return;\n            if (te(o, g, e == null ? void 0 : e.preventDefault), !re(o, g, e == null ? void 0 : e.enabled)) {\n              F(o);\n              return;\n            }\n            w.current(o, g), M || (u.current = !0);\n          }\n        });\n      }\n    }, T = (o) => {\n      o.code !== void 0 && (Q(S(o.code)), ((e == null ? void 0 : e.keydown) === void 0 && (e == null ? void 0 : e.keyup) !== !0 || e != null && e.keydown) && h(o));\n    }, x = (o) => {\n      o.code !== void 0 && (U(S(o.code)), u.current = !1, e != null && e.keyup && h(o, !0));\n    }, C = l.current || (c == null ? void 0 : c.document) || document;\n    return C.addEventListener(\"keyup\", x, c == null ? void 0 : c.eventListenerOptions), C.addEventListener(\"keydown\", T, c == null ? void 0 : c.eventListenerOptions), y && b(d, e == null ? void 0 : e.delimiter).forEach(\n      (o) => y.addHotkey(\n        R(o, e == null ? void 0 : e.splitKey, e == null ? void 0 : e.sequenceSplitKey, e == null ? void 0 : e.useKey, e == null ? void 0 : e.description)\n      )\n    ), () => {\n      C.removeEventListener(\"keyup\", x, c == null ? void 0 : c.eventListenerOptions), C.removeEventListener(\"keydown\", T, c == null ? void 0 : c.eventListenerOptions), y && b(d, e == null ? void 0 : e.delimiter).forEach(\n        (o) => y.removeHotkey(\n          R(o, e == null ? void 0 : e.splitKey, e == null ? void 0 : e.sequenceSplitKey, e == null ? void 0 : e.useKey, e == null ? void 0 : e.description)\n        )\n      ), k = [], K && clearTimeout(K);\n    };\n  }, [d, e, s]), l;\n}\nfunction ge(t = !1) {\n  const [r, n] = A(/* @__PURE__ */ new Set()), [f, l] = A(!1), u = v((i) => {\n    i.code !== void 0 && (i.preventDefault(), i.stopPropagation(), n((w) => {\n      const e = new Set(w);\n      return e.add(S(t ? i.key : i.code)), e;\n    }));\n  }, [t]), c = v(() => {\n    typeof document < \"u\" && (document.removeEventListener(\"keydown\", u), l(!1));\n  }, [u]), d = v(() => {\n    n(/* @__PURE__ */ new Set()), typeof document < \"u\" && (c(), document.addEventListener(\"keydown\", u), l(!0));\n  }, [u, c]), a = v(() => {\n    n(/* @__PURE__ */ new Set());\n  }, []);\n  return [r, { start: d, stop: c, resetKeys: a, isRecording: f }];\n}\nexport {\n  he as HotkeysProvider,\n  ee as isHotkeyPressed,\n  we as useHotkeys,\n  le as useHotkeysContext,\n  ge as useRecordHotkeys\n};\n"], "mappings": ";;;;;;;;;;;AAAA,mBAAwI;AACxI,yBAAyB;AACzB,IAAM,IAAI,CAAC,SAAS,OAAO,QAAQ,OAAO,QAAQ,SAAS;AAA3D,IAA8D,IAAI;AAAA,EAChE,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,MAAM;AAAA,EACN,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,aAAa;AAAA,EACb,cAAc;AAChB;AACA,SAAS,EAAE,GAAG;AACZ,UAAQ,EAAE,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,GAAG,YAAY,EAAE,QAAQ,oBAAoB,EAAE;AAC/E;AACA,SAAS,EAAE,GAAG;AACZ,SAAO,EAAE,SAAS,CAAC;AACrB;AACA,SAAS,EAAE,GAAG,IAAI,KAAK;AACrB,SAAO,EAAE,YAAY,EAAE,MAAM,CAAC;AAChC;AACA,SAAS,EAAE,GAAG,IAAI,KAAK,IAAI,KAAK,IAAI,OAAI,GAAG;AACzC,MAAI,IAAI,CAAC,GAAG,IAAI;AAChB,IAAE,SAAS,CAAC,KAAK,IAAI,MAAI,IAAI,EAAE,kBAAkB,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,KAAK,IAAI,EAAE,kBAAkB,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;AAClI,QAAM,IAAI;AAAA,IACR,KAAK,EAAE,SAAS,KAAK;AAAA,IACrB,MAAM,EAAE,SAAS,MAAM,KAAK,EAAE,SAAS,SAAS;AAAA,IAChD,OAAO,EAAE,SAAS,OAAO;AAAA,IACzB,MAAM,EAAE,SAAS,MAAM;AAAA,IACvB,KAAK,EAAE,SAAS,KAAK;AAAA,IACrB,QAAQ;AAAA,EACV,GAAG,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,SAAS,CAAC,CAAC;AACrC,SAAO;AAAA,IACL,GAAG;AAAA,IACH,MAAM;AAAA,IACN,aAAa;AAAA,IACb,YAAY;AAAA,EACd;AACF;AACA,OAAO,WAAW,QAAQ,SAAS,iBAAiB,WAAW,CAAC,MAAM;AACpE,IAAE,SAAS,UAAU,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;AACpC,CAAC,GAAG,SAAS,iBAAiB,SAAS,CAAC,MAAM;AAC5C,IAAE,SAAS,UAAU,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;AACpC,CAAC,IAAI,OAAO,SAAS,QAAQ,OAAO,iBAAiB,QAAQ,MAAM;AACjE,IAAE,MAAM;AACV,CAAC,GAAG,OAAO,iBAAiB,eAAe,MAAM;AAC/C,aAAW,MAAM;AACf,MAAE,MAAM;AAAA,EACV,GAAG,CAAC;AACN,CAAC;AACD,IAAM,IAAoB,oBAAI,IAAI;AAClC,SAAS,EAAE,GAAG;AACZ,SAAO,MAAM,QAAQ,CAAC;AACxB;AACA,SAAS,GAAG,GAAG,IAAI,KAAK;AACtB,UAAQ,EAAE,CAAC,IAAI,IAAI,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;AAC3E;AACA,SAAS,EAAE,GAAG;AACZ,QAAM,IAAI,MAAM,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC;AACnC,IAAE,IAAI,MAAM,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC,GAAG,EAAE,QAAQ,CAAC,MAAM,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;AAChH;AACA,SAAS,EAAE,GAAG;AACZ,QAAM,IAAI,MAAM,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC;AACnC,QAAM,SAAS,EAAE,MAAM,IAAI,EAAE,QAAQ,CAAC,MAAM,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;AACvE;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,GAAC,OAAO,KAAK,cAAc,EAAE,GAAG,CAAC,KAAK,MAAM,SAAO,EAAE,eAAe;AACtE;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,SAAO,OAAO,KAAK,aAAa,EAAE,GAAG,CAAC,IAAI,MAAM,QAAM,MAAM;AAC9D;AACA,SAAS,GAAG,GAAG;AACb,SAAO,EAAE,GAAG,CAAC,SAAS,YAAY,QAAQ,CAAC;AAC7C;AACA,SAAS,EAAE,GAAG,IAAI,OAAI;AACpB,QAAM,EAAE,QAAQ,GAAG,UAAU,EAAE,IAAI;AACnC,MAAI;AACJ,SAAO,GAAG,CAAC,KAAK,IAAI,IAAI,EAAE,aAAa,EAAE,CAAC,KAAK,EAAE,aAAa,EAAE,CAAC,EAAE,UAAU,IAAI,KAAK,EAAE,SAAS,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,KAAK,EAAE,KAAK,CAAC,MAAM,EAAE,YAAY,MAAM,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE,KAAK,KAAK;AAC1L;AACA,SAAS,GAAG,GAAG;AACb,SAAO,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,QAAQ,WAAW,GAAG,KAAK,EAAE,QAAQ,SAAS,GAAG;AAC5E;AACA,SAAS,GAAG,GAAG,GAAG;AAChB,SAAO,EAAE,WAAW,KAAK,KAAK,QAAQ;AAAA,IACpC;AAAA,EACF,GAAG,QAAM,IAAI,EAAE,KAAK,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,KAAK,EAAE,SAAS,GAAG,IAAI;AACjE;AACA,IAAM,KAAK,CAAC,GAAG,GAAG,IAAI,UAAO;AAC3B,QAAM,EAAE,KAAK,GAAG,MAAM,GAAG,KAAK,GAAG,OAAO,GAAG,MAAM,GAAG,MAAM,GAAG,QAAQ,EAAE,IAAI,GAAG,EAAE,MAAM,GAAG,KAAK,GAAG,SAAS,GAAG,SAAS,GAAG,UAAU,GAAG,QAAQ,EAAE,IAAI,GAAG,IAAI,EAAE,CAAC;AAC9J,MAAI,MAAM,KAAK,OAAO,SAAS,EAAE,YAAY,KAAK,EAAE,SAAS,CAAC;AAC5D,WAAO;AACT,MAAI,EAAE,KAAK,QAAQ,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,QAAQ,WAAW,WAAW,QAAQ,OAAO,SAAS,IAAI,EAAE,SAAS,CAAC;AAC3G,WAAO;AACT,MAAI,CAAC,GAAG;AACN,QAAI,MAAM,KAAK,MAAM,SAAS,MAAM,KAAK,MAAM;AAC7C,aAAO;AACT,QAAI,GAAG;AACL,UAAI,CAAC,KAAK,CAAC;AACT,eAAO;AAAA,IACX,WAAW,MAAM,KAAK,MAAM,UAAU,MAAM,QAAQ,MAAM,KAAK,MAAM,UAAU,MAAM;AACnF,aAAO;AAAA,EACX;AACA,SAAO,KAAK,EAAE,WAAW,KAAK,EAAE,SAAS,CAAC,IAAI,OAAK,IAAI,GAAG,CAAC,IAAI,CAAC;AAClE;AAhBA,IAgBG,QAAI,aAAAA,eAAE,MAAM;AAhBf,IAgBkB,KAAK,UAAM,aAAAC,YAAE,CAAC;AAChC,SAAS,GAAG,EAAE,WAAW,GAAG,cAAc,GAAG,UAAU,EAAE,GAAG;AAC1D,aAAuB,mBAAAC,KAAE,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,GAAG,cAAc,EAAE,GAAG,UAAU,EAAE,CAAC;AAChG;AACA,SAAS,EAAE,GAAG,GAAG;AACf,SAAO,KAAK,KAAK,OAAO,KAAK,YAAY,OAAO,KAAK,WAAW,OAAO,KAAK,CAAC,EAAE,WAAW,OAAO,KAAK,CAAC,EAAE;AAAA,EACzG,OAAO,KAAK,CAAC,EAAE,OAAO,CAAC,GAAG,MAAM,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,IAAE,IAAI,MAAM;AAClE;AACA,IAAM,QAAI,aAAAF,eAAE;AAAA,EACV,SAAS,CAAC;AAAA,EACV,cAAc,CAAC;AAAA;AAAA,EAEf,aAAa,MAAM;AAAA,EACnB;AAAA,EACA,aAAa,MAAM;AAAA,EACnB;AAAA,EACA,cAAc,MAAM;AAAA,EACpB;AACF,CAAC;AAVD,IAUI,KAAK,UAAM,aAAAC,YAAE,CAAC;AAVlB,IAUqB,KAAK,CAAC,EAAE,uBAAuB,IAAI,CAAC,GAAG,GAAG,UAAU,EAAE,MAAM;AAC/E,QAAM,CAAC,GAAG,CAAC,QAAI,aAAAE,UAAE,CAAC,GAAG,CAAC,GAAG,CAAC,QAAI,aAAAA,UAAE,CAAC,CAAC,GAAG,QAAI,aAAAC,aAAE,CAAC,MAAM;AAChD,MAAE,CAAC,MAAM,EAAE,SAAS,GAAG,IAAI,CAAC,CAAC,IAAI,MAAM,KAAqB,oBAAI,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAAA,EACjF,GAAG,CAAC,CAAC,GAAG,QAAI,aAAAA,aAAE,CAAC,MAAM;AACnB,MAAE,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,MAAM,CAAC,CAAC;AAAA,EACnC,GAAG,CAAC,CAAC,GAAG,QAAI,aAAAA,aAAE,CAAC,MAAM;AACnB,MAAE,CAAC,MAAM,EAAE,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,MAAM,CAAC,IAAI,EAAE,SAAS,GAAG,IAAI,CAAC,CAAC,IAAI,MAAM,KAAqB,oBAAI,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAAA,EAC5H,GAAG,CAAC,CAAC,GAAG,QAAI,aAAAA,aAAE,CAAC,MAAM;AACnB,MAAE,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,EACpB,GAAG,CAAC,CAAC,GAAG,QAAI,aAAAA,aAAE,CAAC,MAAM;AACnB,MAAE,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAAA,EACpC,GAAG,CAAC,CAAC;AACL,aAAuB,mBAAAF;AAAA,IACrB,EAAE;AAAA,IACF;AAAA,MACE,OAAO,EAAE,cAAc,GAAG,SAAS,GAAG,aAAa,GAAG,cAAc,GAAG,aAAa,EAAE;AAAA,MACtF,cAA0B,mBAAAA,KAAE,IAAI,EAAE,WAAW,GAAG,cAAc,GAAG,UAAU,EAAE,CAAC;AAAA,IAChF;AAAA,EACF;AACF;AACA,SAAS,GAAG,GAAG;AACb,QAAM,QAAI,aAAAG,QAAE,MAAM;AAClB,SAAO,EAAE,EAAE,SAAS,CAAC,MAAM,EAAE,UAAU,IAAI,EAAE;AAC/C;AACA,IAAM,IAAI,CAAC,MAAM;AACf,IAAE,gBAAgB,GAAG,EAAE,eAAe,GAAG,EAAE,yBAAyB;AACtE;AAFA,IAEG,KAAK,OAAO,SAAS,MAAM,aAAAC,kBAAI,aAAAC;AAClC,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG;AACtB,QAAM,QAAI,aAAAF,QAAE,IAAI,GAAG,QAAI,aAAAA,QAAE,KAAE,GAAG,IAAI,aAAa,QAAQ,aAAa,QAAQ,SAAS,IAAI,GAAG,IAAI,EAAE,CAAC,IAAI,EAAE,KAAK,KAAK,OAAO,SAAS,EAAE,SAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,IAAI,aAAa,QAAQ,IAAI,QAAQ,QAAI,aAAAD,aAAE,GAAG,KAAK,CAAC,CAAC,GAAG,QAAI,aAAAC,QAAE,CAAC;AAC7O,MAAI,EAAE,UAAU,IAAI,EAAE,UAAU;AAChC,QAAM,IAAI,GAAG,CAAC,GAAG,EAAE,cAAc,EAAE,IAAI,GAAG,GAAG,IAAI,GAAG;AACpD,SAAO,GAAG,MAAM;AACd,SAAK,KAAK,OAAO,SAAS,EAAE,aAAa,SAAM,CAAC,GAAG,GAAG,KAAK,OAAO,SAAS,EAAE,MAAM;AACjF;AACF,QAAI,IAAI,CAAC,GAAG;AACZ,UAAM,IAAI,CAAC,GAAG,IAAI,UAAO;AACvB,UAAI;AACJ,UAAI,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG,KAAK,OAAO,SAAS,EAAE,gBAAgB,IAAI;AAC9D,YAAI,EAAE,YAAY,MAAM;AACtB,gBAAM,IAAI,EAAE,QAAQ,YAAY;AAChC,eAAK,aAAa,YAAY,aAAa,eAAe,EAAE,kBAAkB,EAAE,WAAW,CAAC,EAAE,QAAQ,SAAS,EAAE,aAAa,GAAG;AAC/H,cAAE,CAAC;AACH;AAAA,UACF;AAAA,QACF;AACA,SAAC,IAAI,EAAE,WAAW,QAAQ,EAAE,qBAAqB,EAAE,KAAK,QAAQ,EAAE,4BAA4B,EAAE,GAAG,KAAK,OAAO,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC,MAAM;AAClJ,cAAI,GAAG,GAAG,GAAG;AACb,cAAI,EAAE,UAAU,KAAK,OAAO,SAAS,EAAE,aAAa,GAAG,KAAK,EAAE,UAAU,KAAK,OAAO,SAAS,EAAE,qBAAqB,GAAG,GAAG;AACxH,oBAAQ,KAAK,UAAU,CAAC,mBAAmB,KAAK,OAAO,SAAS,EAAE,aAAa,GAAG,SAAS,KAAK,OAAO,SAAS,EAAE,qBAAqB,GAAG,0BAA0B;AACpK;AAAA,UACF;AACA,gBAAM,IAAI,EAAE,GAAG,KAAK,OAAO,SAAS,EAAE,UAAU,KAAK,OAAO,SAAS,EAAE,kBAAkB,KAAK,OAAO,SAAS,EAAE,QAAQ,KAAK,OAAO,SAAS,EAAE,WAAW;AAC1J,cAAI,EAAE,YAAY;AAChB,gBAAI,WAAW,MAAM;AACnB,kBAAI,CAAC;AAAA,YACP,IAAI,KAAK,OAAO,SAAS,EAAE,sBAAsB,GAAG;AACpD,kBAAM,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,EAAE,IAAI;AACrC,gBAAI,EAAE,EAAE,YAAY,CAAC;AACnB;AACF,cAAE,KAAK,CAAC;AACR,kBAAM,KAAK,IAAI,EAAE,SAAS,OAAO,SAAS,EAAE,EAAE,SAAS,CAAC;AACxD,gBAAI,MAAM,GAAG;AACX,kBAAI,CAAC,GAAG,KAAK,aAAa,CAAC;AAC3B;AAAA,YACF;AACA,cAAE,aAAa,IAAI,EAAE,SAAS,OAAO,SAAS,EAAE,YAAY,EAAE,QAAQ,GAAG,CAAC,GAAG,KAAK,aAAa,CAAC,GAAG,IAAI,CAAC;AAAA,UAC1G,WAAW,GAAG,GAAG,GAAG,KAAK,OAAO,SAAS,EAAE,eAAe,MAAM,IAAI,EAAE,SAAS,QAAQ,EAAE,SAAS,GAAG,GAAG;AACtG,iBAAK,IAAI,KAAK,OAAO,SAAS,EAAE,oBAAoB,QAAQ,EAAE,KAAK,GAAG,CAAC,KAAK,KAAK,EAAE;AACjF;AACF,gBAAI,GAAG,GAAG,GAAG,KAAK,OAAO,SAAS,EAAE,cAAc,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,OAAO,SAAS,EAAE,OAAO,GAAG;AAC9F,gBAAE,CAAC;AACH;AAAA,YACF;AACA,cAAE,QAAQ,GAAG,CAAC,GAAG,MAAM,EAAE,UAAU;AAAA,UACrC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,GAAG,IAAI,CAAC,MAAM;AACZ,QAAE,SAAS,WAAW,EAAE,EAAE,EAAE,IAAI,CAAC,KAAK,KAAK,OAAO,SAAS,EAAE,aAAa,WAAW,KAAK,OAAO,SAAS,EAAE,WAAW,QAAM,KAAK,QAAQ,EAAE,YAAY,EAAE,CAAC;AAAA,IAC7J,GAAG,IAAI,CAAC,MAAM;AACZ,QAAE,SAAS,WAAW,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,UAAU,OAAI,KAAK,QAAQ,EAAE,SAAS,EAAE,GAAG,IAAE;AAAA,IACrF,GAAG,IAAI,EAAE,YAAY,KAAK,OAAO,SAAS,EAAE,aAAa;AACzD,WAAO,EAAE,iBAAiB,SAAS,GAAG,KAAK,OAAO,SAAS,EAAE,oBAAoB,GAAG,EAAE,iBAAiB,WAAW,GAAG,KAAK,OAAO,SAAS,EAAE,oBAAoB,GAAG,KAAK,EAAE,GAAG,KAAK,OAAO,SAAS,EAAE,SAAS,EAAE;AAAA,MAC7M,CAAC,MAAM,EAAE;AAAA,QACP,EAAE,GAAG,KAAK,OAAO,SAAS,EAAE,UAAU,KAAK,OAAO,SAAS,EAAE,kBAAkB,KAAK,OAAO,SAAS,EAAE,QAAQ,KAAK,OAAO,SAAS,EAAE,WAAW;AAAA,MAClJ;AAAA,IACF,GAAG,MAAM;AACP,QAAE,oBAAoB,SAAS,GAAG,KAAK,OAAO,SAAS,EAAE,oBAAoB,GAAG,EAAE,oBAAoB,WAAW,GAAG,KAAK,OAAO,SAAS,EAAE,oBAAoB,GAAG,KAAK,EAAE,GAAG,KAAK,OAAO,SAAS,EAAE,SAAS,EAAE;AAAA,QAC5M,CAAC,MAAM,EAAE;AAAA,UACP,EAAE,GAAG,KAAK,OAAO,SAAS,EAAE,UAAU,KAAK,OAAO,SAAS,EAAE,kBAAkB,KAAK,OAAO,SAAS,EAAE,QAAQ,KAAK,OAAO,SAAS,EAAE,WAAW;AAAA,QAClJ;AAAA,MACF,GAAG,IAAI,CAAC,GAAG,KAAK,aAAa,CAAC;AAAA,IAChC;AAAA,EACF,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG;AACjB;AACA,SAAS,GAAG,IAAI,OAAI;AAClB,QAAM,CAAC,GAAG,CAAC,QAAI,aAAAF,UAAkB,oBAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,QAAI,aAAAA,UAAE,KAAE,GAAG,QAAI,aAAAC,aAAE,CAAC,MAAM;AACxE,MAAE,SAAS,WAAW,EAAE,eAAe,GAAG,EAAE,gBAAgB,GAAG,EAAE,CAAC,MAAM;AACtE,YAAM,IAAI,IAAI,IAAI,CAAC;AACnB,aAAO,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,GAAG;AAAA,IACvC,CAAC;AAAA,EACH,GAAG,CAAC,CAAC,CAAC,GAAG,QAAI,aAAAA,aAAE,MAAM;AACnB,WAAO,WAAW,QAAQ,SAAS,oBAAoB,WAAW,CAAC,GAAG,EAAE,KAAE;AAAA,EAC5E,GAAG,CAAC,CAAC,CAAC,GAAG,QAAI,aAAAA,aAAE,MAAM;AACnB,MAAkB,oBAAI,IAAI,CAAC,GAAG,OAAO,WAAW,QAAQ,EAAE,GAAG,SAAS,iBAAiB,WAAW,CAAC,GAAG,EAAE,IAAE;AAAA,EAC5G,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,QAAI,aAAAA,aAAE,MAAM;AACtB,MAAkB,oBAAI,IAAI,CAAC;AAAA,EAC7B,GAAG,CAAC,CAAC;AACL,SAAO,CAAC,GAAG,EAAE,OAAO,GAAG,MAAM,GAAG,WAAW,GAAG,aAAa,EAAE,CAAC;AAChE;", "names": ["W", "z", "q", "A", "v", "H", "_", "m"]}