{"name": "eslint-import-context", "version": "0.1.9", "type": "commonjs", "description": "Provide context info for eslint-plugin-import-x, so no extra arguments need to be added.", "repository": "git+https://github.com/un-ts/eslint-import-context.git", "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://www.1stG.me)", "funding": "https://opencollective.com/eslint-import-context", "license": "MIT", "engines": {"node": "^12.20.0 || ^14.18.0 || >=16.0.0"}, "main": "./lib/index.js", "types": "./lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "files": ["lib", "!**/*.tsbuildinfo"], "peerDependencies": {"unrs-resolver": "^1.0.0"}, "peerDependenciesMeta": {"unrs-resolver": {"optional": true}}, "dependencies": {"get-tsconfig": "^4.10.1", "stable-hash-x": "^0.2.0"}}