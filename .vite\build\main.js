"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
const require$$3$1 = require("electron");
const path$1 = require("node:path");
const require$$0$1 = require("path");
const require$$1$1 = require("child_process");
const require$$0 = require("tty");
const require$$1 = require("util");
const require$$3 = require("fs");
const require$$4 = require("net");
const promises = require("fs/promises");
const node_crypto = require("node:crypto");
const Database = require("better-sqlite3");
function getDefaultExportFromCjs(x) {
  return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, "default") ? x["default"] : x;
}
var src = { exports: {} };
var browser = { exports: {} };
var debug$1 = { exports: {} };
var ms;
var hasRequiredMs;
function requireMs() {
  if (hasRequiredMs) return ms;
  hasRequiredMs = 1;
  var s = 1e3;
  var m = s * 60;
  var h = m * 60;
  var d = h * 24;
  var y = d * 365.25;
  ms = function(val, options) {
    options = options || {};
    var type = typeof val;
    if (type === "string" && val.length > 0) {
      return parse(val);
    } else if (type === "number" && isNaN(val) === false) {
      return options.long ? fmtLong(val) : fmtShort(val);
    }
    throw new Error(
      "val is not a non-empty string or a valid number. val=" + JSON.stringify(val)
    );
  };
  function parse(str) {
    str = String(str);
    if (str.length > 100) {
      return;
    }
    var match = /^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(
      str
    );
    if (!match) {
      return;
    }
    var n = parseFloat(match[1]);
    var type = (match[2] || "ms").toLowerCase();
    switch (type) {
      case "years":
      case "year":
      case "yrs":
      case "yr":
      case "y":
        return n * y;
      case "days":
      case "day":
      case "d":
        return n * d;
      case "hours":
      case "hour":
      case "hrs":
      case "hr":
      case "h":
        return n * h;
      case "minutes":
      case "minute":
      case "mins":
      case "min":
      case "m":
        return n * m;
      case "seconds":
      case "second":
      case "secs":
      case "sec":
      case "s":
        return n * s;
      case "milliseconds":
      case "millisecond":
      case "msecs":
      case "msec":
      case "ms":
        return n;
      default:
        return void 0;
    }
  }
  function fmtShort(ms2) {
    if (ms2 >= d) {
      return Math.round(ms2 / d) + "d";
    }
    if (ms2 >= h) {
      return Math.round(ms2 / h) + "h";
    }
    if (ms2 >= m) {
      return Math.round(ms2 / m) + "m";
    }
    if (ms2 >= s) {
      return Math.round(ms2 / s) + "s";
    }
    return ms2 + "ms";
  }
  function fmtLong(ms2) {
    return plural(ms2, d, "day") || plural(ms2, h, "hour") || plural(ms2, m, "minute") || plural(ms2, s, "second") || ms2 + " ms";
  }
  function plural(ms2, n, name) {
    if (ms2 < n) {
      return;
    }
    if (ms2 < n * 1.5) {
      return Math.floor(ms2 / n) + " " + name;
    }
    return Math.ceil(ms2 / n) + " " + name + "s";
  }
  return ms;
}
var hasRequiredDebug;
function requireDebug() {
  if (hasRequiredDebug) return debug$1.exports;
  hasRequiredDebug = 1;
  (function(module2, exports) {
    exports = module2.exports = createDebug.debug = createDebug["default"] = createDebug;
    exports.coerce = coerce;
    exports.disable = disable;
    exports.enable = enable;
    exports.enabled = enabled;
    exports.humanize = requireMs();
    exports.names = [];
    exports.skips = [];
    exports.formatters = {};
    var prevTime;
    function selectColor(namespace) {
      var hash = 0, i;
      for (i in namespace) {
        hash = (hash << 5) - hash + namespace.charCodeAt(i);
        hash |= 0;
      }
      return exports.colors[Math.abs(hash) % exports.colors.length];
    }
    function createDebug(namespace) {
      function debug2() {
        if (!debug2.enabled) return;
        var self = debug2;
        var curr = +/* @__PURE__ */ new Date();
        var ms2 = curr - (prevTime || curr);
        self.diff = ms2;
        self.prev = prevTime;
        self.curr = curr;
        prevTime = curr;
        var args = new Array(arguments.length);
        for (var i = 0; i < args.length; i++) {
          args[i] = arguments[i];
        }
        args[0] = exports.coerce(args[0]);
        if ("string" !== typeof args[0]) {
          args.unshift("%O");
        }
        var index = 0;
        args[0] = args[0].replace(/%([a-zA-Z%])/g, function(match, format) {
          if (match === "%%") return match;
          index++;
          var formatter = exports.formatters[format];
          if ("function" === typeof formatter) {
            var val = args[index];
            match = formatter.call(self, val);
            args.splice(index, 1);
            index--;
          }
          return match;
        });
        exports.formatArgs.call(self, args);
        var logFn = debug2.log || exports.log || console.log.bind(console);
        logFn.apply(self, args);
      }
      debug2.namespace = namespace;
      debug2.enabled = exports.enabled(namespace);
      debug2.useColors = exports.useColors();
      debug2.color = selectColor(namespace);
      if ("function" === typeof exports.init) {
        exports.init(debug2);
      }
      return debug2;
    }
    function enable(namespaces) {
      exports.save(namespaces);
      exports.names = [];
      exports.skips = [];
      var split = (typeof namespaces === "string" ? namespaces : "").split(/[\s,]+/);
      var len = split.length;
      for (var i = 0; i < len; i++) {
        if (!split[i]) continue;
        namespaces = split[i].replace(/\*/g, ".*?");
        if (namespaces[0] === "-") {
          exports.skips.push(new RegExp("^" + namespaces.substr(1) + "$"));
        } else {
          exports.names.push(new RegExp("^" + namespaces + "$"));
        }
      }
    }
    function disable() {
      exports.enable("");
    }
    function enabled(name) {
      var i, len;
      for (i = 0, len = exports.skips.length; i < len; i++) {
        if (exports.skips[i].test(name)) {
          return false;
        }
      }
      for (i = 0, len = exports.names.length; i < len; i++) {
        if (exports.names[i].test(name)) {
          return true;
        }
      }
      return false;
    }
    function coerce(val) {
      if (val instanceof Error) return val.stack || val.message;
      return val;
    }
  })(debug$1, debug$1.exports);
  return debug$1.exports;
}
var hasRequiredBrowser;
function requireBrowser() {
  if (hasRequiredBrowser) return browser.exports;
  hasRequiredBrowser = 1;
  (function(module2, exports) {
    exports = module2.exports = requireDebug();
    exports.log = log;
    exports.formatArgs = formatArgs;
    exports.save = save;
    exports.load = load;
    exports.useColors = useColors;
    exports.storage = "undefined" != typeof chrome && "undefined" != typeof chrome.storage ? chrome.storage.local : localstorage();
    exports.colors = [
      "lightseagreen",
      "forestgreen",
      "goldenrod",
      "dodgerblue",
      "darkorchid",
      "crimson"
    ];
    function useColors() {
      if (typeof window !== "undefined" && window.process && window.process.type === "renderer") {
        return true;
      }
      return typeof document !== "undefined" && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance || // is firebug? http://stackoverflow.com/a/398120/376773
      typeof window !== "undefined" && window.console && (window.console.firebug || window.console.exception && window.console.table) || // is firefox >= v31?
      // https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages
      typeof navigator !== "undefined" && navigator.userAgent && navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/) && parseInt(RegExp.$1, 10) >= 31 || // double check webkit in userAgent just in case we are in a worker
      typeof navigator !== "undefined" && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/);
    }
    exports.formatters.j = function(v) {
      try {
        return JSON.stringify(v);
      } catch (err) {
        return "[UnexpectedJSONParseError]: " + err.message;
      }
    };
    function formatArgs(args) {
      var useColors2 = this.useColors;
      args[0] = (useColors2 ? "%c" : "") + this.namespace + (useColors2 ? " %c" : " ") + args[0] + (useColors2 ? "%c " : " ") + "+" + exports.humanize(this.diff);
      if (!useColors2) return;
      var c = "color: " + this.color;
      args.splice(1, 0, c, "color: inherit");
      var index = 0;
      var lastC = 0;
      args[0].replace(/%[a-zA-Z%]/g, function(match) {
        if ("%%" === match) return;
        index++;
        if ("%c" === match) {
          lastC = index;
        }
      });
      args.splice(lastC, 0, c);
    }
    function log() {
      return "object" === typeof console && console.log && Function.prototype.apply.call(console.log, console, arguments);
    }
    function save(namespaces) {
      try {
        if (null == namespaces) {
          exports.storage.removeItem("debug");
        } else {
          exports.storage.debug = namespaces;
        }
      } catch (e) {
      }
    }
    function load() {
      var r;
      try {
        r = exports.storage.debug;
      } catch (e) {
      }
      if (!r && typeof process !== "undefined" && "env" in process) {
        r = process.env.DEBUG;
      }
      return r;
    }
    exports.enable(load());
    function localstorage() {
      try {
        return window.localStorage;
      } catch (e) {
      }
    }
  })(browser, browser.exports);
  return browser.exports;
}
var node = { exports: {} };
var hasRequiredNode;
function requireNode() {
  if (hasRequiredNode) return node.exports;
  hasRequiredNode = 1;
  (function(module2, exports) {
    var tty = require$$0;
    var util = require$$1;
    exports = module2.exports = requireDebug();
    exports.init = init;
    exports.log = log;
    exports.formatArgs = formatArgs;
    exports.save = save;
    exports.load = load;
    exports.useColors = useColors;
    exports.colors = [6, 2, 3, 4, 5, 1];
    exports.inspectOpts = Object.keys(process.env).filter(function(key) {
      return /^debug_/i.test(key);
    }).reduce(function(obj, key) {
      var prop = key.substring(6).toLowerCase().replace(/_([a-z])/g, function(_, k) {
        return k.toUpperCase();
      });
      var val = process.env[key];
      if (/^(yes|on|true|enabled)$/i.test(val)) val = true;
      else if (/^(no|off|false|disabled)$/i.test(val)) val = false;
      else if (val === "null") val = null;
      else val = Number(val);
      obj[prop] = val;
      return obj;
    }, {});
    var fd = parseInt(process.env.DEBUG_FD, 10) || 2;
    if (1 !== fd && 2 !== fd) {
      util.deprecate(function() {
      }, "except for stderr(2) and stdout(1), any other usage of DEBUG_FD is deprecated. Override debug.log if you want to use a different log function (https://git.io/debug_fd)")();
    }
    var stream = 1 === fd ? process.stdout : 2 === fd ? process.stderr : createWritableStdioStream(fd);
    function useColors() {
      return "colors" in exports.inspectOpts ? Boolean(exports.inspectOpts.colors) : tty.isatty(fd);
    }
    exports.formatters.o = function(v) {
      this.inspectOpts.colors = this.useColors;
      return util.inspect(v, this.inspectOpts).split("\n").map(function(str) {
        return str.trim();
      }).join(" ");
    };
    exports.formatters.O = function(v) {
      this.inspectOpts.colors = this.useColors;
      return util.inspect(v, this.inspectOpts);
    };
    function formatArgs(args) {
      var name = this.namespace;
      var useColors2 = this.useColors;
      if (useColors2) {
        var c = this.color;
        var prefix = "  \x1B[3" + c + ";1m" + name + " \x1B[0m";
        args[0] = prefix + args[0].split("\n").join("\n" + prefix);
        args.push("\x1B[3" + c + "m+" + exports.humanize(this.diff) + "\x1B[0m");
      } else {
        args[0] = (/* @__PURE__ */ new Date()).toUTCString() + " " + name + " " + args[0];
      }
    }
    function log() {
      return stream.write(util.format.apply(util, arguments) + "\n");
    }
    function save(namespaces) {
      if (null == namespaces) {
        delete process.env.DEBUG;
      } else {
        process.env.DEBUG = namespaces;
      }
    }
    function load() {
      return process.env.DEBUG;
    }
    function createWritableStdioStream(fd2) {
      var stream2;
      var tty_wrap = process.binding("tty_wrap");
      switch (tty_wrap.guessHandleType(fd2)) {
        case "TTY":
          stream2 = new tty.WriteStream(fd2);
          stream2._type = "tty";
          if (stream2._handle && stream2._handle.unref) {
            stream2._handle.unref();
          }
          break;
        case "FILE":
          var fs = require$$3;
          stream2 = new fs.SyncWriteStream(fd2, { autoClose: false });
          stream2._type = "fs";
          break;
        case "PIPE":
        case "TCP":
          var net = require$$4;
          stream2 = new net.Socket({
            fd: fd2,
            readable: false,
            writable: true
          });
          stream2.readable = false;
          stream2.read = null;
          stream2._type = "pipe";
          if (stream2._handle && stream2._handle.unref) {
            stream2._handle.unref();
          }
          break;
        default:
          throw new Error("Implement me. Unknown stream file type!");
      }
      stream2.fd = fd2;
      stream2._isStdio = true;
      return stream2;
    }
    function init(debug2) {
      debug2.inspectOpts = {};
      var keys = Object.keys(exports.inspectOpts);
      for (var i = 0; i < keys.length; i++) {
        debug2.inspectOpts[keys[i]] = exports.inspectOpts[keys[i]];
      }
    }
    exports.enable(load());
  })(node, node.exports);
  return node.exports;
}
if (typeof process !== "undefined" && process.type === "renderer") {
  src.exports = requireBrowser();
} else {
  src.exports = requireNode();
}
var srcExports = src.exports;
var path = require$$0$1;
var spawn = require$$1$1.spawn;
var debug = srcExports("electron-squirrel-startup");
var app = require$$3$1.app;
var run = function(args, done) {
  var updateExe = path.resolve(path.dirname(process.execPath), "..", "Update.exe");
  debug("Spawning `%s` with args `%s`", updateExe, args);
  spawn(updateExe, args, {
    detached: true
  }).on("close", done);
};
var check = function() {
  if (process.platform === "win32") {
    var cmd = process.argv[1];
    debug("processing squirrel command `%s`", cmd);
    var target = path.basename(process.execPath);
    if (cmd === "--squirrel-install" || cmd === "--squirrel-updated") {
      run(["--createShortcut=" + target], app.quit);
      return true;
    }
    if (cmd === "--squirrel-uninstall") {
      run(["--removeShortcut=" + target], app.quit);
      return true;
    }
    if (cmd === "--squirrel-obsolete") {
      app.quit();
      return true;
    }
  }
  return false;
};
var electronSquirrelStartup = check();
const started = /* @__PURE__ */ getDefaultExportFromCjs(electronSquirrelStartup);
const byteToHex = [];
for (let i = 0; i < 256; ++i) {
  byteToHex.push((i + 256).toString(16).slice(1));
}
function unsafeStringify(arr, offset = 0) {
  return (byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + "-" + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + "-" + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + "-" + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + "-" + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]]).toLowerCase();
}
const rnds8Pool = new Uint8Array(256);
let poolPtr = rnds8Pool.length;
function rng() {
  if (poolPtr > rnds8Pool.length - 16) {
    node_crypto.randomFillSync(rnds8Pool);
    poolPtr = 0;
  }
  return rnds8Pool.slice(poolPtr, poolPtr += 16);
}
const native = { randomUUID: node_crypto.randomUUID };
function _v4(options, buf, offset) {
  var _a;
  options = options || {};
  const rnds = options.random ?? ((_a = options.rng) == null ? void 0 : _a.call(options)) ?? rng();
  if (rnds.length < 16) {
    throw new Error("Random bytes length must be >= 16");
  }
  rnds[6] = rnds[6] & 15 | 64;
  rnds[8] = rnds[8] & 63 | 128;
  return unsafeStringify(rnds);
}
function v4(options, buf, offset) {
  if (native.randomUUID && true && !options) {
    return native.randomUUID();
  }
  return _v4(options);
}
const readFileTool = {
  name: "read_file",
  description: "Read the contents of a file",
  schema: {
    name: "read_file",
    description: "Read the contents of a file",
    parameters: {
      type: "object",
      properties: {
        path: {
          type: "string",
          description: "Path to the file to read"
        },
        encoding: {
          type: "string",
          description: "File encoding (default: utf8)",
          enum: ["utf8", "ascii", "base64", "hex"]
        }
      },
      required: ["path"]
    }
  },
  async execute(args) {
    const { path: path2, encoding = "utf8" } = args;
    const id = v4();
    const startTime = Date.now();
    try {
      const content = await promises.readFile(path2, encoding);
      return {
        id,
        success: true,
        output: content,
        executionTime: Date.now() - startTime
      };
    } catch (error) {
      return {
        id,
        success: false,
        output: "",
        error: `Failed to read file: ${error.message}`,
        executionTime: Date.now() - startTime
      };
    }
  }
};
const writeFileTool = {
  name: "write_file",
  description: "Write content to a file",
  schema: {
    name: "write_file",
    description: "Write content to a file",
    parameters: {
      type: "object",
      properties: {
        path: {
          type: "string",
          description: "Path to the file to write"
        },
        content: {
          type: "string",
          description: "Content to write to the file"
        },
        encoding: {
          type: "string",
          description: "File encoding (default: utf8)",
          enum: ["utf8", "ascii", "base64", "hex"]
        },
        createDirectories: {
          type: "boolean",
          description: "Create parent directories if they don't exist (default: false)"
        }
      },
      required: ["path", "content"]
    }
  },
  async execute(args) {
    const { path: path2, content, encoding = "utf8", createDirectories = false } = args;
    const id = v4();
    const startTime = Date.now();
    try {
      if (createDirectories) {
        const dir = require$$0$1.dirname(path2);
        await promises.mkdir(dir, { recursive: true });
      }
      await promises.writeFile(path2, content, encoding);
      return {
        id,
        success: true,
        output: `File written successfully: ${path2}`,
        executionTime: Date.now() - startTime
      };
    } catch (error) {
      return {
        id,
        success: false,
        output: "",
        error: `Failed to write file: ${error.message}`,
        executionTime: Date.now() - startTime
      };
    }
  }
};
const listDirectoryTool = {
  name: "list_directory",
  description: "List files and directories in a given path",
  schema: {
    name: "list_directory",
    description: "List files and directories in a given path",
    parameters: {
      type: "object",
      properties: {
        path: {
          type: "string",
          description: "Path to the directory to list"
        },
        recursive: {
          type: "boolean",
          description: "List files recursively (default: false)"
        },
        showHidden: {
          type: "boolean",
          description: "Show hidden files and directories (default: false)"
        },
        details: {
          type: "boolean",
          description: "Include detailed file information (default: false)"
        }
      },
      required: ["path"]
    }
  },
  async execute(args) {
    const { path: path2, recursive = false, showHidden = false, details = false } = args;
    const id = v4();
    const startTime = Date.now();
    try {
      const listing = await listDirectory(path2, recursive, showHidden, details);
      return {
        id,
        success: true,
        output: JSON.stringify(listing, null, 2),
        executionTime: Date.now() - startTime
      };
    } catch (error) {
      return {
        id,
        success: false,
        output: "",
        error: `Failed to list directory: ${error.message}`,
        executionTime: Date.now() - startTime
      };
    }
  }
};
const replaceInFileTool = {
  name: "replace_in_file",
  description: "Replace text in a file with new content",
  schema: {
    name: "replace_in_file",
    description: "Replace text in a file with new content",
    parameters: {
      type: "object",
      properties: {
        path: {
          type: "string",
          description: "Path to the file to modify"
        },
        oldText: {
          type: "string",
          description: "Text to replace (exact match required)"
        },
        newText: {
          type: "string",
          description: "New text to replace with"
        },
        backup: {
          type: "boolean",
          description: "Create a backup file before modification (default: true)"
        }
      },
      required: ["path", "oldText", "newText"]
    }
  },
  async execute(args) {
    const { path: path2, oldText, newText, backup = true } = args;
    const id = v4();
    const startTime = Date.now();
    try {
      const originalContent = await promises.readFile(path2, "utf8");
      if (!originalContent.includes(oldText)) {
        return {
          id,
          success: false,
          output: "",
          error: "Text to replace not found in file",
          executionTime: Date.now() - startTime
        };
      }
      if (backup) {
        const backupPath = `${path2}.backup.${Date.now()}`;
        await promises.writeFile(backupPath, originalContent, "utf8");
      }
      const newContent = originalContent.replace(oldText, newText);
      await promises.writeFile(path2, newContent, "utf8");
      return {
        id,
        success: true,
        output: `Text replaced successfully in ${path2}`,
        executionTime: Date.now() - startTime
      };
    } catch (error) {
      return {
        id,
        success: false,
        output: "",
        error: `Failed to replace text in file: ${error.message}`,
        executionTime: Date.now() - startTime
      };
    }
  }
};
const deleteFileTool = {
  name: "delete_file",
  description: "Delete a file or directory",
  schema: {
    name: "delete_file",
    description: "Delete a file or directory",
    parameters: {
      type: "object",
      properties: {
        path: {
          type: "string",
          description: "Path to the file or directory to delete"
        },
        recursive: {
          type: "boolean",
          description: "Delete directories recursively (default: false)"
        }
      },
      required: ["path"]
    }
  },
  async execute(args) {
    const { path: path2, recursive = false } = args;
    const id = v4();
    const startTime = Date.now();
    try {
      const stats = await promises.stat(path2);
      if (stats.isDirectory()) {
        await promises.rmdir(path2, { recursive });
      } else {
        await promises.unlink(path2);
      }
      return {
        id,
        success: true,
        output: `Successfully deleted: ${path2}`,
        executionTime: Date.now() - startTime
      };
    } catch (error) {
      return {
        id,
        success: false,
        output: "",
        error: `Failed to delete: ${error.message}`,
        executionTime: Date.now() - startTime
      };
    }
  }
};
async function listDirectory(path2, recursive, showHidden, details) {
  const items = await promises.readdir(path2);
  const files = [];
  const directories = [];
  for (const item of items) {
    if (!showHidden && item.startsWith(".")) {
      continue;
    }
    const itemPath = require$$0$1.join(path2, item);
    const stats = await promises.stat(itemPath);
    const fileInfo = {
      path: itemPath,
      name: item,
      size: stats.size,
      isDirectory: stats.isDirectory(),
      lastModified: stats.mtime
    };
    if (stats.isDirectory()) {
      directories.push(fileInfo);
      if (recursive) {
        try {
          const subListing = await listDirectory(itemPath, recursive, showHidden, details);
          files.push(...subListing.files);
          directories.push(...subListing.directories);
        } catch (error) {
          console.warn(`Cannot read directory: ${itemPath}`);
        }
      }
    } else {
      files.push(fileInfo);
    }
  }
  return {
    path: path2,
    files: files.sort((a, b) => a.name.localeCompare(b.name)),
    directories: directories.sort((a, b) => a.name.localeCompare(b.name))
  };
}
const fileOperationsTools = [readFileTool, writeFileTool, listDirectoryTool, replaceInFileTool, deleteFileTool];
const execAsync$1 = require$$1.promisify(require$$1$1.exec);
const runningProcesses = /* @__PURE__ */ new Map();
const runShellCommandTool = {
  name: "run_shell_command",
  description: "Execute shell commands with support for different shells and process management",
  schema: {
    name: "run_shell_command",
    description: "Execute shell commands with support for different shells and process management",
    parameters: {
      type: "object",
      properties: {
        command: {
          type: "string",
          description: "The shell command to execute"
        },
        shell: {
          type: "string",
          description: "Shell to use for execution",
          enum: ["cmd", "powershell", "bash", "zsh", "sh"]
        },
        cwd: {
          type: "string",
          description: "Working directory for the command"
        },
        timeout: {
          type: "number",
          description: "Timeout in milliseconds (default: 30000)"
        },
        background: {
          type: "boolean",
          description: "Run command in background and return process ID"
        },
        env: {
          type: "object",
          description: "Environment variables to set"
        }
      },
      required: ["command"]
    }
  },
  async execute(args) {
    const {
      command,
      shell = process.platform === "win32" ? "powershell" : "bash",
      cwd = process.cwd(),
      timeout = 3e4,
      background = false,
      env = {}
    } = args;
    const id = v4();
    try {
      if (background) {
        return await executeInBackground(id, command, shell, cwd, env);
      } else {
        return await executeSync(id, command, shell, cwd, timeout, env);
      }
    } catch (error) {
      return {
        id,
        success: false,
        output: "",
        error: error.message || "Command execution failed",
        executionTime: 0
      };
    }
  }
};
async function executeSync(id, command, shell, cwd, timeout, env) {
  const startTime = Date.now();
  try {
    const execOptions = {
      cwd,
      timeout,
      env: { ...process.env, ...env },
      shell: getShellPath(shell),
      maxBuffer: 1024 * 1024 * 10
      // 10MB buffer
    };
    const { stdout, stderr } = await execAsync$1(command, execOptions);
    return {
      id,
      success: true,
      output: stdout + (stderr ? `
STDERR:
${stderr}` : ""),
      executionTime: Date.now() - startTime
    };
  } catch (error) {
    return {
      id,
      success: false,
      output: error.stdout || "",
      error: `Command failed: ${error.message}
${error.stderr || ""}`,
      executionTime: Date.now() - startTime
    };
  }
}
async function executeInBackground(id, command, shell, cwd, env) {
  const startTime = Date.now();
  try {
    const shellPath = getShellPath(shell);
    const shellArgs = getShellArgs(shell, command);
    const childProcess = require$$1$1.spawn(shellPath, shellArgs, {
      cwd,
      env: { ...process.env, ...env },
      detached: true,
      stdio: ["ignore", "pipe", "pipe"]
    });
    runningProcesses.set(id, childProcess);
    childProcess.unref();
    return {
      id,
      success: true,
      output: `Process started with ID: ${id}, PID: ${childProcess.pid}`,
      executionTime: Date.now() - startTime
    };
  } catch (error) {
    return {
      id,
      success: false,
      output: "",
      error: `Failed to start background process: ${error.message}`,
      executionTime: Date.now() - startTime
    };
  }
}
function getShellPath(shell) {
  switch (shell.toLowerCase()) {
    case "cmd":
      return "cmd.exe";
    case "powershell":
      return "powershell.exe";
    case "bash":
      return "bash";
    case "zsh":
      return "zsh";
    case "sh":
      return "sh";
    default:
      return process.platform === "win32" ? "powershell.exe" : "bash";
  }
}
function getShellArgs(shell, command) {
  switch (shell.toLowerCase()) {
    case "cmd":
      return ["/c", command];
    case "powershell":
      return ["-Command", command];
    case "bash":
    case "zsh":
    case "sh":
      return ["-c", command];
    default:
      return process.platform === "win32" ? ["-Command", command] : ["-c", command];
  }
}
const killProcessTool = {
  name: "kill_process",
  description: "Kill a background process by its ID",
  schema: {
    name: "kill_process",
    description: "Kill a background process by its ID",
    parameters: {
      type: "object",
      properties: {
        processId: {
          type: "string",
          description: "The process ID returned from run_shell_command with background=true"
        },
        signal: {
          type: "string",
          description: "Signal to send to the process",
          enum: ["SIGTERM", "SIGKILL", "SIGINT"]
        }
      },
      required: ["processId"]
    }
  },
  async execute(args) {
    const { processId, signal = "SIGTERM" } = args;
    const id = v4();
    const startTime = Date.now();
    const process2 = runningProcesses.get(processId);
    if (!process2) {
      return {
        id,
        success: false,
        output: "",
        error: `Process with ID ${processId} not found`,
        executionTime: Date.now() - startTime
      };
    }
    try {
      process2.kill(signal);
      runningProcesses.delete(processId);
      return {
        id,
        success: true,
        output: `Process ${processId} killed with signal ${signal}`,
        executionTime: Date.now() - startTime
      };
    } catch (error) {
      return {
        id,
        success: false,
        output: "",
        error: `Failed to kill process: ${error.message}`,
        executionTime: Date.now() - startTime
      };
    }
  }
};
const listProcessesTool = {
  name: "list_processes",
  description: "List all running background processes",
  schema: {
    name: "list_processes",
    description: "List all running background processes",
    parameters: {
      type: "object",
      properties: {},
      required: []
    }
  },
  async execute() {
    const id = v4();
    const startTime = Date.now();
    const processes = Array.from(runningProcesses.entries()).map(([processId, process2]) => ({
      id: processId,
      pid: process2.pid,
      killed: process2.killed,
      connected: process2.connected
    }));
    return {
      id,
      success: true,
      output: JSON.stringify(processes, null, 2),
      executionTime: Date.now() - startTime
    };
  }
};
const shellCommandTools = [runShellCommandTool, killProcessTool, listProcessesTool];
const grepTool = {
  name: "grep_search",
  description: "Search for patterns in files using grep-like functionality",
  schema: {
    name: "grep",
    description: "Search for patterns in files using regular expressions",
    parameters: {
      type: "object",
      properties: {
        pattern: {
          type: "string",
          description: "Regular expression pattern to search for"
        },
        path: {
          type: "string",
          description: "File or directory path to search in"
        },
        recursive: {
          type: "boolean",
          description: "Search recursively in subdirectories (default: false)"
        },
        ignoreCase: {
          type: "boolean",
          description: "Case-insensitive search (default: false)"
        },
        wholeWord: {
          type: "boolean",
          description: "Match whole words only (default: false)"
        },
        filePattern: {
          type: "string",
          description: "File name pattern to include (glob pattern)"
        },
        excludePattern: {
          type: "string",
          description: "File name pattern to exclude (glob pattern)"
        },
        maxResults: {
          type: "number",
          description: "Maximum number of results to return (default: 100)"
        },
        contextLines: {
          type: "number",
          description: "Number of context lines to show around matches (default: 0)"
        }
      },
      required: ["pattern", "path"]
    }
  },
  async execute(args) {
    const options = {
      pattern: args.pattern,
      path: args.path,
      recursive: args.recursive || false,
      ignoreCase: args.ignoreCase || false,
      wholeWord: args.wholeWord || false,
      filePattern: args.filePattern,
      excludePattern: args.excludePattern,
      maxResults: args.maxResults || 100,
      contextLines: args.contextLines || 0
    };
    const id = v4();
    const startTime = Date.now();
    try {
      const results = await searchPattern(options);
      const output = formatSearchResults(results, options);
      return {
        id,
        success: true,
        output,
        executionTime: Date.now() - startTime
      };
    } catch (error) {
      return {
        id,
        success: false,
        output: "",
        error: `Search failed: ${error.message}`,
        executionTime: Date.now() - startTime
      };
    }
  }
};
async function searchPattern(options) {
  const results = [];
  const { pattern, path: path2, recursive, ignoreCase, wholeWord, maxResults } = options;
  let regexPattern = pattern;
  if (wholeWord) {
    regexPattern = `\\b${pattern}\\b`;
  }
  const flags = ignoreCase ? "gi" : "g";
  const regex = new RegExp(regexPattern, flags);
  const files = await getFilesToSearch(path2, recursive || false, options.filePattern, options.excludePattern);
  for (const file of files) {
    if (results.length >= maxResults) {
      break;
    }
    try {
      const fileResults = await searchInFile(file, regex, options.contextLines || 0);
      results.push(...fileResults);
      if (results.length >= maxResults) {
        results.splice(maxResults);
        break;
      }
    } catch (error) {
      console.warn(`Cannot read file: ${file}`);
    }
  }
  return results;
}
async function getFilesToSearch(path2, recursive, filePattern, excludePattern) {
  const files = [];
  try {
    const stats = await promises.stat(path2);
    if (stats.isFile()) {
      if (shouldIncludeFile(path2, filePattern, excludePattern)) {
        files.push(path2);
      }
    } else if (stats.isDirectory()) {
      const items = await promises.readdir(path2);
      for (const item of items) {
        const itemPath = require$$0$1.join(path2, item);
        const itemStats = await promises.stat(itemPath);
        if (itemStats.isFile()) {
          if (shouldIncludeFile(itemPath, filePattern, excludePattern)) {
            files.push(itemPath);
          }
        } else if (itemStats.isDirectory() && recursive) {
          const subFiles = await getFilesToSearch(itemPath, recursive, filePattern, excludePattern);
          files.push(...subFiles);
        }
      }
    }
  } catch (error) {
    throw new Error(`Cannot access path: ${path2}`);
  }
  return files;
}
function shouldIncludeFile(filePath, filePattern, excludePattern) {
  const fileName = filePath.split(/[/\\]/).pop() || "";
  if (excludePattern && matchesPattern(fileName, excludePattern)) {
    return false;
  }
  if (filePattern && !matchesPattern(fileName, filePattern)) {
    return false;
  }
  const binaryExtensions = [".exe", ".dll", ".so", ".dylib", ".bin", ".obj", ".o", ".a", ".lib"];
  const ext = require$$0$1.extname(fileName).toLowerCase();
  if (binaryExtensions.includes(ext)) {
    return false;
  }
  return true;
}
function matchesPattern(fileName, pattern) {
  const regexPattern = pattern.replace(/\./g, "\\.").replace(/\*/g, ".*").replace(/\?/g, ".");
  const regex = new RegExp(`^${regexPattern}$`, "i");
  return regex.test(fileName);
}
async function searchInFile(filePath, regex, contextLines) {
  const results = [];
  try {
    const content = await promises.readFile(filePath, "utf8");
    const lines = content.split("\n");
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const matches = line.match(regex);
      if (matches) {
        for (const match of matches) {
          const result = {
            file: filePath,
            line: i + 1,
            content: line,
            match
          };
          if (contextLines > 0) {
            const contextStart = Math.max(0, i - contextLines);
            const contextEnd = Math.min(lines.length - 1, i + contextLines);
            const contextContent = lines.slice(contextStart, contextEnd + 1);
            result.content = contextContent.map((contextLine, idx) => {
              const lineNum = contextStart + idx + 1;
              const marker = lineNum === i + 1 ? ">" : " ";
              return `${marker} ${lineNum}: ${contextLine}`;
            }).join("\n");
          }
          results.push(result);
        }
      }
    }
  } catch (error) {
    if (error instanceof Error && error.message.includes("EISDIR")) {
      throw error;
    }
  }
  return results;
}
function formatSearchResults(results, options) {
  if (results.length === 0) {
    return `No matches found for pattern: ${options.pattern}`;
  }
  const output = [];
  output.push(`Found ${results.length} matches for pattern: ${options.pattern}`);
  output.push("");
  let currentFile = "";
  for (const result of results) {
    if (result.file !== currentFile) {
      currentFile = result.file;
      output.push(`File: ${result.file}`);
      output.push("─".repeat(50));
    }
    if (options.contextLines && options.contextLines > 0) {
      output.push(result.content);
    } else {
      output.push(`Line ${result.line}: ${result.content}`);
    }
    output.push("");
  }
  return output.join("\n");
}
const grepTools = [grepTool];
const allTools = [
  ...fileOperationsTools,
  ...shellCommandTools,
  ...grepTools
];
allTools.map((tool) => tool.schema.name);
const _ToolRegistry = class _ToolRegistry {
  constructor() {
    __publicField(this, "tools", /* @__PURE__ */ new Map());
    __publicField(this, "toolsLoaded", false);
  }
  static getInstance() {
    if (!_ToolRegistry.instance) {
      _ToolRegistry.instance = new _ToolRegistry();
    }
    return _ToolRegistry.instance;
  }
  async loadTools() {
    var _a;
    if (this.toolsLoaded) {
      return;
    }
    try {
      for (const tool of allTools) {
        if (this.validateTool(tool)) {
          this.tools.set(tool.schema.name, tool);
          console.log(`Loaded tool: ${tool.schema.name}`);
        } else {
          console.warn(`Invalid tool: ${((_a = tool.schema) == null ? void 0 : _a.name) || "unknown"}`);
        }
      }
      this.toolsLoaded = true;
      console.log(`Loaded ${this.tools.size} tools`);
    } catch (error) {
      console.error("Failed to load tools:", error);
    }
  }
  getTool(name) {
    return this.tools.get(name);
  }
  getAllTools() {
    return Array.from(this.tools.values());
  }
  getToolSchemas() {
    const schemas = {};
    for (const [name, tool] of this.tools) {
      schemas[name] = tool.schema;
    }
    return schemas;
  }
  getToolNames() {
    return Array.from(this.tools.keys());
  }
  async executeTool(name, args) {
    const tool = this.tools.get(name);
    if (!tool) {
      return {
        id: `${name}-${Date.now()}`,
        success: false,
        output: "",
        error: `Tool '${name}' not found`,
        executionTime: 0
      };
    }
    const startTime = Date.now();
    try {
      const validationResult = this.validateArguments(tool.schema, args);
      if (!validationResult.valid) {
        return {
          id: `${name}-${Date.now()}`,
          success: false,
          output: "",
          error: `Invalid arguments: ${validationResult.errors.join(", ")}`,
          executionTime: Date.now() - startTime
        };
      }
      const result = await tool.execute(args);
      result.executionTime = Date.now() - startTime;
      return result;
    } catch (error) {
      return {
        id: `${name}-${Date.now()}`,
        success: false,
        output: "",
        error: error.message || "Unknown error occurred",
        executionTime: Date.now() - startTime
      };
    }
  }
  registerTool(tool) {
    var _a;
    if (!this.validateTool(tool)) {
      throw new Error(`Invalid tool: ${((_a = tool.schema) == null ? void 0 : _a.name) || "unknown"}`);
    }
    this.tools.set(tool.schema.name, tool);
    console.log(`Registered tool: ${tool.schema.name}`);
  }
  unregisterTool(name) {
    return this.tools.delete(name);
  }
  hasTools() {
    return this.tools.size > 0;
  }
  getToolCount() {
    return this.tools.size;
  }
  getToolsByCategory(category) {
    if (!category) {
      return this.getAllTools();
    }
    return Array.from(this.tools.values()).filter(
      (tool) => tool.schema.description.toLowerCase().includes(category.toLowerCase())
    );
  }
  validateTool(tool) {
    if (!tool || typeof tool !== "object") {
      return false;
    }
    if (!tool.schema || !tool.execute) {
      return false;
    }
    const schema = tool.schema;
    if (!schema.name || !schema.description || !schema.parameters) {
      return false;
    }
    if (typeof tool.execute !== "function") {
      return false;
    }
    const params = schema.parameters;
    if (params.type !== "object" || !params.properties || !Array.isArray(params.required)) {
      return false;
    }
    return true;
  }
  validateArguments(schema, args) {
    const errors = [];
    const { parameters } = schema;
    for (const requiredParam of parameters.required) {
      if (!(requiredParam in args)) {
        errors.push(`Missing required parameter: ${requiredParam}`);
      }
    }
    for (const [paramName, paramValue] of Object.entries(args)) {
      const paramSchema = parameters.properties[paramName];
      if (!paramSchema) {
        errors.push(`Unknown parameter: ${paramName}`);
        continue;
      }
      const expectedType = paramSchema.type;
      const actualType = typeof paramValue;
      if (expectedType === "string" && actualType !== "string") {
        errors.push(`Parameter '${paramName}' must be a string`);
      } else if (expectedType === "number" && actualType !== "number") {
        errors.push(`Parameter '${paramName}' must be a number`);
      } else if (expectedType === "boolean" && actualType !== "boolean") {
        errors.push(`Parameter '${paramName}' must be a boolean`);
      } else if (expectedType === "array" && !Array.isArray(paramValue)) {
        errors.push(`Parameter '${paramName}' must be an array`);
      } else if (expectedType === "object" && (actualType !== "object" || Array.isArray(paramValue))) {
        errors.push(`Parameter '${paramName}' must be an object`);
      }
      if (paramSchema.enum && !paramSchema.enum.includes(paramValue)) {
        errors.push(`Parameter '${paramName}' must be one of: ${paramSchema.enum.join(", ")}`);
      }
    }
    return {
      valid: errors.length === 0,
      errors
    };
  }
  async reloadTools() {
    this.tools.clear();
    this.toolsLoaded = false;
    await this.loadTools();
  }
  getToolInfo(name) {
    const tool = this.tools.get(name);
    if (!tool) {
      return null;
    }
    return {
      name: tool.schema.name,
      description: tool.schema.description,
      parameters: tool.schema.parameters,
      examples: tool.examples || []
    };
  }
  searchTools(query) {
    const lowerQuery = query.toLowerCase();
    return Array.from(this.tools.values()).filter(
      (tool) => tool.schema.name.toLowerCase().includes(lowerQuery) || tool.schema.description.toLowerCase().includes(lowerQuery)
    );
  }
  clearTools() {
    this.tools.clear();
  }
};
__publicField(_ToolRegistry, "instance");
let ToolRegistry = _ToolRegistry;
const _DatabaseManager = class _DatabaseManager {
  constructor(dbPath) {
    __publicField(this, "db");
    const finalDbPath = dbPath || require$$0$1.join(require$$3$1.app.getPath("userData"), "arien-agent.db");
    this.db = new Database(finalDbPath);
    this.db.pragma("journal_mode = WAL");
    this.db.pragma("foreign_keys = ON");
    this.initializeDatabase();
  }
  static getInstance() {
    if (!_DatabaseManager.instance) {
      _DatabaseManager.instance = new _DatabaseManager();
    }
    return _DatabaseManager.instance;
  }
  static createTestInstance(dbPath) {
    return new _DatabaseManager(dbPath);
  }
  initializeDatabase() {
    try {
      let schemaPath = require$$0$1.join(__dirname, "schema.sql");
      try {
        const schema = require$$3.readFileSync(schemaPath, "utf-8");
        this.db.exec(schema);
        console.log("Database initialized successfully from build directory");
        return;
      } catch (buildError) {
        schemaPath = require$$0$1.join(__dirname, "..", "..", "src", "database", "schema.sql");
        try {
          const schema = require$$3.readFileSync(schemaPath, "utf-8");
          this.db.exec(schema);
          console.log("Database initialized successfully from source directory");
          return;
        } catch (devError) {
          console.error("Failed to read schema from both build and source directories");
          console.error("Build error:", buildError);
          console.error("Dev error:", devError);
          throw devError;
        }
      }
    } catch (error) {
      console.error("Failed to initialize database:", error);
      throw error;
    }
  }
  // Conversation Management
  createConversation(title = "New Conversation") {
    const id = v4();
    const stmt = this.db.prepare(`
      INSERT INTO conversations (id, title)
      VALUES (?, ?)
    `);
    stmt.run(id, title);
    return id;
  }
  getConversations() {
    const stmt = this.db.prepare(`
      SELECT * FROM conversations
      ORDER BY updated_at DESC
    `);
    return stmt.all();
  }
  getConversation(id) {
    const stmt = this.db.prepare(`
      SELECT * FROM conversations WHERE id = ?
    `);
    return stmt.get(id);
  }
  updateConversationTitle(id, title) {
    const stmt = this.db.prepare(`
      UPDATE conversations 
      SET title = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
    stmt.run(title, id);
  }
  deleteConversation(id) {
    const stmt = this.db.prepare(`
      DELETE FROM conversations WHERE id = ?
    `);
    stmt.run(id);
  }
  // Message Management
  saveMessage(message, conversationId) {
    const stmt = this.db.prepare(`
      INSERT INTO messages (id, conversation_id, type, content, timestamp, metadata)
      VALUES (?, ?, ?, ?, ?, ?)
    `);
    stmt.run(
      message.id,
      conversationId,
      message.type,
      message.content,
      message.timestamp.toISOString(),
      message.metadata ? JSON.stringify(message.metadata) : null
    );
  }
  getMessages(conversationId, limit) {
    let query = `
      SELECT * FROM messages 
      WHERE conversation_id = ?
      ORDER BY timestamp ASC
    `;
    if (limit) {
      query += ` LIMIT ${limit}`;
    }
    const stmt = this.db.prepare(query);
    const rows = stmt.all(conversationId);
    return rows.map((row) => ({
      id: row.id,
      type: row.type,
      content: row.content,
      timestamp: new Date(row.timestamp),
      metadata: row.metadata ? JSON.parse(row.metadata) : void 0
    }));
  }
  deleteMessage(id) {
    const stmt = this.db.prepare(`
      DELETE FROM messages WHERE id = ?
    `);
    stmt.run(id);
  }
  // Tool Execution Tracking
  saveToolExecution(messageId, toolName, toolArguments, result, executionTime) {
    const id = v4();
    const stmt = this.db.prepare(`
      INSERT INTO tool_executions (id, message_id, tool_name, arguments, result, execution_time)
      VALUES (?, ?, ?, ?, ?, ?)
    `);
    stmt.run(
      id,
      messageId,
      toolName,
      JSON.stringify(toolArguments),
      JSON.stringify(result),
      executionTime
    );
    return id;
  }
  getToolExecutions(messageId) {
    let query = "SELECT * FROM tool_executions";
    let params = [];
    if (messageId) {
      query += " WHERE message_id = ?";
      params.push(messageId);
    }
    query += " ORDER BY created_at DESC";
    const stmt = this.db.prepare(query);
    return stmt.all(...params);
  }
  // Context Cache Management
  setCacheValue(key, value, expiresAt) {
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO context_cache (id, key, value, expires_at)
      VALUES (?, ?, ?, ?)
    `);
    stmt.run(
      v4(),
      key,
      JSON.stringify(value),
      (expiresAt == null ? void 0 : expiresAt.toISOString()) || null
    );
  }
  getCacheValue(key) {
    const stmt = this.db.prepare(`
      SELECT value, expires_at FROM context_cache 
      WHERE key = ? AND (expires_at IS NULL OR expires_at > CURRENT_TIMESTAMP)
    `);
    const row = stmt.get(key);
    if (!row) return null;
    try {
      return JSON.parse(row.value);
    } catch {
      return null;
    }
  }
  deleteCacheValue(key) {
    const stmt = this.db.prepare(`
      DELETE FROM context_cache WHERE key = ?
    `);
    stmt.run(key);
  }
  clearExpiredCache() {
    const stmt = this.db.prepare(`
      DELETE FROM context_cache 
      WHERE expires_at IS NOT NULL AND expires_at <= CURRENT_TIMESTAMP
    `);
    stmt.run();
  }
  // Settings Management
  saveSetting(key, value) {
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO app_settings (key, value, updated_at)
      VALUES (?, ?, CURRENT_TIMESTAMP)
    `);
    stmt.run(key, JSON.stringify(value));
  }
  getSetting(key) {
    const stmt = this.db.prepare(`
      SELECT value FROM app_settings WHERE key = ?
    `);
    const row = stmt.get(key);
    if (!row) return null;
    try {
      return JSON.parse(row.value);
    } catch {
      return null;
    }
  }
  getAllSettings() {
    const stmt = this.db.prepare(`
      SELECT key, value FROM app_settings
    `);
    const rows = stmt.all();
    const settings = {};
    for (const row of rows) {
      try {
        settings[row.key] = JSON.parse(row.value);
      } catch {
        settings[row.key] = row.value;
      }
    }
    return settings;
  }
  // AppConfig specific methods
  saveAppConfig(config) {
    this.saveSetting("appConfig", config);
  }
  getAppConfig() {
    return this.getSetting("appConfig");
  }
  updateAppConfig(updates) {
    const currentConfig = this.getAppConfig();
    if (currentConfig) {
      const updatedConfig = { ...currentConfig, ...updates };
      this.saveAppConfig(updatedConfig);
    } else {
      const defaultConfig = {
        llm: {
          provider: "openai",
          model: "gpt-4",
          apiKey: "",
          maxTokens: 2e3,
          temperature: 0.7
        },
        executionMode: "confirm",
        autoSave: true,
        maxHistoryLength: 100,
        theme: "dark",
        ...updates
      };
      this.saveAppConfig(defaultConfig);
    }
  }
  // Utility Methods
  close() {
    this.db.close();
  }
  vacuum() {
    this.db.exec("VACUUM");
  }
  getStats() {
    const conversationsCount = this.db.prepare("SELECT COUNT(*) as count FROM conversations").get();
    const messagesCount = this.db.prepare("SELECT COUNT(*) as count FROM messages").get();
    const toolExecutionsCount = this.db.prepare("SELECT COUNT(*) as count FROM tool_executions").get();
    const cacheEntriesCount = this.db.prepare("SELECT COUNT(*) as count FROM context_cache").get();
    return {
      conversations: conversationsCount.count,
      messages: messagesCount.count,
      toolExecutions: toolExecutionsCount.count,
      cacheEntries: cacheEntriesCount.count
    };
  }
};
__publicField(_DatabaseManager, "instance");
let DatabaseManager = _DatabaseManager;
const execAsync = require$$1.promisify(require$$1$1.exec);
function setupIPC() {
  const toolRegistry = ToolRegistry.getInstance();
  const database = DatabaseManager.getInstance();
  toolRegistry.loadTools();
  require$$3$1.ipcMain.handle("execute-tool", async (event, request) => {
    try {
      const result = await toolRegistry.executeTool(request.toolName, request.arguments);
      return {
        requestId: request.requestId,
        success: result.success,
        result: {
          output: result.output,
          error: result.error,
          executionTime: result.executionTime
        }
      };
    } catch (error) {
      return {
        requestId: request.requestId,
        success: false,
        result: null,
        error: error.message || "Tool execution failed"
      };
    }
  });
  require$$3$1.ipcMain.handle("tools-get-schemas", async (event) => {
    try {
      const toolRegistry2 = ToolRegistry.getInstance();
      return toolRegistry2.getToolSchemas();
    } catch (error) {
      console.error("Failed to get tool schemas:", error);
      return [];
    }
  });
  require$$3$1.ipcMain.handle("tools-get-info", async (event, toolName) => {
    try {
      const toolRegistry2 = ToolRegistry.getInstance();
      return toolRegistry2.getToolInfo(toolName);
    } catch (error) {
      console.error("Failed to get tool info:", error);
      return null;
    }
  });
  require$$3$1.ipcMain.handle("fs-read-file", async (event, filePath, encoding = "utf8") => {
    try {
      return await promises.readFile(filePath, encoding);
    } catch (error) {
      throw new Error(`Failed to read file: ${error.message}`);
    }
  });
  require$$3$1.ipcMain.handle("fs-write-file", async (event, filePath, content, encoding = "utf8") => {
    try {
      const dir = require$$0$1.dirname(filePath);
      await import("fs/promises").then((fs) => fs.mkdir(dir, { recursive: true }));
      await promises.writeFile(filePath, content, encoding);
    } catch (error) {
      throw new Error(`Failed to write file: ${error.message}`);
    }
  });
  require$$3$1.ipcMain.handle("fs-list-directory", async (event, dirPath, options = {}) => {
    try {
      const items = await promises.readdir(dirPath);
      const results = [];
      for (const item of items) {
        const itemPath = require$$0$1.join(dirPath, item);
        const stats = await promises.stat(itemPath);
        results.push({
          name: item,
          path: itemPath,
          isDirectory: stats.isDirectory(),
          size: stats.size,
          lastModified: stats.mtime
        });
      }
      return results;
    } catch (error) {
      throw new Error(`Failed to list directory: ${error.message}`);
    }
  });
  require$$3$1.ipcMain.handle("fs-delete-file", async (event, filePath, options = {}) => {
    try {
      const stats = await promises.stat(filePath);
      if (stats.isDirectory()) {
        await promises.rmdir(filePath, { recursive: options.recursive || false });
      } else {
        await promises.unlink(filePath);
      }
    } catch (error) {
      throw new Error(`Failed to delete: ${error.message}`);
    }
  });
  require$$3$1.ipcMain.handle("shell-execute", async (event, command, options = {}) => {
    try {
      const { stdout, stderr } = await execAsync(command, {
        cwd: options.cwd || process.cwd(),
        timeout: options.timeout || 3e4,
        env: { ...process.env, ...options.env }
      });
      return {
        success: true,
        stdout,
        stderr,
        exitCode: 0
      };
    } catch (error) {
      return {
        success: false,
        stdout: error.stdout || "",
        stderr: error.stderr || "",
        exitCode: error.code || 1,
        error: error.message
      };
    }
  });
  require$$3$1.ipcMain.handle("get-system-info", async () => {
    return {
      platform: process.platform,
      arch: process.arch,
      nodeVersion: process.version,
      electronVersion: process.versions.electron,
      chromeVersion: process.versions.chrome,
      cwd: process.cwd(),
      homedir: require("os").homedir(),
      tmpdir: require("os").tmpdir()
    };
  });
  require$$3$1.ipcMain.handle("get-settings", async () => {
    try {
      return database.getAllSettings();
    } catch (error) {
      throw new Error(`Failed to get settings: ${error.message}`);
    }
  });
  require$$3$1.ipcMain.handle("save-settings", async (event, settings) => {
    try {
      for (const [key, value] of Object.entries(settings)) {
        database.saveSetting(key, value);
      }
    } catch (error) {
      throw new Error(`Failed to save settings: ${error.message}`);
    }
  });
  require$$3$1.ipcMain.on("window-minimize", (event) => {
    const window2 = require$$3$1.BrowserWindow.fromWebContents(event.sender);
    window2 == null ? void 0 : window2.minimize();
  });
  require$$3$1.ipcMain.on("window-maximize", (event) => {
    const window2 = require$$3$1.BrowserWindow.fromWebContents(event.sender);
    if (window2 == null ? void 0 : window2.isMaximized()) {
      window2.unmaximize();
    } else {
      window2 == null ? void 0 : window2.maximize();
    }
  });
  require$$3$1.ipcMain.on("window-close", (event) => {
    const window2 = require$$3$1.BrowserWindow.fromWebContents(event.sender);
    window2 == null ? void 0 : window2.close();
  });
  require$$3$1.ipcMain.handle("db-create-conversation", async (event, title) => {
    try {
      return database.createConversation(title);
    } catch (error) {
      throw new Error(`Failed to create conversation: ${error.message}`);
    }
  });
  require$$3$1.ipcMain.handle("db-get-conversations", async () => {
    try {
      return database.getConversations();
    } catch (error) {
      throw new Error(`Failed to get conversations: ${error.message}`);
    }
  });
  require$$3$1.ipcMain.handle("db-get-messages", async (event, conversationId) => {
    try {
      return database.getMessages(conversationId);
    } catch (error) {
      throw new Error(`Failed to get messages: ${error.message}`);
    }
  });
  require$$3$1.ipcMain.handle("db-save-message", async (event, message, conversationId) => {
    try {
      database.saveMessage(message, conversationId);
    } catch (error) {
      throw new Error(`Failed to save message: ${error.message}`);
    }
  });
  require$$3$1.ipcMain.handle("db-delete-conversation", async (event, conversationId) => {
    try {
      database.deleteConversation(conversationId);
    } catch (error) {
      throw new Error(`Failed to delete conversation: ${error.message}`);
    }
  });
  require$$3$1.ipcMain.handle("tools-get-available", async () => {
    try {
      return toolRegistry.getToolNames();
    } catch (error) {
      throw new Error(`Failed to get available tools: ${error.message}`);
    }
  });
  require$$3$1.ipcMain.handle("tools-get-schemas", async () => {
    try {
      return toolRegistry.getToolSchemas();
    } catch (error) {
      throw new Error(`Failed to get tool schemas: ${error.message}`);
    }
  });
  require$$3$1.ipcMain.handle("tools-get-info", async (event, toolName) => {
    try {
      return toolRegistry.getToolInfo(toolName);
    } catch (error) {
      throw new Error(`Failed to get tool info: ${error.message}`);
    }
  });
  require$$3$1.ipcMain.handle("save-message", async (event, message, conversationId) => {
    try {
      const database2 = DatabaseManager.getInstance();
      database2.saveMessage(message, conversationId);
    } catch (error) {
      console.error("Failed to save message:", error);
    }
  });
  require$$3$1.ipcMain.handle("get-messages", async (event, conversationId) => {
    try {
      const database2 = DatabaseManager.getInstance();
      return database2.getMessages(conversationId);
    } catch (error) {
      console.error("Failed to get messages:", error);
      return [];
    }
  });
  require$$3$1.ipcMain.handle("delete-message", async (event, messageId) => {
    try {
      const database2 = DatabaseManager.getInstance();
      database2.deleteMessage(messageId);
    } catch (error) {
      console.error("Failed to delete message:", error);
    }
  });
  process.on("uncaughtException", (error) => {
    console.error("Uncaught Exception:", error);
    const windows = require$$3$1.BrowserWindow.getAllWindows();
    windows.forEach((window2) => {
      window2.webContents.send("main-process-error", {
        type: "uncaughtException",
        message: error.message,
        stack: error.stack
      });
    });
  });
  process.on("unhandledRejection", (reason, promise) => {
    console.error("Unhandled Rejection at:", promise, "reason:", reason);
    const windows = require$$3$1.BrowserWindow.getAllWindows();
    windows.forEach((window2) => {
      window2.webContents.send("main-process-error", {
        type: "unhandledRejection",
        message: String(reason),
        promise: String(promise)
      });
    });
  });
  console.log("IPC handlers set up successfully");
}
if (started) {
  require$$3$1.app.quit();
}
const createWindow = () => {
  const mainWindow = new require$$3$1.BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 400,
    minHeight: 300,
    resizable: true,
    autoHideMenuBar: true,
    webPreferences: {
      preload: path$1.join(__dirname, "preload.js"),
      nodeIntegration: false,
      contextIsolation: true
    },
    titleBarStyle: "default",
    show: false
  });
  mainWindow.setMenuBarVisibility(false);
  mainWindow.once("ready-to-show", () => {
    mainWindow.show();
  });
  {
    mainWindow.loadURL("http://localhost:5173");
    mainWindow.webContents.openDevTools();
  }
};
require$$3$1.app.on("ready", () => {
  setupIPC();
  createWindow();
});
require$$3$1.app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    require$$3$1.app.quit();
  }
});
require$$3$1.app.on("activate", () => {
  if (require$$3$1.BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
