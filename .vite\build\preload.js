"use strict";
const electron = require("electron");
electron.contextBridge.exposeInMainWorld("electronAPI", {
  // Tool execution
  executeTool: (request) => electron.ipcRenderer.invoke("execute-tool", request),
  // Tool management
  getToolSchemas: () => electron.ipcRenderer.invoke("tools-get-schemas"),
  getToolInfo: (toolName) => electron.ipcRenderer.invoke("tools-get-info", toolName),
  // File system operations
  readFile: (path, encoding) => electron.ipcRenderer.invoke("fs-read-file", path, encoding),
  writeFile: (path, content, encoding) => electron.ipcRenderer.invoke("fs-write-file", path, content, encoding),
  listDirectory: (path, options) => electron.ipcRenderer.invoke("fs-list-directory", path, options),
  deleteFile: (path, options) => electron.ipcRenderer.invoke("fs-delete-file", path, options),
  // Shell command execution
  executeCommand: (command, options) => electron.ipcRenderer.invoke("shell-execute", command, options),
  // System information
  getSystemInfo: () => electron.ipcRenderer.invoke("get-system-info"),
  // App control
  minimizeWindow: () => electron.ipcRenderer.send("window-minimize"),
  maximizeWindow: () => electron.ipcRenderer.send("window-maximize"),
  closeWindow: () => electron.ipcRenderer.send("window-close"),
  // Settings
  getSettings: () => electron.ipcRenderer.invoke("get-settings"),
  saveSettings: (settings) => electron.ipcRenderer.invoke("save-settings", settings),
  // Message management
  saveMessage: (message, conversationId) => electron.ipcRenderer.invoke("save-message", message, conversationId),
  getMessages: (conversationId) => electron.ipcRenderer.invoke("get-messages", conversationId),
  deleteMessage: (messageId) => electron.ipcRenderer.invoke("delete-message", messageId),
  // Event listeners
  onToolExecutionProgress: (callback) => {
    electron.ipcRenderer.on("tool-execution-progress", (_event, data) => callback(data));
  },
  removeAllListeners: (channel) => {
    electron.ipcRenderer.removeAllListeners(channel);
  }
});
