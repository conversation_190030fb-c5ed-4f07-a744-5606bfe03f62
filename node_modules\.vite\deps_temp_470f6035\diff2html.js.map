{"version": 3, "sources": ["../../hogan.js/lib/compiler.js", "../../hogan.js/lib/template.js", "../../hogan.js/lib/hogan.js", "../../diff2html/src/types.ts", "../../diff2html/src/utils.ts", "../../diff2html/src/diff-parser.ts", "../../diff2html/node_modules/diff/lib/index.mjs", "../../diff2html/src/rematch.ts", "../../diff2html/src/render-utils.ts", "../../diff2html/src/file-list-renderer.ts", "../../diff2html/src/line-by-line-renderer.ts", "../../diff2html/src/side-by-side-renderer.ts", "../../diff2html/src/hoganjs-utils.ts", "../../diff2html/src/diff2html-templates.ts", "../../diff2html/src/diff2html.ts"], "sourcesContent": ["/*\n *  Copyright 2011 Twitter, Inc.\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *  http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n(function (<PERSON>) {\n  // Setup regex  assignments\n  // remove whitespace according to Mustache spec\n  var rIsWhitespace = /\\S/,\n      rQuot = /\\\"/g,\n      rNewline =  /\\n/g,\n      rCr = /\\r/g,\n      rSlash = /\\\\/g,\n      rLineSep = /\\u2028/,\n      rParagraphSep = /\\u2029/;\n\n  Hogan.tags = {\n    '#': 1, '^': 2, '<': 3, '$': 4,\n    '/': 5, '!': 6, '>': 7, '=': 8, '_v': 9,\n    '{': 10, '&': 11, '_t': 12\n  };\n\n  Hogan.scan = function scan(text, delimiters) {\n    var len = text.length,\n        IN_TEXT = 0,\n        IN_TAG_TYPE = 1,\n        IN_TAG = 2,\n        state = IN_TEXT,\n        tagType = null,\n        tag = null,\n        buf = '',\n        tokens = [],\n        seenTag = false,\n        i = 0,\n        lineStart = 0,\n        otag = '{{',\n        ctag = '}}';\n\n    function addBuf() {\n      if (buf.length > 0) {\n        tokens.push({tag: '_t', text: new String(buf)});\n        buf = '';\n      }\n    }\n\n    function lineIsWhitespace() {\n      var isAllWhitespace = true;\n      for (var j = lineStart; j < tokens.length; j++) {\n        isAllWhitespace =\n          (Hogan.tags[tokens[j].tag] < Hogan.tags['_v']) ||\n          (tokens[j].tag == '_t' && tokens[j].text.match(rIsWhitespace) === null);\n        if (!isAllWhitespace) {\n          return false;\n        }\n      }\n\n      return isAllWhitespace;\n    }\n\n    function filterLine(haveSeenTag, noNewLine) {\n      addBuf();\n\n      if (haveSeenTag && lineIsWhitespace()) {\n        for (var j = lineStart, next; j < tokens.length; j++) {\n          if (tokens[j].text) {\n            if ((next = tokens[j+1]) && next.tag == '>') {\n              // set indent to token value\n              next.indent = tokens[j].text.toString()\n            }\n            tokens.splice(j, 1);\n          }\n        }\n      } else if (!noNewLine) {\n        tokens.push({tag:'\\n'});\n      }\n\n      seenTag = false;\n      lineStart = tokens.length;\n    }\n\n    function changeDelimiters(text, index) {\n      var close = '=' + ctag,\n          closeIndex = text.indexOf(close, index),\n          delimiters = trim(\n            text.substring(text.indexOf('=', index) + 1, closeIndex)\n          ).split(' ');\n\n      otag = delimiters[0];\n      ctag = delimiters[delimiters.length - 1];\n\n      return closeIndex + close.length - 1;\n    }\n\n    if (delimiters) {\n      delimiters = delimiters.split(' ');\n      otag = delimiters[0];\n      ctag = delimiters[1];\n    }\n\n    for (i = 0; i < len; i++) {\n      if (state == IN_TEXT) {\n        if (tagChange(otag, text, i)) {\n          --i;\n          addBuf();\n          state = IN_TAG_TYPE;\n        } else {\n          if (text.charAt(i) == '\\n') {\n            filterLine(seenTag);\n          } else {\n            buf += text.charAt(i);\n          }\n        }\n      } else if (state == IN_TAG_TYPE) {\n        i += otag.length - 1;\n        tag = Hogan.tags[text.charAt(i + 1)];\n        tagType = tag ? text.charAt(i + 1) : '_v';\n        if (tagType == '=') {\n          i = changeDelimiters(text, i);\n          state = IN_TEXT;\n        } else {\n          if (tag) {\n            i++;\n          }\n          state = IN_TAG;\n        }\n        seenTag = i;\n      } else {\n        if (tagChange(ctag, text, i)) {\n          tokens.push({tag: tagType, n: trim(buf), otag: otag, ctag: ctag,\n                       i: (tagType == '/') ? seenTag - otag.length : i + ctag.length});\n          buf = '';\n          i += ctag.length - 1;\n          state = IN_TEXT;\n          if (tagType == '{') {\n            if (ctag == '}}') {\n              i++;\n            } else {\n              cleanTripleStache(tokens[tokens.length - 1]);\n            }\n          }\n        } else {\n          buf += text.charAt(i);\n        }\n      }\n    }\n\n    filterLine(seenTag, true);\n\n    return tokens;\n  }\n\n  function cleanTripleStache(token) {\n    if (token.n.substr(token.n.length - 1) === '}') {\n      token.n = token.n.substring(0, token.n.length - 1);\n    }\n  }\n\n  function trim(s) {\n    if (s.trim) {\n      return s.trim();\n    }\n\n    return s.replace(/^\\s*|\\s*$/g, '');\n  }\n\n  function tagChange(tag, text, index) {\n    if (text.charAt(index) != tag.charAt(0)) {\n      return false;\n    }\n\n    for (var i = 1, l = tag.length; i < l; i++) {\n      if (text.charAt(index + i) != tag.charAt(i)) {\n        return false;\n      }\n    }\n\n    return true;\n  }\n\n  // the tags allowed inside super templates\n  var allowedInSuper = {'_t': true, '\\n': true, '$': true, '/': true};\n\n  function buildTree(tokens, kind, stack, customTags) {\n    var instructions = [],\n        opener = null,\n        tail = null,\n        token = null;\n\n    tail = stack[stack.length - 1];\n\n    while (tokens.length > 0) {\n      token = tokens.shift();\n\n      if (tail && tail.tag == '<' && !(token.tag in allowedInSuper)) {\n        throw new Error('Illegal content in < super tag.');\n      }\n\n      if (Hogan.tags[token.tag] <= Hogan.tags['$'] || isOpener(token, customTags)) {\n        stack.push(token);\n        token.nodes = buildTree(tokens, token.tag, stack, customTags);\n      } else if (token.tag == '/') {\n        if (stack.length === 0) {\n          throw new Error('Closing tag without opener: /' + token.n);\n        }\n        opener = stack.pop();\n        if (token.n != opener.n && !isCloser(token.n, opener.n, customTags)) {\n          throw new Error('Nesting error: ' + opener.n + ' vs. ' + token.n);\n        }\n        opener.end = token.i;\n        return instructions;\n      } else if (token.tag == '\\n') {\n        token.last = (tokens.length == 0) || (tokens[0].tag == '\\n');\n      }\n\n      instructions.push(token);\n    }\n\n    if (stack.length > 0) {\n      throw new Error('missing closing tag: ' + stack.pop().n);\n    }\n\n    return instructions;\n  }\n\n  function isOpener(token, tags) {\n    for (var i = 0, l = tags.length; i < l; i++) {\n      if (tags[i].o == token.n) {\n        token.tag = '#';\n        return true;\n      }\n    }\n  }\n\n  function isCloser(close, open, tags) {\n    for (var i = 0, l = tags.length; i < l; i++) {\n      if (tags[i].c == close && tags[i].o == open) {\n        return true;\n      }\n    }\n  }\n\n  function stringifySubstitutions(obj) {\n    var items = [];\n    for (var key in obj) {\n      items.push('\"' + esc(key) + '\": function(c,p,t,i) {' + obj[key] + '}');\n    }\n    return \"{ \" + items.join(\",\") + \" }\";\n  }\n\n  function stringifyPartials(codeObj) {\n    var partials = [];\n    for (var key in codeObj.partials) {\n      partials.push('\"' + esc(key) + '\":{name:\"' + esc(codeObj.partials[key].name) + '\", ' + stringifyPartials(codeObj.partials[key]) + \"}\");\n    }\n    return \"partials: {\" + partials.join(\",\") + \"}, subs: \" + stringifySubstitutions(codeObj.subs);\n  }\n\n  Hogan.stringify = function(codeObj, text, options) {\n    return \"{code: function (c,p,i) { \" + Hogan.wrapMain(codeObj.code) + \" },\" + stringifyPartials(codeObj) +  \"}\";\n  }\n\n  var serialNo = 0;\n  Hogan.generate = function(tree, text, options) {\n    serialNo = 0;\n    var context = { code: '', subs: {}, partials: {} };\n    Hogan.walk(tree, context);\n\n    if (options.asString) {\n      return this.stringify(context, text, options);\n    }\n\n    return this.makeTemplate(context, text, options);\n  }\n\n  Hogan.wrapMain = function(code) {\n    return 'var t=this;t.b(i=i||\"\");' + code + 'return t.fl();';\n  }\n\n  Hogan.template = Hogan.Template;\n\n  Hogan.makeTemplate = function(codeObj, text, options) {\n    var template = this.makePartials(codeObj);\n    template.code = new Function('c', 'p', 'i', this.wrapMain(codeObj.code));\n    return new this.template(template, text, this, options);\n  }\n\n  Hogan.makePartials = function(codeObj) {\n    var key, template = {subs: {}, partials: codeObj.partials, name: codeObj.name};\n    for (key in template.partials) {\n      template.partials[key] = this.makePartials(template.partials[key]);\n    }\n    for (key in codeObj.subs) {\n      template.subs[key] = new Function('c', 'p', 't', 'i', codeObj.subs[key]);\n    }\n    return template;\n  }\n\n  function esc(s) {\n    return s.replace(rSlash, '\\\\\\\\')\n            .replace(rQuot, '\\\\\\\"')\n            .replace(rNewline, '\\\\n')\n            .replace(rCr, '\\\\r')\n            .replace(rLineSep, '\\\\u2028')\n            .replace(rParagraphSep, '\\\\u2029');\n  }\n\n  function chooseMethod(s) {\n    return (~s.indexOf('.')) ? 'd' : 'f';\n  }\n\n  function createPartial(node, context) {\n    var prefix = \"<\" + (context.prefix || \"\");\n    var sym = prefix + node.n + serialNo++;\n    context.partials[sym] = {name: node.n, partials: {}};\n    context.code += 't.b(t.rp(\"' +  esc(sym) + '\",c,p,\"' + (node.indent || '') + '\"));';\n    return sym;\n  }\n\n  Hogan.codegen = {\n    '#': function(node, context) {\n      context.code += 'if(t.s(t.' + chooseMethod(node.n) + '(\"' + esc(node.n) + '\",c,p,1),' +\n                      'c,p,0,' + node.i + ',' + node.end + ',\"' + node.otag + \" \" + node.ctag + '\")){' +\n                      't.rs(c,p,' + 'function(c,p,t){';\n      Hogan.walk(node.nodes, context);\n      context.code += '});c.pop();}';\n    },\n\n    '^': function(node, context) {\n      context.code += 'if(!t.s(t.' + chooseMethod(node.n) + '(\"' + esc(node.n) + '\",c,p,1),c,p,1,0,0,\"\")){';\n      Hogan.walk(node.nodes, context);\n      context.code += '};';\n    },\n\n    '>': createPartial,\n    '<': function(node, context) {\n      var ctx = {partials: {}, code: '', subs: {}, inPartial: true};\n      Hogan.walk(node.nodes, ctx);\n      var template = context.partials[createPartial(node, context)];\n      template.subs = ctx.subs;\n      template.partials = ctx.partials;\n    },\n\n    '$': function(node, context) {\n      var ctx = {subs: {}, code: '', partials: context.partials, prefix: node.n};\n      Hogan.walk(node.nodes, ctx);\n      context.subs[node.n] = ctx.code;\n      if (!context.inPartial) {\n        context.code += 't.sub(\"' + esc(node.n) + '\",c,p,i);';\n      }\n    },\n\n    '\\n': function(node, context) {\n      context.code += write('\"\\\\n\"' + (node.last ? '' : ' + i'));\n    },\n\n    '_v': function(node, context) {\n      context.code += 't.b(t.v(t.' + chooseMethod(node.n) + '(\"' + esc(node.n) + '\",c,p,0)));';\n    },\n\n    '_t': function(node, context) {\n      context.code += write('\"' + esc(node.text) + '\"');\n    },\n\n    '{': tripleStache,\n\n    '&': tripleStache\n  }\n\n  function tripleStache(node, context) {\n    context.code += 't.b(t.t(t.' + chooseMethod(node.n) + '(\"' + esc(node.n) + '\",c,p,0)));';\n  }\n\n  function write(s) {\n    return 't.b(' + s + ');';\n  }\n\n  Hogan.walk = function(nodelist, context) {\n    var func;\n    for (var i = 0, l = nodelist.length; i < l; i++) {\n      func = Hogan.codegen[nodelist[i].tag];\n      func && func(nodelist[i], context);\n    }\n    return context;\n  }\n\n  Hogan.parse = function(tokens, text, options) {\n    options = options || {};\n    return buildTree(tokens, '', [], options.sectionTags || []);\n  }\n\n  Hogan.cache = {};\n\n  Hogan.cacheKey = function(text, options) {\n    return [text, !!options.asString, !!options.disableLambda, options.delimiters, !!options.modelGet].join('||');\n  }\n\n  Hogan.compile = function(text, options) {\n    options = options || {};\n    var key = Hogan.cacheKey(text, options);\n    var template = this.cache[key];\n\n    if (template) {\n      var partials = template.partials;\n      for (var name in partials) {\n        delete partials[name].instance;\n      }\n      return template;\n    }\n\n    template = this.generate(this.parse(this.scan(text, options.delimiters), text, options), text, options);\n    return this.cache[key] = template;\n  }\n})(typeof exports !== 'undefined' ? exports : Hogan);\n", "/*\n *  Copyright 2011 Twitter, Inc.\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *  http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\nvar Hogan = {};\n\n(function (Hogan) {\n  Hogan.Template = function (codeObj, text, compiler, options) {\n    codeObj = codeObj || {};\n    this.r = codeObj.code || this.r;\n    this.c = compiler;\n    this.options = options || {};\n    this.text = text || '';\n    this.partials = codeObj.partials || {};\n    this.subs = codeObj.subs || {};\n    this.buf = '';\n  }\n\n  Hogan.Template.prototype = {\n    // render: replaced by generated code.\n    r: function (context, partials, indent) { return ''; },\n\n    // variable escaping\n    v: hoganEscape,\n\n    // triple stache\n    t: coerceToString,\n\n    render: function render(context, partials, indent) {\n      return this.ri([context], partials || {}, indent);\n    },\n\n    // render internal -- a hook for overrides that catches partials too\n    ri: function (context, partials, indent) {\n      return this.r(context, partials, indent);\n    },\n\n    // ensurePartial\n    ep: function(symbol, partials) {\n      var partial = this.partials[symbol];\n\n      // check to see that if we've instantiated this partial before\n      var template = partials[partial.name];\n      if (partial.instance && partial.base == template) {\n        return partial.instance;\n      }\n\n      if (typeof template == 'string') {\n        if (!this.c) {\n          throw new Error(\"No compiler available.\");\n        }\n        template = this.c.compile(template, this.options);\n      }\n\n      if (!template) {\n        return null;\n      }\n\n      // We use this to check whether the partials dictionary has changed\n      this.partials[symbol].base = template;\n\n      if (partial.subs) {\n        // Make sure we consider parent template now\n        if (!partials.stackText) partials.stackText = {};\n        for (key in partial.subs) {\n          if (!partials.stackText[key]) {\n            partials.stackText[key] = (this.activeSub !== undefined && partials.stackText[this.activeSub]) ? partials.stackText[this.activeSub] : this.text;\n          }\n        }\n        template = createSpecializedPartial(template, partial.subs, partial.partials,\n          this.stackSubs, this.stackPartials, partials.stackText);\n      }\n      this.partials[symbol].instance = template;\n\n      return template;\n    },\n\n    // tries to find a partial in the current scope and render it\n    rp: function(symbol, context, partials, indent) {\n      var partial = this.ep(symbol, partials);\n      if (!partial) {\n        return '';\n      }\n\n      return partial.ri(context, partials, indent);\n    },\n\n    // render a section\n    rs: function(context, partials, section) {\n      var tail = context[context.length - 1];\n\n      if (!isArray(tail)) {\n        section(context, partials, this);\n        return;\n      }\n\n      for (var i = 0; i < tail.length; i++) {\n        context.push(tail[i]);\n        section(context, partials, this);\n        context.pop();\n      }\n    },\n\n    // maybe start a section\n    s: function(val, ctx, partials, inverted, start, end, tags) {\n      var pass;\n\n      if (isArray(val) && val.length === 0) {\n        return false;\n      }\n\n      if (typeof val == 'function') {\n        val = this.ms(val, ctx, partials, inverted, start, end, tags);\n      }\n\n      pass = !!val;\n\n      if (!inverted && pass && ctx) {\n        ctx.push((typeof val == 'object') ? val : ctx[ctx.length - 1]);\n      }\n\n      return pass;\n    },\n\n    // find values with dotted names\n    d: function(key, ctx, partials, returnFound) {\n      var found,\n          names = key.split('.'),\n          val = this.f(names[0], ctx, partials, returnFound),\n          doModelGet = this.options.modelGet,\n          cx = null;\n\n      if (key === '.' && isArray(ctx[ctx.length - 2])) {\n        val = ctx[ctx.length - 1];\n      } else {\n        for (var i = 1; i < names.length; i++) {\n          found = findInScope(names[i], val, doModelGet);\n          if (found !== undefined) {\n            cx = val;\n            val = found;\n          } else {\n            val = '';\n          }\n        }\n      }\n\n      if (returnFound && !val) {\n        return false;\n      }\n\n      if (!returnFound && typeof val == 'function') {\n        ctx.push(cx);\n        val = this.mv(val, ctx, partials);\n        ctx.pop();\n      }\n\n      return val;\n    },\n\n    // find values with normal names\n    f: function(key, ctx, partials, returnFound) {\n      var val = false,\n          v = null,\n          found = false,\n          doModelGet = this.options.modelGet;\n\n      for (var i = ctx.length - 1; i >= 0; i--) {\n        v = ctx[i];\n        val = findInScope(key, v, doModelGet);\n        if (val !== undefined) {\n          found = true;\n          break;\n        }\n      }\n\n      if (!found) {\n        return (returnFound) ? false : \"\";\n      }\n\n      if (!returnFound && typeof val == 'function') {\n        val = this.mv(val, ctx, partials);\n      }\n\n      return val;\n    },\n\n    // higher order templates\n    ls: function(func, cx, partials, text, tags) {\n      var oldTags = this.options.delimiters;\n\n      this.options.delimiters = tags;\n      this.b(this.ct(coerceToString(func.call(cx, text)), cx, partials));\n      this.options.delimiters = oldTags;\n\n      return false;\n    },\n\n    // compile text\n    ct: function(text, cx, partials) {\n      if (this.options.disableLambda) {\n        throw new Error('Lambda features disabled.');\n      }\n      return this.c.compile(text, this.options).render(cx, partials);\n    },\n\n    // template result buffering\n    b: function(s) { this.buf += s; },\n\n    fl: function() { var r = this.buf; this.buf = ''; return r; },\n\n    // method replace section\n    ms: function(func, ctx, partials, inverted, start, end, tags) {\n      var textSource,\n          cx = ctx[ctx.length - 1],\n          result = func.call(cx);\n\n      if (typeof result == 'function') {\n        if (inverted) {\n          return true;\n        } else {\n          textSource = (this.activeSub && this.subsText && this.subsText[this.activeSub]) ? this.subsText[this.activeSub] : this.text;\n          return this.ls(result, cx, partials, textSource.substring(start, end), tags);\n        }\n      }\n\n      return result;\n    },\n\n    // method replace variable\n    mv: function(func, ctx, partials) {\n      var cx = ctx[ctx.length - 1];\n      var result = func.call(cx);\n\n      if (typeof result == 'function') {\n        return this.ct(coerceToString(result.call(cx)), cx, partials);\n      }\n\n      return result;\n    },\n\n    sub: function(name, context, partials, indent) {\n      var f = this.subs[name];\n      if (f) {\n        this.activeSub = name;\n        f(context, partials, this, indent);\n        this.activeSub = false;\n      }\n    }\n\n  };\n\n  //Find a key in an object\n  function findInScope(key, scope, doModelGet) {\n    var val;\n\n    if (scope && typeof scope == 'object') {\n\n      if (scope[key] !== undefined) {\n        val = scope[key];\n\n      // try lookup with get for backbone or similar model data\n      } else if (doModelGet && scope.get && typeof scope.get == 'function') {\n        val = scope.get(key);\n      }\n    }\n\n    return val;\n  }\n\n  function createSpecializedPartial(instance, subs, partials, stackSubs, stackPartials, stackText) {\n    function PartialTemplate() {};\n    PartialTemplate.prototype = instance;\n    function Substitutions() {};\n    Substitutions.prototype = instance.subs;\n    var key;\n    var partial = new PartialTemplate();\n    partial.subs = new Substitutions();\n    partial.subsText = {};  //hehe. substext.\n    partial.buf = '';\n\n    stackSubs = stackSubs || {};\n    partial.stackSubs = stackSubs;\n    partial.subsText = stackText;\n    for (key in subs) {\n      if (!stackSubs[key]) stackSubs[key] = subs[key];\n    }\n    for (key in stackSubs) {\n      partial.subs[key] = stackSubs[key];\n    }\n\n    stackPartials = stackPartials || {};\n    partial.stackPartials = stackPartials;\n    for (key in partials) {\n      if (!stackPartials[key]) stackPartials[key] = partials[key];\n    }\n    for (key in stackPartials) {\n      partial.partials[key] = stackPartials[key];\n    }\n\n    return partial;\n  }\n\n  var rAmp = /&/g,\n      rLt = /</g,\n      rGt = />/g,\n      rApos = /\\'/g,\n      rQuot = /\\\"/g,\n      hChars = /[&<>\\\"\\']/;\n\n  function coerceToString(val) {\n    return String((val === null || val === undefined) ? '' : val);\n  }\n\n  function hoganEscape(str) {\n    str = coerceToString(str);\n    return hChars.test(str) ?\n      str\n        .replace(rAmp, '&amp;')\n        .replace(rLt, '&lt;')\n        .replace(rGt, '&gt;')\n        .replace(rApos, '&#39;')\n        .replace(rQuot, '&quot;') :\n      str;\n  }\n\n  var isArray = Array.isArray || function(a) {\n    return Object.prototype.toString.call(a) === '[object Array]';\n  };\n\n})(typeof exports !== 'undefined' ? exports : Hogan);\n", "/*\n *  Copyright 2011 Twitter, Inc.\n *  Licensed under the Apache License, Version 2.0 (the \"License\");\n *  you may not use this file except in compliance with the License.\n *  You may obtain a copy of the License at\n *\n *  http://www.apache.org/licenses/LICENSE-2.0\n *\n *  Unless required by applicable law or agreed to in writing, software\n *  distributed under the License is distributed on an \"AS IS\" BASIS,\n *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n *  See the License for the specific language governing permissions and\n *  limitations under the License.\n */\n\n// This file is for use with Node.js. See dist/ for browser files.\n\nvar Hogan = require('./compiler');\nHogan.Template = require('./template').Template;\nHogan.template = Hogan.Template;\nmodule.exports = Hogan;\n", null, null, null, "function Diff() {}\nDiff.prototype = {\n  diff: function diff(oldString, newString) {\n    var _options$timeout;\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var callback = options.callback;\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n    var self = this;\n    function done(value) {\n      value = self.postProcess(value, options);\n      if (callback) {\n        setTimeout(function () {\n          callback(value);\n        }, 0);\n        return true;\n      } else {\n        return value;\n      }\n    }\n\n    // Allow subclasses to massage the input prior to running\n    oldString = this.castInput(oldString, options);\n    newString = this.castInput(newString, options);\n    oldString = this.removeEmpty(this.tokenize(oldString, options));\n    newString = this.removeEmpty(this.tokenize(newString, options));\n    var newLen = newString.length,\n      oldLen = oldString.length;\n    var editLength = 1;\n    var maxEditLength = newLen + oldLen;\n    if (options.maxEditLength != null) {\n      maxEditLength = Math.min(maxEditLength, options.maxEditLength);\n    }\n    var maxExecutionTime = (_options$timeout = options.timeout) !== null && _options$timeout !== void 0 ? _options$timeout : Infinity;\n    var abortAfterTimestamp = Date.now() + maxExecutionTime;\n    var bestPath = [{\n      oldPos: -1,\n      lastComponent: undefined\n    }];\n\n    // Seed editLength = 0, i.e. the content starts with the same values\n    var newPos = this.extractCommon(bestPath[0], newString, oldString, 0, options);\n    if (bestPath[0].oldPos + 1 >= oldLen && newPos + 1 >= newLen) {\n      // Identity per the equality and tokenizer\n      return done(buildValues(self, bestPath[0].lastComponent, newString, oldString, self.useLongestToken));\n    }\n\n    // Once we hit the right edge of the edit graph on some diagonal k, we can\n    // definitely reach the end of the edit graph in no more than k edits, so\n    // there's no point in considering any moves to diagonal k+1 any more (from\n    // which we're guaranteed to need at least k+1 more edits).\n    // Similarly, once we've reached the bottom of the edit graph, there's no\n    // point considering moves to lower diagonals.\n    // We record this fact by setting minDiagonalToConsider and\n    // maxDiagonalToConsider to some finite value once we've hit the edge of\n    // the edit graph.\n    // This optimization is not faithful to the original algorithm presented in\n    // Myers's paper, which instead pointlessly extends D-paths off the end of\n    // the edit graph - see page 7 of Myers's paper which notes this point\n    // explicitly and illustrates it with a diagram. This has major performance\n    // implications for some common scenarios. For instance, to compute a diff\n    // where the new text simply appends d characters on the end of the\n    // original text of length n, the true Myers algorithm will take O(n+d^2)\n    // time while this optimization needs only O(n+d) time.\n    var minDiagonalToConsider = -Infinity,\n      maxDiagonalToConsider = Infinity;\n\n    // Main worker method. checks all permutations of a given edit length for acceptance.\n    function execEditLength() {\n      for (var diagonalPath = Math.max(minDiagonalToConsider, -editLength); diagonalPath <= Math.min(maxDiagonalToConsider, editLength); diagonalPath += 2) {\n        var basePath = void 0;\n        var removePath = bestPath[diagonalPath - 1],\n          addPath = bestPath[diagonalPath + 1];\n        if (removePath) {\n          // No one else is going to attempt to use this value, clear it\n          bestPath[diagonalPath - 1] = undefined;\n        }\n        var canAdd = false;\n        if (addPath) {\n          // what newPos will be after we do an insertion:\n          var addPathNewPos = addPath.oldPos - diagonalPath;\n          canAdd = addPath && 0 <= addPathNewPos && addPathNewPos < newLen;\n        }\n        var canRemove = removePath && removePath.oldPos + 1 < oldLen;\n        if (!canAdd && !canRemove) {\n          // If this path is a terminal then prune\n          bestPath[diagonalPath] = undefined;\n          continue;\n        }\n\n        // Select the diagonal that we want to branch from. We select the prior\n        // path whose position in the old string is the farthest from the origin\n        // and does not pass the bounds of the diff graph\n        if (!canRemove || canAdd && removePath.oldPos < addPath.oldPos) {\n          basePath = self.addToPath(addPath, true, false, 0, options);\n        } else {\n          basePath = self.addToPath(removePath, false, true, 1, options);\n        }\n        newPos = self.extractCommon(basePath, newString, oldString, diagonalPath, options);\n        if (basePath.oldPos + 1 >= oldLen && newPos + 1 >= newLen) {\n          // If we have hit the end of both strings, then we are done\n          return done(buildValues(self, basePath.lastComponent, newString, oldString, self.useLongestToken));\n        } else {\n          bestPath[diagonalPath] = basePath;\n          if (basePath.oldPos + 1 >= oldLen) {\n            maxDiagonalToConsider = Math.min(maxDiagonalToConsider, diagonalPath - 1);\n          }\n          if (newPos + 1 >= newLen) {\n            minDiagonalToConsider = Math.max(minDiagonalToConsider, diagonalPath + 1);\n          }\n        }\n      }\n      editLength++;\n    }\n\n    // Performs the length of edit iteration. Is a bit fugly as this has to support the\n    // sync and async mode which is never fun. Loops over execEditLength until a value\n    // is produced, or until the edit length exceeds options.maxEditLength (if given),\n    // in which case it will return undefined.\n    if (callback) {\n      (function exec() {\n        setTimeout(function () {\n          if (editLength > maxEditLength || Date.now() > abortAfterTimestamp) {\n            return callback();\n          }\n          if (!execEditLength()) {\n            exec();\n          }\n        }, 0);\n      })();\n    } else {\n      while (editLength <= maxEditLength && Date.now() <= abortAfterTimestamp) {\n        var ret = execEditLength();\n        if (ret) {\n          return ret;\n        }\n      }\n    }\n  },\n  addToPath: function addToPath(path, added, removed, oldPosInc, options) {\n    var last = path.lastComponent;\n    if (last && !options.oneChangePerToken && last.added === added && last.removed === removed) {\n      return {\n        oldPos: path.oldPos + oldPosInc,\n        lastComponent: {\n          count: last.count + 1,\n          added: added,\n          removed: removed,\n          previousComponent: last.previousComponent\n        }\n      };\n    } else {\n      return {\n        oldPos: path.oldPos + oldPosInc,\n        lastComponent: {\n          count: 1,\n          added: added,\n          removed: removed,\n          previousComponent: last\n        }\n      };\n    }\n  },\n  extractCommon: function extractCommon(basePath, newString, oldString, diagonalPath, options) {\n    var newLen = newString.length,\n      oldLen = oldString.length,\n      oldPos = basePath.oldPos,\n      newPos = oldPos - diagonalPath,\n      commonCount = 0;\n    while (newPos + 1 < newLen && oldPos + 1 < oldLen && this.equals(oldString[oldPos + 1], newString[newPos + 1], options)) {\n      newPos++;\n      oldPos++;\n      commonCount++;\n      if (options.oneChangePerToken) {\n        basePath.lastComponent = {\n          count: 1,\n          previousComponent: basePath.lastComponent,\n          added: false,\n          removed: false\n        };\n      }\n    }\n    if (commonCount && !options.oneChangePerToken) {\n      basePath.lastComponent = {\n        count: commonCount,\n        previousComponent: basePath.lastComponent,\n        added: false,\n        removed: false\n      };\n    }\n    basePath.oldPos = oldPos;\n    return newPos;\n  },\n  equals: function equals(left, right, options) {\n    if (options.comparator) {\n      return options.comparator(left, right);\n    } else {\n      return left === right || options.ignoreCase && left.toLowerCase() === right.toLowerCase();\n    }\n  },\n  removeEmpty: function removeEmpty(array) {\n    var ret = [];\n    for (var i = 0; i < array.length; i++) {\n      if (array[i]) {\n        ret.push(array[i]);\n      }\n    }\n    return ret;\n  },\n  castInput: function castInput(value) {\n    return value;\n  },\n  tokenize: function tokenize(value) {\n    return Array.from(value);\n  },\n  join: function join(chars) {\n    return chars.join('');\n  },\n  postProcess: function postProcess(changeObjects) {\n    return changeObjects;\n  }\n};\nfunction buildValues(diff, lastComponent, newString, oldString, useLongestToken) {\n  // First we convert our linked list of components in reverse order to an\n  // array in the right order:\n  var components = [];\n  var nextComponent;\n  while (lastComponent) {\n    components.push(lastComponent);\n    nextComponent = lastComponent.previousComponent;\n    delete lastComponent.previousComponent;\n    lastComponent = nextComponent;\n  }\n  components.reverse();\n  var componentPos = 0,\n    componentLen = components.length,\n    newPos = 0,\n    oldPos = 0;\n  for (; componentPos < componentLen; componentPos++) {\n    var component = components[componentPos];\n    if (!component.removed) {\n      if (!component.added && useLongestToken) {\n        var value = newString.slice(newPos, newPos + component.count);\n        value = value.map(function (value, i) {\n          var oldValue = oldString[oldPos + i];\n          return oldValue.length > value.length ? oldValue : value;\n        });\n        component.value = diff.join(value);\n      } else {\n        component.value = diff.join(newString.slice(newPos, newPos + component.count));\n      }\n      newPos += component.count;\n\n      // Common case\n      if (!component.added) {\n        oldPos += component.count;\n      }\n    } else {\n      component.value = diff.join(oldString.slice(oldPos, oldPos + component.count));\n      oldPos += component.count;\n    }\n  }\n  return components;\n}\n\nvar characterDiff = new Diff();\nfunction diffChars(oldStr, newStr, options) {\n  return characterDiff.diff(oldStr, newStr, options);\n}\n\nfunction longestCommonPrefix(str1, str2) {\n  var i;\n  for (i = 0; i < str1.length && i < str2.length; i++) {\n    if (str1[i] != str2[i]) {\n      return str1.slice(0, i);\n    }\n  }\n  return str1.slice(0, i);\n}\nfunction longestCommonSuffix(str1, str2) {\n  var i;\n\n  // Unlike longestCommonPrefix, we need a special case to handle all scenarios\n  // where we return the empty string since str1.slice(-0) will return the\n  // entire string.\n  if (!str1 || !str2 || str1[str1.length - 1] != str2[str2.length - 1]) {\n    return '';\n  }\n  for (i = 0; i < str1.length && i < str2.length; i++) {\n    if (str1[str1.length - (i + 1)] != str2[str2.length - (i + 1)]) {\n      return str1.slice(-i);\n    }\n  }\n  return str1.slice(-i);\n}\nfunction replacePrefix(string, oldPrefix, newPrefix) {\n  if (string.slice(0, oldPrefix.length) != oldPrefix) {\n    throw Error(\"string \".concat(JSON.stringify(string), \" doesn't start with prefix \").concat(JSON.stringify(oldPrefix), \"; this is a bug\"));\n  }\n  return newPrefix + string.slice(oldPrefix.length);\n}\nfunction replaceSuffix(string, oldSuffix, newSuffix) {\n  if (!oldSuffix) {\n    return string + newSuffix;\n  }\n  if (string.slice(-oldSuffix.length) != oldSuffix) {\n    throw Error(\"string \".concat(JSON.stringify(string), \" doesn't end with suffix \").concat(JSON.stringify(oldSuffix), \"; this is a bug\"));\n  }\n  return string.slice(0, -oldSuffix.length) + newSuffix;\n}\nfunction removePrefix(string, oldPrefix) {\n  return replacePrefix(string, oldPrefix, '');\n}\nfunction removeSuffix(string, oldSuffix) {\n  return replaceSuffix(string, oldSuffix, '');\n}\nfunction maximumOverlap(string1, string2) {\n  return string2.slice(0, overlapCount(string1, string2));\n}\n\n// Nicked from https://stackoverflow.com/a/60422853/1709587\nfunction overlapCount(a, b) {\n  // Deal with cases where the strings differ in length\n  var startA = 0;\n  if (a.length > b.length) {\n    startA = a.length - b.length;\n  }\n  var endB = b.length;\n  if (a.length < b.length) {\n    endB = a.length;\n  }\n  // Create a back-reference for each index\n  //   that should be followed in case of a mismatch.\n  //   We only need B to make these references:\n  var map = Array(endB);\n  var k = 0; // Index that lags behind j\n  map[0] = 0;\n  for (var j = 1; j < endB; j++) {\n    if (b[j] == b[k]) {\n      map[j] = map[k]; // skip over the same character (optional optimisation)\n    } else {\n      map[j] = k;\n    }\n    while (k > 0 && b[j] != b[k]) {\n      k = map[k];\n    }\n    if (b[j] == b[k]) {\n      k++;\n    }\n  }\n  // Phase 2: use these references while iterating over A\n  k = 0;\n  for (var i = startA; i < a.length; i++) {\n    while (k > 0 && a[i] != b[k]) {\n      k = map[k];\n    }\n    if (a[i] == b[k]) {\n      k++;\n    }\n  }\n  return k;\n}\n\n/**\n * Returns true if the string consistently uses Windows line endings.\n */\nfunction hasOnlyWinLineEndings(string) {\n  return string.includes('\\r\\n') && !string.startsWith('\\n') && !string.match(/[^\\r]\\n/);\n}\n\n/**\n * Returns true if the string consistently uses Unix line endings.\n */\nfunction hasOnlyUnixLineEndings(string) {\n  return !string.includes('\\r\\n') && string.includes('\\n');\n}\n\n// Based on https://en.wikipedia.org/wiki/Latin_script_in_Unicode\n//\n// Ranges and exceptions:\n// Latin-1 Supplement, 0080–00FF\n//  - U+00D7  × Multiplication sign\n//  - U+00F7  ÷ Division sign\n// Latin Extended-A, 0100–017F\n// Latin Extended-B, 0180–024F\n// IPA Extensions, 0250–02AF\n// Spacing Modifier Letters, 02B0–02FF\n//  - U+02C7  ˇ &#711;  Caron\n//  - U+02D8  ˘ &#728;  Breve\n//  - U+02D9  ˙ &#729;  Dot Above\n//  - U+02DA  ˚ &#730;  Ring Above\n//  - U+02DB  ˛ &#731;  Ogonek\n//  - U+02DC  ˜ &#732;  Small Tilde\n//  - U+02DD  ˝ &#733;  Double Acute Accent\n// Latin Extended Additional, 1E00–1EFF\nvar extendedWordChars = \"a-zA-Z0-9_\\\\u{C0}-\\\\u{FF}\\\\u{D8}-\\\\u{F6}\\\\u{F8}-\\\\u{2C6}\\\\u{2C8}-\\\\u{2D7}\\\\u{2DE}-\\\\u{2FF}\\\\u{1E00}-\\\\u{1EFF}\";\n\n// Each token is one of the following:\n// - A punctuation mark plus the surrounding whitespace\n// - A word plus the surrounding whitespace\n// - Pure whitespace (but only in the special case where this the entire text\n//   is just whitespace)\n//\n// We have to include surrounding whitespace in the tokens because the two\n// alternative approaches produce horribly broken results:\n// * If we just discard the whitespace, we can't fully reproduce the original\n//   text from the sequence of tokens and any attempt to render the diff will\n//   get the whitespace wrong.\n// * If we have separate tokens for whitespace, then in a typical text every\n//   second token will be a single space character. But this often results in\n//   the optimal diff between two texts being a perverse one that preserves\n//   the spaces between words but deletes and reinserts actual common words.\n//   See https://github.com/kpdecker/jsdiff/issues/160#issuecomment-1866099640\n//   for an example.\n//\n// Keeping the surrounding whitespace of course has implications for .equals\n// and .join, not just .tokenize.\n\n// This regex does NOT fully implement the tokenization rules described above.\n// Instead, it gives runs of whitespace their own \"token\". The tokenize method\n// then handles stitching whitespace tokens onto adjacent word or punctuation\n// tokens.\nvar tokenizeIncludingWhitespace = new RegExp(\"[\".concat(extendedWordChars, \"]+|\\\\s+|[^\").concat(extendedWordChars, \"]\"), 'ug');\nvar wordDiff = new Diff();\nwordDiff.equals = function (left, right, options) {\n  if (options.ignoreCase) {\n    left = left.toLowerCase();\n    right = right.toLowerCase();\n  }\n  return left.trim() === right.trim();\n};\nwordDiff.tokenize = function (value) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var parts;\n  if (options.intlSegmenter) {\n    if (options.intlSegmenter.resolvedOptions().granularity != 'word') {\n      throw new Error('The segmenter passed must have a granularity of \"word\"');\n    }\n    parts = Array.from(options.intlSegmenter.segment(value), function (segment) {\n      return segment.segment;\n    });\n  } else {\n    parts = value.match(tokenizeIncludingWhitespace) || [];\n  }\n  var tokens = [];\n  var prevPart = null;\n  parts.forEach(function (part) {\n    if (/\\s/.test(part)) {\n      if (prevPart == null) {\n        tokens.push(part);\n      } else {\n        tokens.push(tokens.pop() + part);\n      }\n    } else if (/\\s/.test(prevPart)) {\n      if (tokens[tokens.length - 1] == prevPart) {\n        tokens.push(tokens.pop() + part);\n      } else {\n        tokens.push(prevPart + part);\n      }\n    } else {\n      tokens.push(part);\n    }\n    prevPart = part;\n  });\n  return tokens;\n};\nwordDiff.join = function (tokens) {\n  // Tokens being joined here will always have appeared consecutively in the\n  // same text, so we can simply strip off the leading whitespace from all the\n  // tokens except the first (and except any whitespace-only tokens - but such\n  // a token will always be the first and only token anyway) and then join them\n  // and the whitespace around words and punctuation will end up correct.\n  return tokens.map(function (token, i) {\n    if (i == 0) {\n      return token;\n    } else {\n      return token.replace(/^\\s+/, '');\n    }\n  }).join('');\n};\nwordDiff.postProcess = function (changes, options) {\n  if (!changes || options.oneChangePerToken) {\n    return changes;\n  }\n  var lastKeep = null;\n  // Change objects representing any insertion or deletion since the last\n  // \"keep\" change object. There can be at most one of each.\n  var insertion = null;\n  var deletion = null;\n  changes.forEach(function (change) {\n    if (change.added) {\n      insertion = change;\n    } else if (change.removed) {\n      deletion = change;\n    } else {\n      if (insertion || deletion) {\n        // May be false at start of text\n        dedupeWhitespaceInChangeObjects(lastKeep, deletion, insertion, change);\n      }\n      lastKeep = change;\n      insertion = null;\n      deletion = null;\n    }\n  });\n  if (insertion || deletion) {\n    dedupeWhitespaceInChangeObjects(lastKeep, deletion, insertion, null);\n  }\n  return changes;\n};\nfunction diffWords(oldStr, newStr, options) {\n  // This option has never been documented and never will be (it's clearer to\n  // just call `diffWordsWithSpace` directly if you need that behavior), but\n  // has existed in jsdiff for a long time, so we retain support for it here\n  // for the sake of backwards compatibility.\n  if ((options === null || options === void 0 ? void 0 : options.ignoreWhitespace) != null && !options.ignoreWhitespace) {\n    return diffWordsWithSpace(oldStr, newStr, options);\n  }\n  return wordDiff.diff(oldStr, newStr, options);\n}\nfunction dedupeWhitespaceInChangeObjects(startKeep, deletion, insertion, endKeep) {\n  // Before returning, we tidy up the leading and trailing whitespace of the\n  // change objects to eliminate cases where trailing whitespace in one object\n  // is repeated as leading whitespace in the next.\n  // Below are examples of the outcomes we want here to explain the code.\n  // I=insert, K=keep, D=delete\n  // 1. diffing 'foo bar baz' vs 'foo baz'\n  //    Prior to cleanup, we have K:'foo ' D:' bar ' K:' baz'\n  //    After cleanup, we want:   K:'foo ' D:'bar ' K:'baz'\n  //\n  // 2. Diffing 'foo bar baz' vs 'foo qux baz'\n  //    Prior to cleanup, we have K:'foo ' D:' bar ' I:' qux ' K:' baz'\n  //    After cleanup, we want K:'foo ' D:'bar' I:'qux' K:' baz'\n  //\n  // 3. Diffing 'foo\\nbar baz' vs 'foo baz'\n  //    Prior to cleanup, we have K:'foo ' D:'\\nbar ' K:' baz'\n  //    After cleanup, we want K'foo' D:'\\nbar' K:' baz'\n  //\n  // 4. Diffing 'foo baz' vs 'foo\\nbar baz'\n  //    Prior to cleanup, we have K:'foo\\n' I:'\\nbar ' K:' baz'\n  //    After cleanup, we ideally want K'foo' I:'\\nbar' K:' baz'\n  //    but don't actually manage this currently (the pre-cleanup change\n  //    objects don't contain enough information to make it possible).\n  //\n  // 5. Diffing 'foo   bar baz' vs 'foo  baz'\n  //    Prior to cleanup, we have K:'foo  ' D:'   bar ' K:'  baz'\n  //    After cleanup, we want K:'foo  ' D:' bar ' K:'baz'\n  //\n  // Our handling is unavoidably imperfect in the case where there's a single\n  // indel between keeps and the whitespace has changed. For instance, consider\n  // diffing 'foo\\tbar\\nbaz' vs 'foo baz'. Unless we create an extra change\n  // object to represent the insertion of the space character (which isn't even\n  // a token), we have no way to avoid losing information about the texts'\n  // original whitespace in the result we return. Still, we do our best to\n  // output something that will look sensible if we e.g. print it with\n  // insertions in green and deletions in red.\n\n  // Between two \"keep\" change objects (or before the first or after the last\n  // change object), we can have either:\n  // * A \"delete\" followed by an \"insert\"\n  // * Just an \"insert\"\n  // * Just a \"delete\"\n  // We handle the three cases separately.\n  if (deletion && insertion) {\n    var oldWsPrefix = deletion.value.match(/^\\s*/)[0];\n    var oldWsSuffix = deletion.value.match(/\\s*$/)[0];\n    var newWsPrefix = insertion.value.match(/^\\s*/)[0];\n    var newWsSuffix = insertion.value.match(/\\s*$/)[0];\n    if (startKeep) {\n      var commonWsPrefix = longestCommonPrefix(oldWsPrefix, newWsPrefix);\n      startKeep.value = replaceSuffix(startKeep.value, newWsPrefix, commonWsPrefix);\n      deletion.value = removePrefix(deletion.value, commonWsPrefix);\n      insertion.value = removePrefix(insertion.value, commonWsPrefix);\n    }\n    if (endKeep) {\n      var commonWsSuffix = longestCommonSuffix(oldWsSuffix, newWsSuffix);\n      endKeep.value = replacePrefix(endKeep.value, newWsSuffix, commonWsSuffix);\n      deletion.value = removeSuffix(deletion.value, commonWsSuffix);\n      insertion.value = removeSuffix(insertion.value, commonWsSuffix);\n    }\n  } else if (insertion) {\n    // The whitespaces all reflect what was in the new text rather than\n    // the old, so we essentially have no information about whitespace\n    // insertion or deletion. We just want to dedupe the whitespace.\n    // We do that by having each change object keep its trailing\n    // whitespace and deleting duplicate leading whitespace where\n    // present.\n    if (startKeep) {\n      insertion.value = insertion.value.replace(/^\\s*/, '');\n    }\n    if (endKeep) {\n      endKeep.value = endKeep.value.replace(/^\\s*/, '');\n    }\n    // otherwise we've got a deletion and no insertion\n  } else if (startKeep && endKeep) {\n    var newWsFull = endKeep.value.match(/^\\s*/)[0],\n      delWsStart = deletion.value.match(/^\\s*/)[0],\n      delWsEnd = deletion.value.match(/\\s*$/)[0];\n\n    // Any whitespace that comes straight after startKeep in both the old and\n    // new texts, assign to startKeep and remove from the deletion.\n    var newWsStart = longestCommonPrefix(newWsFull, delWsStart);\n    deletion.value = removePrefix(deletion.value, newWsStart);\n\n    // Any whitespace that comes straight before endKeep in both the old and\n    // new texts, and hasn't already been assigned to startKeep, assign to\n    // endKeep and remove from the deletion.\n    var newWsEnd = longestCommonSuffix(removePrefix(newWsFull, newWsStart), delWsEnd);\n    deletion.value = removeSuffix(deletion.value, newWsEnd);\n    endKeep.value = replacePrefix(endKeep.value, newWsFull, newWsEnd);\n\n    // If there's any whitespace from the new text that HASN'T already been\n    // assigned, assign it to the start:\n    startKeep.value = replaceSuffix(startKeep.value, newWsFull, newWsFull.slice(0, newWsFull.length - newWsEnd.length));\n  } else if (endKeep) {\n    // We are at the start of the text. Preserve all the whitespace on\n    // endKeep, and just remove whitespace from the end of deletion to the\n    // extent that it overlaps with the start of endKeep.\n    var endKeepWsPrefix = endKeep.value.match(/^\\s*/)[0];\n    var deletionWsSuffix = deletion.value.match(/\\s*$/)[0];\n    var overlap = maximumOverlap(deletionWsSuffix, endKeepWsPrefix);\n    deletion.value = removeSuffix(deletion.value, overlap);\n  } else if (startKeep) {\n    // We are at the END of the text. Preserve all the whitespace on\n    // startKeep, and just remove whitespace from the start of deletion to\n    // the extent that it overlaps with the end of startKeep.\n    var startKeepWsSuffix = startKeep.value.match(/\\s*$/)[0];\n    var deletionWsPrefix = deletion.value.match(/^\\s*/)[0];\n    var _overlap = maximumOverlap(startKeepWsSuffix, deletionWsPrefix);\n    deletion.value = removePrefix(deletion.value, _overlap);\n  }\n}\nvar wordWithSpaceDiff = new Diff();\nwordWithSpaceDiff.tokenize = function (value) {\n  // Slightly different to the tokenizeIncludingWhitespace regex used above in\n  // that this one treats each individual newline as a distinct tokens, rather\n  // than merging them into other surrounding whitespace. This was requested\n  // in https://github.com/kpdecker/jsdiff/issues/180 &\n  //    https://github.com/kpdecker/jsdiff/issues/211\n  var regex = new RegExp(\"(\\\\r?\\\\n)|[\".concat(extendedWordChars, \"]+|[^\\\\S\\\\n\\\\r]+|[^\").concat(extendedWordChars, \"]\"), 'ug');\n  return value.match(regex) || [];\n};\nfunction diffWordsWithSpace(oldStr, newStr, options) {\n  return wordWithSpaceDiff.diff(oldStr, newStr, options);\n}\n\nfunction generateOptions(options, defaults) {\n  if (typeof options === 'function') {\n    defaults.callback = options;\n  } else if (options) {\n    for (var name in options) {\n      /* istanbul ignore else */\n      if (options.hasOwnProperty(name)) {\n        defaults[name] = options[name];\n      }\n    }\n  }\n  return defaults;\n}\n\nvar lineDiff = new Diff();\nlineDiff.tokenize = function (value, options) {\n  if (options.stripTrailingCr) {\n    // remove one \\r before \\n to match GNU diff's --strip-trailing-cr behavior\n    value = value.replace(/\\r\\n/g, '\\n');\n  }\n  var retLines = [],\n    linesAndNewlines = value.split(/(\\n|\\r\\n)/);\n\n  // Ignore the final empty token that occurs if the string ends with a new line\n  if (!linesAndNewlines[linesAndNewlines.length - 1]) {\n    linesAndNewlines.pop();\n  }\n\n  // Merge the content and line separators into single tokens\n  for (var i = 0; i < linesAndNewlines.length; i++) {\n    var line = linesAndNewlines[i];\n    if (i % 2 && !options.newlineIsToken) {\n      retLines[retLines.length - 1] += line;\n    } else {\n      retLines.push(line);\n    }\n  }\n  return retLines;\n};\nlineDiff.equals = function (left, right, options) {\n  // If we're ignoring whitespace, we need to normalise lines by stripping\n  // whitespace before checking equality. (This has an annoying interaction\n  // with newlineIsToken that requires special handling: if newlines get their\n  // own token, then we DON'T want to trim the *newline* tokens down to empty\n  // strings, since this would cause us to treat whitespace-only line content\n  // as equal to a separator between lines, which would be weird and\n  // inconsistent with the documented behavior of the options.)\n  if (options.ignoreWhitespace) {\n    if (!options.newlineIsToken || !left.includes('\\n')) {\n      left = left.trim();\n    }\n    if (!options.newlineIsToken || !right.includes('\\n')) {\n      right = right.trim();\n    }\n  } else if (options.ignoreNewlineAtEof && !options.newlineIsToken) {\n    if (left.endsWith('\\n')) {\n      left = left.slice(0, -1);\n    }\n    if (right.endsWith('\\n')) {\n      right = right.slice(0, -1);\n    }\n  }\n  return Diff.prototype.equals.call(this, left, right, options);\n};\nfunction diffLines(oldStr, newStr, callback) {\n  return lineDiff.diff(oldStr, newStr, callback);\n}\n\n// Kept for backwards compatibility. This is a rather arbitrary wrapper method\n// that just calls `diffLines` with `ignoreWhitespace: true`. It's confusing to\n// have two ways to do exactly the same thing in the API, so we no longer\n// document this one (library users should explicitly use `diffLines` with\n// `ignoreWhitespace: true` instead) but we keep it around to maintain\n// compatibility with code that used old versions.\nfunction diffTrimmedLines(oldStr, newStr, callback) {\n  var options = generateOptions(callback, {\n    ignoreWhitespace: true\n  });\n  return lineDiff.diff(oldStr, newStr, options);\n}\n\nvar sentenceDiff = new Diff();\nsentenceDiff.tokenize = function (value) {\n  return value.split(/(\\S.+?[.!?])(?=\\s+|$)/);\n};\nfunction diffSentences(oldStr, newStr, callback) {\n  return sentenceDiff.diff(oldStr, newStr, callback);\n}\n\nvar cssDiff = new Diff();\ncssDiff.tokenize = function (value) {\n  return value.split(/([{}:;,]|\\s+)/);\n};\nfunction diffCss(oldStr, newStr, callback) {\n  return cssDiff.diff(oldStr, newStr, callback);\n}\n\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar jsonDiff = new Diff();\n// Discriminate between two lines of pretty-printed, serialized JSON where one of them has a\n// dangling comma and the other doesn't. Turns out including the dangling comma yields the nicest output:\njsonDiff.useLongestToken = true;\njsonDiff.tokenize = lineDiff.tokenize;\njsonDiff.castInput = function (value, options) {\n  var undefinedReplacement = options.undefinedReplacement,\n    _options$stringifyRep = options.stringifyReplacer,\n    stringifyReplacer = _options$stringifyRep === void 0 ? function (k, v) {\n      return typeof v === 'undefined' ? undefinedReplacement : v;\n    } : _options$stringifyRep;\n  return typeof value === 'string' ? value : JSON.stringify(canonicalize(value, null, null, stringifyReplacer), stringifyReplacer, '  ');\n};\njsonDiff.equals = function (left, right, options) {\n  return Diff.prototype.equals.call(jsonDiff, left.replace(/,([\\r\\n])/g, '$1'), right.replace(/,([\\r\\n])/g, '$1'), options);\n};\nfunction diffJson(oldObj, newObj, options) {\n  return jsonDiff.diff(oldObj, newObj, options);\n}\n\n// This function handles the presence of circular references by bailing out when encountering an\n// object that is already on the \"stack\" of items being processed. Accepts an optional replacer\nfunction canonicalize(obj, stack, replacementStack, replacer, key) {\n  stack = stack || [];\n  replacementStack = replacementStack || [];\n  if (replacer) {\n    obj = replacer(key, obj);\n  }\n  var i;\n  for (i = 0; i < stack.length; i += 1) {\n    if (stack[i] === obj) {\n      return replacementStack[i];\n    }\n  }\n  var canonicalizedObj;\n  if ('[object Array]' === Object.prototype.toString.call(obj)) {\n    stack.push(obj);\n    canonicalizedObj = new Array(obj.length);\n    replacementStack.push(canonicalizedObj);\n    for (i = 0; i < obj.length; i += 1) {\n      canonicalizedObj[i] = canonicalize(obj[i], stack, replacementStack, replacer, key);\n    }\n    stack.pop();\n    replacementStack.pop();\n    return canonicalizedObj;\n  }\n  if (obj && obj.toJSON) {\n    obj = obj.toJSON();\n  }\n  if (_typeof(obj) === 'object' && obj !== null) {\n    stack.push(obj);\n    canonicalizedObj = {};\n    replacementStack.push(canonicalizedObj);\n    var sortedKeys = [],\n      _key;\n    for (_key in obj) {\n      /* istanbul ignore else */\n      if (Object.prototype.hasOwnProperty.call(obj, _key)) {\n        sortedKeys.push(_key);\n      }\n    }\n    sortedKeys.sort();\n    for (i = 0; i < sortedKeys.length; i += 1) {\n      _key = sortedKeys[i];\n      canonicalizedObj[_key] = canonicalize(obj[_key], stack, replacementStack, replacer, _key);\n    }\n    stack.pop();\n    replacementStack.pop();\n  } else {\n    canonicalizedObj = obj;\n  }\n  return canonicalizedObj;\n}\n\nvar arrayDiff = new Diff();\narrayDiff.tokenize = function (value) {\n  return value.slice();\n};\narrayDiff.join = arrayDiff.removeEmpty = function (value) {\n  return value;\n};\nfunction diffArrays(oldArr, newArr, callback) {\n  return arrayDiff.diff(oldArr, newArr, callback);\n}\n\nfunction unixToWin(patch) {\n  if (Array.isArray(patch)) {\n    return patch.map(unixToWin);\n  }\n  return _objectSpread2(_objectSpread2({}, patch), {}, {\n    hunks: patch.hunks.map(function (hunk) {\n      return _objectSpread2(_objectSpread2({}, hunk), {}, {\n        lines: hunk.lines.map(function (line, i) {\n          var _hunk$lines;\n          return line.startsWith('\\\\') || line.endsWith('\\r') || (_hunk$lines = hunk.lines[i + 1]) !== null && _hunk$lines !== void 0 && _hunk$lines.startsWith('\\\\') ? line : line + '\\r';\n        })\n      });\n    })\n  });\n}\nfunction winToUnix(patch) {\n  if (Array.isArray(patch)) {\n    return patch.map(winToUnix);\n  }\n  return _objectSpread2(_objectSpread2({}, patch), {}, {\n    hunks: patch.hunks.map(function (hunk) {\n      return _objectSpread2(_objectSpread2({}, hunk), {}, {\n        lines: hunk.lines.map(function (line) {\n          return line.endsWith('\\r') ? line.substring(0, line.length - 1) : line;\n        })\n      });\n    })\n  });\n}\n\n/**\n * Returns true if the patch consistently uses Unix line endings (or only involves one line and has\n * no line endings).\n */\nfunction isUnix(patch) {\n  if (!Array.isArray(patch)) {\n    patch = [patch];\n  }\n  return !patch.some(function (index) {\n    return index.hunks.some(function (hunk) {\n      return hunk.lines.some(function (line) {\n        return !line.startsWith('\\\\') && line.endsWith('\\r');\n      });\n    });\n  });\n}\n\n/**\n * Returns true if the patch uses Windows line endings and only Windows line endings.\n */\nfunction isWin(patch) {\n  if (!Array.isArray(patch)) {\n    patch = [patch];\n  }\n  return patch.some(function (index) {\n    return index.hunks.some(function (hunk) {\n      return hunk.lines.some(function (line) {\n        return line.endsWith('\\r');\n      });\n    });\n  }) && patch.every(function (index) {\n    return index.hunks.every(function (hunk) {\n      return hunk.lines.every(function (line, i) {\n        var _hunk$lines2;\n        return line.startsWith('\\\\') || line.endsWith('\\r') || ((_hunk$lines2 = hunk.lines[i + 1]) === null || _hunk$lines2 === void 0 ? void 0 : _hunk$lines2.startsWith('\\\\'));\n      });\n    });\n  });\n}\n\nfunction parsePatch(uniDiff) {\n  var diffstr = uniDiff.split(/\\n/),\n    list = [],\n    i = 0;\n  function parseIndex() {\n    var index = {};\n    list.push(index);\n\n    // Parse diff metadata\n    while (i < diffstr.length) {\n      var line = diffstr[i];\n\n      // File header found, end parsing diff metadata\n      if (/^(\\-\\-\\-|\\+\\+\\+|@@)\\s/.test(line)) {\n        break;\n      }\n\n      // Diff index\n      var header = /^(?:Index:|diff(?: -r \\w+)+)\\s+(.+?)\\s*$/.exec(line);\n      if (header) {\n        index.index = header[1];\n      }\n      i++;\n    }\n\n    // Parse file headers if they are defined. Unified diff requires them, but\n    // there's no technical issues to have an isolated hunk without file header\n    parseFileHeader(index);\n    parseFileHeader(index);\n\n    // Parse hunks\n    index.hunks = [];\n    while (i < diffstr.length) {\n      var _line = diffstr[i];\n      if (/^(Index:\\s|diff\\s|\\-\\-\\-\\s|\\+\\+\\+\\s|===================================================================)/.test(_line)) {\n        break;\n      } else if (/^@@/.test(_line)) {\n        index.hunks.push(parseHunk());\n      } else if (_line) {\n        throw new Error('Unknown line ' + (i + 1) + ' ' + JSON.stringify(_line));\n      } else {\n        i++;\n      }\n    }\n  }\n\n  // Parses the --- and +++ headers, if none are found, no lines\n  // are consumed.\n  function parseFileHeader(index) {\n    var fileHeader = /^(---|\\+\\+\\+)\\s+(.*)\\r?$/.exec(diffstr[i]);\n    if (fileHeader) {\n      var keyPrefix = fileHeader[1] === '---' ? 'old' : 'new';\n      var data = fileHeader[2].split('\\t', 2);\n      var fileName = data[0].replace(/\\\\\\\\/g, '\\\\');\n      if (/^\".*\"$/.test(fileName)) {\n        fileName = fileName.substr(1, fileName.length - 2);\n      }\n      index[keyPrefix + 'FileName'] = fileName;\n      index[keyPrefix + 'Header'] = (data[1] || '').trim();\n      i++;\n    }\n  }\n\n  // Parses a hunk\n  // This assumes that we are at the start of a hunk.\n  function parseHunk() {\n    var chunkHeaderIndex = i,\n      chunkHeaderLine = diffstr[i++],\n      chunkHeader = chunkHeaderLine.split(/@@ -(\\d+)(?:,(\\d+))? \\+(\\d+)(?:,(\\d+))? @@/);\n    var hunk = {\n      oldStart: +chunkHeader[1],\n      oldLines: typeof chunkHeader[2] === 'undefined' ? 1 : +chunkHeader[2],\n      newStart: +chunkHeader[3],\n      newLines: typeof chunkHeader[4] === 'undefined' ? 1 : +chunkHeader[4],\n      lines: []\n    };\n\n    // Unified Diff Format quirk: If the chunk size is 0,\n    // the first number is one lower than one would expect.\n    // https://www.artima.com/weblogs/viewpost.jsp?thread=164293\n    if (hunk.oldLines === 0) {\n      hunk.oldStart += 1;\n    }\n    if (hunk.newLines === 0) {\n      hunk.newStart += 1;\n    }\n    var addCount = 0,\n      removeCount = 0;\n    for (; i < diffstr.length && (removeCount < hunk.oldLines || addCount < hunk.newLines || (_diffstr$i = diffstr[i]) !== null && _diffstr$i !== void 0 && _diffstr$i.startsWith('\\\\')); i++) {\n      var _diffstr$i;\n      var operation = diffstr[i].length == 0 && i != diffstr.length - 1 ? ' ' : diffstr[i][0];\n      if (operation === '+' || operation === '-' || operation === ' ' || operation === '\\\\') {\n        hunk.lines.push(diffstr[i]);\n        if (operation === '+') {\n          addCount++;\n        } else if (operation === '-') {\n          removeCount++;\n        } else if (operation === ' ') {\n          addCount++;\n          removeCount++;\n        }\n      } else {\n        throw new Error(\"Hunk at line \".concat(chunkHeaderIndex + 1, \" contained invalid line \").concat(diffstr[i]));\n      }\n    }\n\n    // Handle the empty block count case\n    if (!addCount && hunk.newLines === 1) {\n      hunk.newLines = 0;\n    }\n    if (!removeCount && hunk.oldLines === 1) {\n      hunk.oldLines = 0;\n    }\n\n    // Perform sanity checking\n    if (addCount !== hunk.newLines) {\n      throw new Error('Added line count did not match for hunk at line ' + (chunkHeaderIndex + 1));\n    }\n    if (removeCount !== hunk.oldLines) {\n      throw new Error('Removed line count did not match for hunk at line ' + (chunkHeaderIndex + 1));\n    }\n    return hunk;\n  }\n  while (i < diffstr.length) {\n    parseIndex();\n  }\n  return list;\n}\n\n// Iterator that traverses in the range of [min, max], stepping\n// by distance from a given start position. I.e. for [0, 4], with\n// start of 2, this will iterate 2, 3, 1, 4, 0.\nfunction distanceIterator (start, minLine, maxLine) {\n  var wantForward = true,\n    backwardExhausted = false,\n    forwardExhausted = false,\n    localOffset = 1;\n  return function iterator() {\n    if (wantForward && !forwardExhausted) {\n      if (backwardExhausted) {\n        localOffset++;\n      } else {\n        wantForward = false;\n      }\n\n      // Check if trying to fit beyond text length, and if not, check it fits\n      // after offset location (or desired location on first iteration)\n      if (start + localOffset <= maxLine) {\n        return start + localOffset;\n      }\n      forwardExhausted = true;\n    }\n    if (!backwardExhausted) {\n      if (!forwardExhausted) {\n        wantForward = true;\n      }\n\n      // Check if trying to fit before text beginning, and if not, check it fits\n      // before offset location\n      if (minLine <= start - localOffset) {\n        return start - localOffset++;\n      }\n      backwardExhausted = true;\n      return iterator();\n    }\n\n    // We tried to fit hunk before text beginning and beyond text length, then\n    // hunk can't fit on the text. Return undefined\n  };\n}\n\nfunction applyPatch(source, uniDiff) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  if (typeof uniDiff === 'string') {\n    uniDiff = parsePatch(uniDiff);\n  }\n  if (Array.isArray(uniDiff)) {\n    if (uniDiff.length > 1) {\n      throw new Error('applyPatch only works with a single input.');\n    }\n    uniDiff = uniDiff[0];\n  }\n  if (options.autoConvertLineEndings || options.autoConvertLineEndings == null) {\n    if (hasOnlyWinLineEndings(source) && isUnix(uniDiff)) {\n      uniDiff = unixToWin(uniDiff);\n    } else if (hasOnlyUnixLineEndings(source) && isWin(uniDiff)) {\n      uniDiff = winToUnix(uniDiff);\n    }\n  }\n\n  // Apply the diff to the input\n  var lines = source.split('\\n'),\n    hunks = uniDiff.hunks,\n    compareLine = options.compareLine || function (lineNumber, line, operation, patchContent) {\n      return line === patchContent;\n    },\n    fuzzFactor = options.fuzzFactor || 0,\n    minLine = 0;\n  if (fuzzFactor < 0 || !Number.isInteger(fuzzFactor)) {\n    throw new Error('fuzzFactor must be a non-negative integer');\n  }\n\n  // Special case for empty patch.\n  if (!hunks.length) {\n    return source;\n  }\n\n  // Before anything else, handle EOFNL insertion/removal. If the patch tells us to make a change\n  // to the EOFNL that is redundant/impossible - i.e. to remove a newline that's not there, or add a\n  // newline that already exists - then we either return false and fail to apply the patch (if\n  // fuzzFactor is 0) or simply ignore the problem and do nothing (if fuzzFactor is >0).\n  // If we do need to remove/add a newline at EOF, this will always be in the final hunk:\n  var prevLine = '',\n    removeEOFNL = false,\n    addEOFNL = false;\n  for (var i = 0; i < hunks[hunks.length - 1].lines.length; i++) {\n    var line = hunks[hunks.length - 1].lines[i];\n    if (line[0] == '\\\\') {\n      if (prevLine[0] == '+') {\n        removeEOFNL = true;\n      } else if (prevLine[0] == '-') {\n        addEOFNL = true;\n      }\n    }\n    prevLine = line;\n  }\n  if (removeEOFNL) {\n    if (addEOFNL) {\n      // This means the final line gets changed but doesn't have a trailing newline in either the\n      // original or patched version. In that case, we do nothing if fuzzFactor > 0, and if\n      // fuzzFactor is 0, we simply validate that the source file has no trailing newline.\n      if (!fuzzFactor && lines[lines.length - 1] == '') {\n        return false;\n      }\n    } else if (lines[lines.length - 1] == '') {\n      lines.pop();\n    } else if (!fuzzFactor) {\n      return false;\n    }\n  } else if (addEOFNL) {\n    if (lines[lines.length - 1] != '') {\n      lines.push('');\n    } else if (!fuzzFactor) {\n      return false;\n    }\n  }\n\n  /**\n   * Checks if the hunk can be made to fit at the provided location with at most `maxErrors`\n   * insertions, substitutions, or deletions, while ensuring also that:\n   * - lines deleted in the hunk match exactly, and\n   * - wherever an insertion operation or block of insertion operations appears in the hunk, the\n   *   immediately preceding and following lines of context match exactly\n   *\n   * `toPos` should be set such that lines[toPos] is meant to match hunkLines[0].\n   *\n   * If the hunk can be applied, returns an object with properties `oldLineLastI` and\n   * `replacementLines`. Otherwise, returns null.\n   */\n  function applyHunk(hunkLines, toPos, maxErrors) {\n    var hunkLinesI = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n    var lastContextLineMatched = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : true;\n    var patchedLines = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : [];\n    var patchedLinesLength = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : 0;\n    var nConsecutiveOldContextLines = 0;\n    var nextContextLineMustMatch = false;\n    for (; hunkLinesI < hunkLines.length; hunkLinesI++) {\n      var hunkLine = hunkLines[hunkLinesI],\n        operation = hunkLine.length > 0 ? hunkLine[0] : ' ',\n        content = hunkLine.length > 0 ? hunkLine.substr(1) : hunkLine;\n      if (operation === '-') {\n        if (compareLine(toPos + 1, lines[toPos], operation, content)) {\n          toPos++;\n          nConsecutiveOldContextLines = 0;\n        } else {\n          if (!maxErrors || lines[toPos] == null) {\n            return null;\n          }\n          patchedLines[patchedLinesLength] = lines[toPos];\n          return applyHunk(hunkLines, toPos + 1, maxErrors - 1, hunkLinesI, false, patchedLines, patchedLinesLength + 1);\n        }\n      }\n      if (operation === '+') {\n        if (!lastContextLineMatched) {\n          return null;\n        }\n        patchedLines[patchedLinesLength] = content;\n        patchedLinesLength++;\n        nConsecutiveOldContextLines = 0;\n        nextContextLineMustMatch = true;\n      }\n      if (operation === ' ') {\n        nConsecutiveOldContextLines++;\n        patchedLines[patchedLinesLength] = lines[toPos];\n        if (compareLine(toPos + 1, lines[toPos], operation, content)) {\n          patchedLinesLength++;\n          lastContextLineMatched = true;\n          nextContextLineMustMatch = false;\n          toPos++;\n        } else {\n          if (nextContextLineMustMatch || !maxErrors) {\n            return null;\n          }\n\n          // Consider 3 possibilities in sequence:\n          // 1. lines contains a *substitution* not included in the patch context, or\n          // 2. lines contains an *insertion* not included in the patch context, or\n          // 3. lines contains a *deletion* not included in the patch context\n          // The first two options are of course only possible if the line from lines is non-null -\n          // i.e. only option 3 is possible if we've overrun the end of the old file.\n          return lines[toPos] && (applyHunk(hunkLines, toPos + 1, maxErrors - 1, hunkLinesI + 1, false, patchedLines, patchedLinesLength + 1) || applyHunk(hunkLines, toPos + 1, maxErrors - 1, hunkLinesI, false, patchedLines, patchedLinesLength + 1)) || applyHunk(hunkLines, toPos, maxErrors - 1, hunkLinesI + 1, false, patchedLines, patchedLinesLength);\n        }\n      }\n    }\n\n    // Before returning, trim any unmodified context lines off the end of patchedLines and reduce\n    // toPos (and thus oldLineLastI) accordingly. This allows later hunks to be applied to a region\n    // that starts in this hunk's trailing context.\n    patchedLinesLength -= nConsecutiveOldContextLines;\n    toPos -= nConsecutiveOldContextLines;\n    patchedLines.length = patchedLinesLength;\n    return {\n      patchedLines: patchedLines,\n      oldLineLastI: toPos - 1\n    };\n  }\n  var resultLines = [];\n\n  // Search best fit offsets for each hunk based on the previous ones\n  var prevHunkOffset = 0;\n  for (var _i = 0; _i < hunks.length; _i++) {\n    var hunk = hunks[_i];\n    var hunkResult = void 0;\n    var maxLine = lines.length - hunk.oldLines + fuzzFactor;\n    var toPos = void 0;\n    for (var maxErrors = 0; maxErrors <= fuzzFactor; maxErrors++) {\n      toPos = hunk.oldStart + prevHunkOffset - 1;\n      var iterator = distanceIterator(toPos, minLine, maxLine);\n      for (; toPos !== undefined; toPos = iterator()) {\n        hunkResult = applyHunk(hunk.lines, toPos, maxErrors);\n        if (hunkResult) {\n          break;\n        }\n      }\n      if (hunkResult) {\n        break;\n      }\n    }\n    if (!hunkResult) {\n      return false;\n    }\n\n    // Copy everything from the end of where we applied the last hunk to the start of this hunk\n    for (var _i2 = minLine; _i2 < toPos; _i2++) {\n      resultLines.push(lines[_i2]);\n    }\n\n    // Add the lines produced by applying the hunk:\n    for (var _i3 = 0; _i3 < hunkResult.patchedLines.length; _i3++) {\n      var _line = hunkResult.patchedLines[_i3];\n      resultLines.push(_line);\n    }\n\n    // Set lower text limit to end of the current hunk, so next ones don't try\n    // to fit over already patched text\n    minLine = hunkResult.oldLineLastI + 1;\n\n    // Note the offset between where the patch said the hunk should've applied and where we\n    // applied it, so we can adjust future hunks accordingly:\n    prevHunkOffset = toPos + 1 - hunk.oldStart;\n  }\n\n  // Copy over the rest of the lines from the old text\n  for (var _i4 = minLine; _i4 < lines.length; _i4++) {\n    resultLines.push(lines[_i4]);\n  }\n  return resultLines.join('\\n');\n}\n\n// Wrapper that supports multiple file patches via callbacks.\nfunction applyPatches(uniDiff, options) {\n  if (typeof uniDiff === 'string') {\n    uniDiff = parsePatch(uniDiff);\n  }\n  var currentIndex = 0;\n  function processIndex() {\n    var index = uniDiff[currentIndex++];\n    if (!index) {\n      return options.complete();\n    }\n    options.loadFile(index, function (err, data) {\n      if (err) {\n        return options.complete(err);\n      }\n      var updatedContent = applyPatch(data, index, options);\n      options.patched(index, updatedContent, function (err) {\n        if (err) {\n          return options.complete(err);\n        }\n        processIndex();\n      });\n    });\n  }\n  processIndex();\n}\n\nfunction structuredPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options) {\n  if (!options) {\n    options = {};\n  }\n  if (typeof options === 'function') {\n    options = {\n      callback: options\n    };\n  }\n  if (typeof options.context === 'undefined') {\n    options.context = 4;\n  }\n  if (options.newlineIsToken) {\n    throw new Error('newlineIsToken may not be used with patch-generation functions, only with diffing functions');\n  }\n  if (!options.callback) {\n    return diffLinesResultToPatch(diffLines(oldStr, newStr, options));\n  } else {\n    var _options = options,\n      _callback = _options.callback;\n    diffLines(oldStr, newStr, _objectSpread2(_objectSpread2({}, options), {}, {\n      callback: function callback(diff) {\n        var patch = diffLinesResultToPatch(diff);\n        _callback(patch);\n      }\n    }));\n  }\n  function diffLinesResultToPatch(diff) {\n    // STEP 1: Build up the patch with no \"\\ No newline at end of file\" lines and with the arrays\n    //         of lines containing trailing newline characters. We'll tidy up later...\n\n    if (!diff) {\n      return;\n    }\n    diff.push({\n      value: '',\n      lines: []\n    }); // Append an empty value to make cleanup easier\n\n    function contextLines(lines) {\n      return lines.map(function (entry) {\n        return ' ' + entry;\n      });\n    }\n    var hunks = [];\n    var oldRangeStart = 0,\n      newRangeStart = 0,\n      curRange = [],\n      oldLine = 1,\n      newLine = 1;\n    var _loop = function _loop() {\n      var current = diff[i],\n        lines = current.lines || splitLines(current.value);\n      current.lines = lines;\n      if (current.added || current.removed) {\n        var _curRange;\n        // If we have previous context, start with that\n        if (!oldRangeStart) {\n          var prev = diff[i - 1];\n          oldRangeStart = oldLine;\n          newRangeStart = newLine;\n          if (prev) {\n            curRange = options.context > 0 ? contextLines(prev.lines.slice(-options.context)) : [];\n            oldRangeStart -= curRange.length;\n            newRangeStart -= curRange.length;\n          }\n        }\n\n        // Output our changes\n        (_curRange = curRange).push.apply(_curRange, _toConsumableArray(lines.map(function (entry) {\n          return (current.added ? '+' : '-') + entry;\n        })));\n\n        // Track the updated file position\n        if (current.added) {\n          newLine += lines.length;\n        } else {\n          oldLine += lines.length;\n        }\n      } else {\n        // Identical context lines. Track line changes\n        if (oldRangeStart) {\n          // Close out any changes that have been output (or join overlapping)\n          if (lines.length <= options.context * 2 && i < diff.length - 2) {\n            var _curRange2;\n            // Overlapping\n            (_curRange2 = curRange).push.apply(_curRange2, _toConsumableArray(contextLines(lines)));\n          } else {\n            var _curRange3;\n            // end the range and output\n            var contextSize = Math.min(lines.length, options.context);\n            (_curRange3 = curRange).push.apply(_curRange3, _toConsumableArray(contextLines(lines.slice(0, contextSize))));\n            var _hunk = {\n              oldStart: oldRangeStart,\n              oldLines: oldLine - oldRangeStart + contextSize,\n              newStart: newRangeStart,\n              newLines: newLine - newRangeStart + contextSize,\n              lines: curRange\n            };\n            hunks.push(_hunk);\n            oldRangeStart = 0;\n            newRangeStart = 0;\n            curRange = [];\n          }\n        }\n        oldLine += lines.length;\n        newLine += lines.length;\n      }\n    };\n    for (var i = 0; i < diff.length; i++) {\n      _loop();\n    }\n\n    // Step 2: eliminate the trailing `\\n` from each line of each hunk, and, where needed, add\n    //         \"\\ No newline at end of file\".\n    for (var _i = 0, _hunks = hunks; _i < _hunks.length; _i++) {\n      var hunk = _hunks[_i];\n      for (var _i2 = 0; _i2 < hunk.lines.length; _i2++) {\n        if (hunk.lines[_i2].endsWith('\\n')) {\n          hunk.lines[_i2] = hunk.lines[_i2].slice(0, -1);\n        } else {\n          hunk.lines.splice(_i2 + 1, 0, '\\\\ No newline at end of file');\n          _i2++; // Skip the line we just added, then continue iterating\n        }\n      }\n    }\n    return {\n      oldFileName: oldFileName,\n      newFileName: newFileName,\n      oldHeader: oldHeader,\n      newHeader: newHeader,\n      hunks: hunks\n    };\n  }\n}\nfunction formatPatch(diff) {\n  if (Array.isArray(diff)) {\n    return diff.map(formatPatch).join('\\n');\n  }\n  var ret = [];\n  if (diff.oldFileName == diff.newFileName) {\n    ret.push('Index: ' + diff.oldFileName);\n  }\n  ret.push('===================================================================');\n  ret.push('--- ' + diff.oldFileName + (typeof diff.oldHeader === 'undefined' ? '' : '\\t' + diff.oldHeader));\n  ret.push('+++ ' + diff.newFileName + (typeof diff.newHeader === 'undefined' ? '' : '\\t' + diff.newHeader));\n  for (var i = 0; i < diff.hunks.length; i++) {\n    var hunk = diff.hunks[i];\n    // Unified Diff Format quirk: If the chunk size is 0,\n    // the first number is one lower than one would expect.\n    // https://www.artima.com/weblogs/viewpost.jsp?thread=164293\n    if (hunk.oldLines === 0) {\n      hunk.oldStart -= 1;\n    }\n    if (hunk.newLines === 0) {\n      hunk.newStart -= 1;\n    }\n    ret.push('@@ -' + hunk.oldStart + ',' + hunk.oldLines + ' +' + hunk.newStart + ',' + hunk.newLines + ' @@');\n    ret.push.apply(ret, hunk.lines);\n  }\n  return ret.join('\\n') + '\\n';\n}\nfunction createTwoFilesPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options) {\n  var _options2;\n  if (typeof options === 'function') {\n    options = {\n      callback: options\n    };\n  }\n  if (!((_options2 = options) !== null && _options2 !== void 0 && _options2.callback)) {\n    var patchObj = structuredPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options);\n    if (!patchObj) {\n      return;\n    }\n    return formatPatch(patchObj);\n  } else {\n    var _options3 = options,\n      _callback2 = _options3.callback;\n    structuredPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, _objectSpread2(_objectSpread2({}, options), {}, {\n      callback: function callback(patchObj) {\n        if (!patchObj) {\n          _callback2();\n        } else {\n          _callback2(formatPatch(patchObj));\n        }\n      }\n    }));\n  }\n}\nfunction createPatch(fileName, oldStr, newStr, oldHeader, newHeader, options) {\n  return createTwoFilesPatch(fileName, fileName, oldStr, newStr, oldHeader, newHeader, options);\n}\n\n/**\n * Split `text` into an array of lines, including the trailing newline character (where present)\n */\nfunction splitLines(text) {\n  var hasTrailingNl = text.endsWith('\\n');\n  var result = text.split('\\n').map(function (line) {\n    return line + '\\n';\n  });\n  if (hasTrailingNl) {\n    result.pop();\n  } else {\n    result.push(result.pop().slice(0, -1));\n  }\n  return result;\n}\n\nfunction arrayEqual(a, b) {\n  if (a.length !== b.length) {\n    return false;\n  }\n  return arrayStartsWith(a, b);\n}\nfunction arrayStartsWith(array, start) {\n  if (start.length > array.length) {\n    return false;\n  }\n  for (var i = 0; i < start.length; i++) {\n    if (start[i] !== array[i]) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction calcLineCount(hunk) {\n  var _calcOldNewLineCount = calcOldNewLineCount(hunk.lines),\n    oldLines = _calcOldNewLineCount.oldLines,\n    newLines = _calcOldNewLineCount.newLines;\n  if (oldLines !== undefined) {\n    hunk.oldLines = oldLines;\n  } else {\n    delete hunk.oldLines;\n  }\n  if (newLines !== undefined) {\n    hunk.newLines = newLines;\n  } else {\n    delete hunk.newLines;\n  }\n}\nfunction merge(mine, theirs, base) {\n  mine = loadPatch(mine, base);\n  theirs = loadPatch(theirs, base);\n  var ret = {};\n\n  // For index we just let it pass through as it doesn't have any necessary meaning.\n  // Leaving sanity checks on this to the API consumer that may know more about the\n  // meaning in their own context.\n  if (mine.index || theirs.index) {\n    ret.index = mine.index || theirs.index;\n  }\n  if (mine.newFileName || theirs.newFileName) {\n    if (!fileNameChanged(mine)) {\n      // No header or no change in ours, use theirs (and ours if theirs does not exist)\n      ret.oldFileName = theirs.oldFileName || mine.oldFileName;\n      ret.newFileName = theirs.newFileName || mine.newFileName;\n      ret.oldHeader = theirs.oldHeader || mine.oldHeader;\n      ret.newHeader = theirs.newHeader || mine.newHeader;\n    } else if (!fileNameChanged(theirs)) {\n      // No header or no change in theirs, use ours\n      ret.oldFileName = mine.oldFileName;\n      ret.newFileName = mine.newFileName;\n      ret.oldHeader = mine.oldHeader;\n      ret.newHeader = mine.newHeader;\n    } else {\n      // Both changed... figure it out\n      ret.oldFileName = selectField(ret, mine.oldFileName, theirs.oldFileName);\n      ret.newFileName = selectField(ret, mine.newFileName, theirs.newFileName);\n      ret.oldHeader = selectField(ret, mine.oldHeader, theirs.oldHeader);\n      ret.newHeader = selectField(ret, mine.newHeader, theirs.newHeader);\n    }\n  }\n  ret.hunks = [];\n  var mineIndex = 0,\n    theirsIndex = 0,\n    mineOffset = 0,\n    theirsOffset = 0;\n  while (mineIndex < mine.hunks.length || theirsIndex < theirs.hunks.length) {\n    var mineCurrent = mine.hunks[mineIndex] || {\n        oldStart: Infinity\n      },\n      theirsCurrent = theirs.hunks[theirsIndex] || {\n        oldStart: Infinity\n      };\n    if (hunkBefore(mineCurrent, theirsCurrent)) {\n      // This patch does not overlap with any of the others, yay.\n      ret.hunks.push(cloneHunk(mineCurrent, mineOffset));\n      mineIndex++;\n      theirsOffset += mineCurrent.newLines - mineCurrent.oldLines;\n    } else if (hunkBefore(theirsCurrent, mineCurrent)) {\n      // This patch does not overlap with any of the others, yay.\n      ret.hunks.push(cloneHunk(theirsCurrent, theirsOffset));\n      theirsIndex++;\n      mineOffset += theirsCurrent.newLines - theirsCurrent.oldLines;\n    } else {\n      // Overlap, merge as best we can\n      var mergedHunk = {\n        oldStart: Math.min(mineCurrent.oldStart, theirsCurrent.oldStart),\n        oldLines: 0,\n        newStart: Math.min(mineCurrent.newStart + mineOffset, theirsCurrent.oldStart + theirsOffset),\n        newLines: 0,\n        lines: []\n      };\n      mergeLines(mergedHunk, mineCurrent.oldStart, mineCurrent.lines, theirsCurrent.oldStart, theirsCurrent.lines);\n      theirsIndex++;\n      mineIndex++;\n      ret.hunks.push(mergedHunk);\n    }\n  }\n  return ret;\n}\nfunction loadPatch(param, base) {\n  if (typeof param === 'string') {\n    if (/^@@/m.test(param) || /^Index:/m.test(param)) {\n      return parsePatch(param)[0];\n    }\n    if (!base) {\n      throw new Error('Must provide a base reference or pass in a patch');\n    }\n    return structuredPatch(undefined, undefined, base, param);\n  }\n  return param;\n}\nfunction fileNameChanged(patch) {\n  return patch.newFileName && patch.newFileName !== patch.oldFileName;\n}\nfunction selectField(index, mine, theirs) {\n  if (mine === theirs) {\n    return mine;\n  } else {\n    index.conflict = true;\n    return {\n      mine: mine,\n      theirs: theirs\n    };\n  }\n}\nfunction hunkBefore(test, check) {\n  return test.oldStart < check.oldStart && test.oldStart + test.oldLines < check.oldStart;\n}\nfunction cloneHunk(hunk, offset) {\n  return {\n    oldStart: hunk.oldStart,\n    oldLines: hunk.oldLines,\n    newStart: hunk.newStart + offset,\n    newLines: hunk.newLines,\n    lines: hunk.lines\n  };\n}\nfunction mergeLines(hunk, mineOffset, mineLines, theirOffset, theirLines) {\n  // This will generally result in a conflicted hunk, but there are cases where the context\n  // is the only overlap where we can successfully merge the content here.\n  var mine = {\n      offset: mineOffset,\n      lines: mineLines,\n      index: 0\n    },\n    their = {\n      offset: theirOffset,\n      lines: theirLines,\n      index: 0\n    };\n\n  // Handle any leading content\n  insertLeading(hunk, mine, their);\n  insertLeading(hunk, their, mine);\n\n  // Now in the overlap content. Scan through and select the best changes from each.\n  while (mine.index < mine.lines.length && their.index < their.lines.length) {\n    var mineCurrent = mine.lines[mine.index],\n      theirCurrent = their.lines[their.index];\n    if ((mineCurrent[0] === '-' || mineCurrent[0] === '+') && (theirCurrent[0] === '-' || theirCurrent[0] === '+')) {\n      // Both modified ...\n      mutualChange(hunk, mine, their);\n    } else if (mineCurrent[0] === '+' && theirCurrent[0] === ' ') {\n      var _hunk$lines;\n      // Mine inserted\n      (_hunk$lines = hunk.lines).push.apply(_hunk$lines, _toConsumableArray(collectChange(mine)));\n    } else if (theirCurrent[0] === '+' && mineCurrent[0] === ' ') {\n      var _hunk$lines2;\n      // Theirs inserted\n      (_hunk$lines2 = hunk.lines).push.apply(_hunk$lines2, _toConsumableArray(collectChange(their)));\n    } else if (mineCurrent[0] === '-' && theirCurrent[0] === ' ') {\n      // Mine removed or edited\n      removal(hunk, mine, their);\n    } else if (theirCurrent[0] === '-' && mineCurrent[0] === ' ') {\n      // Their removed or edited\n      removal(hunk, their, mine, true);\n    } else if (mineCurrent === theirCurrent) {\n      // Context identity\n      hunk.lines.push(mineCurrent);\n      mine.index++;\n      their.index++;\n    } else {\n      // Context mismatch\n      conflict(hunk, collectChange(mine), collectChange(their));\n    }\n  }\n\n  // Now push anything that may be remaining\n  insertTrailing(hunk, mine);\n  insertTrailing(hunk, their);\n  calcLineCount(hunk);\n}\nfunction mutualChange(hunk, mine, their) {\n  var myChanges = collectChange(mine),\n    theirChanges = collectChange(their);\n  if (allRemoves(myChanges) && allRemoves(theirChanges)) {\n    // Special case for remove changes that are supersets of one another\n    if (arrayStartsWith(myChanges, theirChanges) && skipRemoveSuperset(their, myChanges, myChanges.length - theirChanges.length)) {\n      var _hunk$lines3;\n      (_hunk$lines3 = hunk.lines).push.apply(_hunk$lines3, _toConsumableArray(myChanges));\n      return;\n    } else if (arrayStartsWith(theirChanges, myChanges) && skipRemoveSuperset(mine, theirChanges, theirChanges.length - myChanges.length)) {\n      var _hunk$lines4;\n      (_hunk$lines4 = hunk.lines).push.apply(_hunk$lines4, _toConsumableArray(theirChanges));\n      return;\n    }\n  } else if (arrayEqual(myChanges, theirChanges)) {\n    var _hunk$lines5;\n    (_hunk$lines5 = hunk.lines).push.apply(_hunk$lines5, _toConsumableArray(myChanges));\n    return;\n  }\n  conflict(hunk, myChanges, theirChanges);\n}\nfunction removal(hunk, mine, their, swap) {\n  var myChanges = collectChange(mine),\n    theirChanges = collectContext(their, myChanges);\n  if (theirChanges.merged) {\n    var _hunk$lines6;\n    (_hunk$lines6 = hunk.lines).push.apply(_hunk$lines6, _toConsumableArray(theirChanges.merged));\n  } else {\n    conflict(hunk, swap ? theirChanges : myChanges, swap ? myChanges : theirChanges);\n  }\n}\nfunction conflict(hunk, mine, their) {\n  hunk.conflict = true;\n  hunk.lines.push({\n    conflict: true,\n    mine: mine,\n    theirs: their\n  });\n}\nfunction insertLeading(hunk, insert, their) {\n  while (insert.offset < their.offset && insert.index < insert.lines.length) {\n    var line = insert.lines[insert.index++];\n    hunk.lines.push(line);\n    insert.offset++;\n  }\n}\nfunction insertTrailing(hunk, insert) {\n  while (insert.index < insert.lines.length) {\n    var line = insert.lines[insert.index++];\n    hunk.lines.push(line);\n  }\n}\nfunction collectChange(state) {\n  var ret = [],\n    operation = state.lines[state.index][0];\n  while (state.index < state.lines.length) {\n    var line = state.lines[state.index];\n\n    // Group additions that are immediately after subtractions and treat them as one \"atomic\" modify change.\n    if (operation === '-' && line[0] === '+') {\n      operation = '+';\n    }\n    if (operation === line[0]) {\n      ret.push(line);\n      state.index++;\n    } else {\n      break;\n    }\n  }\n  return ret;\n}\nfunction collectContext(state, matchChanges) {\n  var changes = [],\n    merged = [],\n    matchIndex = 0,\n    contextChanges = false,\n    conflicted = false;\n  while (matchIndex < matchChanges.length && state.index < state.lines.length) {\n    var change = state.lines[state.index],\n      match = matchChanges[matchIndex];\n\n    // Once we've hit our add, then we are done\n    if (match[0] === '+') {\n      break;\n    }\n    contextChanges = contextChanges || change[0] !== ' ';\n    merged.push(match);\n    matchIndex++;\n\n    // Consume any additions in the other block as a conflict to attempt\n    // to pull in the remaining context after this\n    if (change[0] === '+') {\n      conflicted = true;\n      while (change[0] === '+') {\n        changes.push(change);\n        change = state.lines[++state.index];\n      }\n    }\n    if (match.substr(1) === change.substr(1)) {\n      changes.push(change);\n      state.index++;\n    } else {\n      conflicted = true;\n    }\n  }\n  if ((matchChanges[matchIndex] || '')[0] === '+' && contextChanges) {\n    conflicted = true;\n  }\n  if (conflicted) {\n    return changes;\n  }\n  while (matchIndex < matchChanges.length) {\n    merged.push(matchChanges[matchIndex++]);\n  }\n  return {\n    merged: merged,\n    changes: changes\n  };\n}\nfunction allRemoves(changes) {\n  return changes.reduce(function (prev, change) {\n    return prev && change[0] === '-';\n  }, true);\n}\nfunction skipRemoveSuperset(state, removeChanges, delta) {\n  for (var i = 0; i < delta; i++) {\n    var changeContent = removeChanges[removeChanges.length - delta + i].substr(1);\n    if (state.lines[state.index + i] !== ' ' + changeContent) {\n      return false;\n    }\n  }\n  state.index += delta;\n  return true;\n}\nfunction calcOldNewLineCount(lines) {\n  var oldLines = 0;\n  var newLines = 0;\n  lines.forEach(function (line) {\n    if (typeof line !== 'string') {\n      var myCount = calcOldNewLineCount(line.mine);\n      var theirCount = calcOldNewLineCount(line.theirs);\n      if (oldLines !== undefined) {\n        if (myCount.oldLines === theirCount.oldLines) {\n          oldLines += myCount.oldLines;\n        } else {\n          oldLines = undefined;\n        }\n      }\n      if (newLines !== undefined) {\n        if (myCount.newLines === theirCount.newLines) {\n          newLines += myCount.newLines;\n        } else {\n          newLines = undefined;\n        }\n      }\n    } else {\n      if (newLines !== undefined && (line[0] === '+' || line[0] === ' ')) {\n        newLines++;\n      }\n      if (oldLines !== undefined && (line[0] === '-' || line[0] === ' ')) {\n        oldLines++;\n      }\n    }\n  });\n  return {\n    oldLines: oldLines,\n    newLines: newLines\n  };\n}\n\nfunction reversePatch(structuredPatch) {\n  if (Array.isArray(structuredPatch)) {\n    return structuredPatch.map(reversePatch).reverse();\n  }\n  return _objectSpread2(_objectSpread2({}, structuredPatch), {}, {\n    oldFileName: structuredPatch.newFileName,\n    oldHeader: structuredPatch.newHeader,\n    newFileName: structuredPatch.oldFileName,\n    newHeader: structuredPatch.oldHeader,\n    hunks: structuredPatch.hunks.map(function (hunk) {\n      return {\n        oldLines: hunk.newLines,\n        oldStart: hunk.newStart,\n        newLines: hunk.oldLines,\n        newStart: hunk.oldStart,\n        lines: hunk.lines.map(function (l) {\n          if (l.startsWith('-')) {\n            return \"+\".concat(l.slice(1));\n          }\n          if (l.startsWith('+')) {\n            return \"-\".concat(l.slice(1));\n          }\n          return l;\n        })\n      };\n    })\n  });\n}\n\n// See: http://code.google.com/p/google-diff-match-patch/wiki/API\nfunction convertChangesToDMP(changes) {\n  var ret = [],\n    change,\n    operation;\n  for (var i = 0; i < changes.length; i++) {\n    change = changes[i];\n    if (change.added) {\n      operation = 1;\n    } else if (change.removed) {\n      operation = -1;\n    } else {\n      operation = 0;\n    }\n    ret.push([operation, change.value]);\n  }\n  return ret;\n}\n\nfunction convertChangesToXML(changes) {\n  var ret = [];\n  for (var i = 0; i < changes.length; i++) {\n    var change = changes[i];\n    if (change.added) {\n      ret.push('<ins>');\n    } else if (change.removed) {\n      ret.push('<del>');\n    }\n    ret.push(escapeHTML(change.value));\n    if (change.added) {\n      ret.push('</ins>');\n    } else if (change.removed) {\n      ret.push('</del>');\n    }\n  }\n  return ret.join('');\n}\nfunction escapeHTML(s) {\n  var n = s;\n  n = n.replace(/&/g, '&amp;');\n  n = n.replace(/</g, '&lt;');\n  n = n.replace(/>/g, '&gt;');\n  n = n.replace(/\"/g, '&quot;');\n  return n;\n}\n\nexport { Diff, applyPatch, applyPatches, canonicalize, convertChangesToDMP, convertChangesToXML, createPatch, createTwoFilesPatch, diffArrays, diffChars, diffCss, diffJson, diffLines, diffSentences, diffTrimmedLines, diffWords, diffWordsWithSpace, formatPatch, merge, parsePatch, reversePatch, structuredPatch };\n", null, null, null, null, null, null, null, null], "mappings": ";;;;;;AAAA;AAAA;AAeA,KAAC,SAAUA,QAAO;AAGhB,UAAI,gBAAgB,MAChB,QAAQ,OACR,WAAY,OACZ,MAAM,OACN,SAAS,OACT,WAAW,UACX,gBAAgB;AAEpB,MAAAA,OAAM,OAAO;AAAA,QACX,KAAK;AAAA,QAAG,KAAK;AAAA,QAAG,KAAK;AAAA,QAAG,KAAK;AAAA,QAC7B,KAAK;AAAA,QAAG,KAAK;AAAA,QAAG,KAAK;AAAA,QAAG,KAAK;AAAA,QAAG,MAAM;AAAA,QACtC,KAAK;AAAA,QAAI,KAAK;AAAA,QAAI,MAAM;AAAA,MAC1B;AAEA,MAAAA,OAAM,OAAO,SAAS,KAAK,MAAM,YAAY;AAC3C,YAAI,MAAM,KAAK,QACX,UAAU,GACV,cAAc,GACd,SAAS,GACT,QAAQ,SACR,UAAU,MACV,MAAM,MACN,MAAM,IACN,SAAS,CAAC,GACV,UAAU,OACV,IAAI,GACJ,YAAY,GACZ,OAAO,MACP,OAAO;AAEX,iBAAS,SAAS;AAChB,cAAI,IAAI,SAAS,GAAG;AAClB,mBAAO,KAAK,EAAC,KAAK,MAAM,MAAM,IAAI,OAAO,GAAG,EAAC,CAAC;AAC9C,kBAAM;AAAA,UACR;AAAA,QACF;AAEA,iBAAS,mBAAmB;AAC1B,cAAI,kBAAkB;AACtB,mBAAS,IAAI,WAAW,IAAI,OAAO,QAAQ,KAAK;AAC9C,8BACGA,OAAM,KAAK,OAAO,CAAC,EAAE,GAAG,IAAIA,OAAM,KAAK,IAAI,KAC3C,OAAO,CAAC,EAAE,OAAO,QAAQ,OAAO,CAAC,EAAE,KAAK,MAAM,aAAa,MAAM;AACpE,gBAAI,CAAC,iBAAiB;AACpB,qBAAO;AAAA,YACT;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,iBAAS,WAAW,aAAa,WAAW;AAC1C,iBAAO;AAEP,cAAI,eAAe,iBAAiB,GAAG;AACrC,qBAAS,IAAI,WAAW,MAAM,IAAI,OAAO,QAAQ,KAAK;AACpD,kBAAI,OAAO,CAAC,EAAE,MAAM;AAClB,qBAAK,OAAO,OAAO,IAAE,CAAC,MAAM,KAAK,OAAO,KAAK;AAE3C,uBAAK,SAAS,OAAO,CAAC,EAAE,KAAK,SAAS;AAAA,gBACxC;AACA,uBAAO,OAAO,GAAG,CAAC;AAAA,cACpB;AAAA,YACF;AAAA,UACF,WAAW,CAAC,WAAW;AACrB,mBAAO,KAAK,EAAC,KAAI,KAAI,CAAC;AAAA,UACxB;AAEA,oBAAU;AACV,sBAAY,OAAO;AAAA,QACrB;AAEA,iBAAS,iBAAiBC,OAAM,OAAO;AACrC,cAAI,QAAQ,MAAM,MACd,aAAaA,MAAK,QAAQ,OAAO,KAAK,GACtCC,cAAa;AAAA,YACXD,MAAK,UAAUA,MAAK,QAAQ,KAAK,KAAK,IAAI,GAAG,UAAU;AAAA,UACzD,EAAE,MAAM,GAAG;AAEf,iBAAOC,YAAW,CAAC;AACnB,iBAAOA,YAAWA,YAAW,SAAS,CAAC;AAEvC,iBAAO,aAAa,MAAM,SAAS;AAAA,QACrC;AAEA,YAAI,YAAY;AACd,uBAAa,WAAW,MAAM,GAAG;AACjC,iBAAO,WAAW,CAAC;AACnB,iBAAO,WAAW,CAAC;AAAA,QACrB;AAEA,aAAK,IAAI,GAAG,IAAI,KAAK,KAAK;AACxB,cAAI,SAAS,SAAS;AACpB,gBAAI,UAAU,MAAM,MAAM,CAAC,GAAG;AAC5B,gBAAE;AACF,qBAAO;AACP,sBAAQ;AAAA,YACV,OAAO;AACL,kBAAI,KAAK,OAAO,CAAC,KAAK,MAAM;AAC1B,2BAAW,OAAO;AAAA,cACpB,OAAO;AACL,uBAAO,KAAK,OAAO,CAAC;AAAA,cACtB;AAAA,YACF;AAAA,UACF,WAAW,SAAS,aAAa;AAC/B,iBAAK,KAAK,SAAS;AACnB,kBAAMF,OAAM,KAAK,KAAK,OAAO,IAAI,CAAC,CAAC;AACnC,sBAAU,MAAM,KAAK,OAAO,IAAI,CAAC,IAAI;AACrC,gBAAI,WAAW,KAAK;AAClB,kBAAI,iBAAiB,MAAM,CAAC;AAC5B,sBAAQ;AAAA,YACV,OAAO;AACL,kBAAI,KAAK;AACP;AAAA,cACF;AACA,sBAAQ;AAAA,YACV;AACA,sBAAU;AAAA,UACZ,OAAO;AACL,gBAAI,UAAU,MAAM,MAAM,CAAC,GAAG;AAC5B,qBAAO,KAAK;AAAA,gBAAC,KAAK;AAAA,gBAAS,GAAG,KAAK,GAAG;AAAA,gBAAG;AAAA,gBAAY;AAAA,gBACxC,GAAI,WAAW,MAAO,UAAU,KAAK,SAAS,IAAI,KAAK;AAAA,cAAM,CAAC;AAC3E,oBAAM;AACN,mBAAK,KAAK,SAAS;AACnB,sBAAQ;AACR,kBAAI,WAAW,KAAK;AAClB,oBAAI,QAAQ,MAAM;AAChB;AAAA,gBACF,OAAO;AACL,oCAAkB,OAAO,OAAO,SAAS,CAAC,CAAC;AAAA,gBAC7C;AAAA,cACF;AAAA,YACF,OAAO;AACL,qBAAO,KAAK,OAAO,CAAC;AAAA,YACtB;AAAA,UACF;AAAA,QACF;AAEA,mBAAW,SAAS,IAAI;AAExB,eAAO;AAAA,MACT;AAEA,eAAS,kBAAkB,OAAO;AAChC,YAAI,MAAM,EAAE,OAAO,MAAM,EAAE,SAAS,CAAC,MAAM,KAAK;AAC9C,gBAAM,IAAI,MAAM,EAAE,UAAU,GAAG,MAAM,EAAE,SAAS,CAAC;AAAA,QACnD;AAAA,MACF;AAEA,eAAS,KAAK,GAAG;AACf,YAAI,EAAE,MAAM;AACV,iBAAO,EAAE,KAAK;AAAA,QAChB;AAEA,eAAO,EAAE,QAAQ,cAAc,EAAE;AAAA,MACnC;AAEA,eAAS,UAAU,KAAK,MAAM,OAAO;AACnC,YAAI,KAAK,OAAO,KAAK,KAAK,IAAI,OAAO,CAAC,GAAG;AACvC,iBAAO;AAAA,QACT;AAEA,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAI,GAAG,KAAK;AAC1C,cAAI,KAAK,OAAO,QAAQ,CAAC,KAAK,IAAI,OAAO,CAAC,GAAG;AAC3C,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAGA,UAAI,iBAAiB,EAAC,MAAM,MAAM,MAAM,MAAM,KAAK,MAAM,KAAK,KAAI;AAElE,eAAS,UAAU,QAAQ,MAAM,OAAO,YAAY;AAClD,YAAI,eAAe,CAAC,GAChB,SAAS,MACT,OAAO,MACP,QAAQ;AAEZ,eAAO,MAAM,MAAM,SAAS,CAAC;AAE7B,eAAO,OAAO,SAAS,GAAG;AACxB,kBAAQ,OAAO,MAAM;AAErB,cAAI,QAAQ,KAAK,OAAO,OAAO,EAAE,MAAM,OAAO,iBAAiB;AAC7D,kBAAM,IAAI,MAAM,iCAAiC;AAAA,UACnD;AAEA,cAAIA,OAAM,KAAK,MAAM,GAAG,KAAKA,OAAM,KAAK,GAAG,KAAK,SAAS,OAAO,UAAU,GAAG;AAC3E,kBAAM,KAAK,KAAK;AAChB,kBAAM,QAAQ,UAAU,QAAQ,MAAM,KAAK,OAAO,UAAU;AAAA,UAC9D,WAAW,MAAM,OAAO,KAAK;AAC3B,gBAAI,MAAM,WAAW,GAAG;AACtB,oBAAM,IAAI,MAAM,kCAAkC,MAAM,CAAC;AAAA,YAC3D;AACA,qBAAS,MAAM,IAAI;AACnB,gBAAI,MAAM,KAAK,OAAO,KAAK,CAAC,SAAS,MAAM,GAAG,OAAO,GAAG,UAAU,GAAG;AACnE,oBAAM,IAAI,MAAM,oBAAoB,OAAO,IAAI,UAAU,MAAM,CAAC;AAAA,YAClE;AACA,mBAAO,MAAM,MAAM;AACnB,mBAAO;AAAA,UACT,WAAW,MAAM,OAAO,MAAM;AAC5B,kBAAM,OAAQ,OAAO,UAAU,KAAO,OAAO,CAAC,EAAE,OAAO;AAAA,UACzD;AAEA,uBAAa,KAAK,KAAK;AAAA,QACzB;AAEA,YAAI,MAAM,SAAS,GAAG;AACpB,gBAAM,IAAI,MAAM,0BAA0B,MAAM,IAAI,EAAE,CAAC;AAAA,QACzD;AAEA,eAAO;AAAA,MACT;AAEA,eAAS,SAAS,OAAO,MAAM;AAC7B,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK;AAC3C,cAAI,KAAK,CAAC,EAAE,KAAK,MAAM,GAAG;AACxB,kBAAM,MAAM;AACZ,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAEA,eAAS,SAAS,OAAO,MAAM,MAAM;AACnC,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK;AAC3C,cAAI,KAAK,CAAC,EAAE,KAAK,SAAS,KAAK,CAAC,EAAE,KAAK,MAAM;AAC3C,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAEA,eAAS,uBAAuB,KAAK;AACnC,YAAI,QAAQ,CAAC;AACb,iBAASG,QAAO,KAAK;AACnB,gBAAM,KAAK,MAAM,IAAIA,IAAG,IAAI,2BAA2B,IAAIA,IAAG,IAAI,GAAG;AAAA,QACvE;AACA,eAAO,OAAO,MAAM,KAAK,GAAG,IAAI;AAAA,MAClC;AAEA,eAAS,kBAAkB,SAAS;AAClC,YAAI,WAAW,CAAC;AAChB,iBAASA,QAAO,QAAQ,UAAU;AAChC,mBAAS,KAAK,MAAM,IAAIA,IAAG,IAAI,cAAc,IAAI,QAAQ,SAASA,IAAG,EAAE,IAAI,IAAI,QAAQ,kBAAkB,QAAQ,SAASA,IAAG,CAAC,IAAI,GAAG;AAAA,QACvI;AACA,eAAO,gBAAgB,SAAS,KAAK,GAAG,IAAI,cAAc,uBAAuB,QAAQ,IAAI;AAAA,MAC/F;AAEA,MAAAH,OAAM,YAAY,SAAS,SAAS,MAAM,SAAS;AACjD,eAAO,+BAA+BA,OAAM,SAAS,QAAQ,IAAI,IAAI,QAAQ,kBAAkB,OAAO,IAAK;AAAA,MAC7G;AAEA,UAAI,WAAW;AACf,MAAAA,OAAM,WAAW,SAAS,MAAM,MAAM,SAAS;AAC7C,mBAAW;AACX,YAAI,UAAU,EAAE,MAAM,IAAI,MAAM,CAAC,GAAG,UAAU,CAAC,EAAE;AACjD,QAAAA,OAAM,KAAK,MAAM,OAAO;AAExB,YAAI,QAAQ,UAAU;AACpB,iBAAO,KAAK,UAAU,SAAS,MAAM,OAAO;AAAA,QAC9C;AAEA,eAAO,KAAK,aAAa,SAAS,MAAM,OAAO;AAAA,MACjD;AAEA,MAAAA,OAAM,WAAW,SAAS,MAAM;AAC9B,eAAO,6BAA6B,OAAO;AAAA,MAC7C;AAEA,MAAAA,OAAM,WAAWA,OAAM;AAEvB,MAAAA,OAAM,eAAe,SAAS,SAAS,MAAM,SAAS;AACpD,YAAI,WAAW,KAAK,aAAa,OAAO;AACxC,iBAAS,OAAO,IAAI,SAAS,KAAK,KAAK,KAAK,KAAK,SAAS,QAAQ,IAAI,CAAC;AACvE,eAAO,IAAI,KAAK,SAAS,UAAU,MAAM,MAAM,OAAO;AAAA,MACxD;AAEA,MAAAA,OAAM,eAAe,SAAS,SAAS;AACrC,YAAIG,MAAK,WAAW,EAAC,MAAM,CAAC,GAAG,UAAU,QAAQ,UAAU,MAAM,QAAQ,KAAI;AAC7E,aAAKA,QAAO,SAAS,UAAU;AAC7B,mBAAS,SAASA,IAAG,IAAI,KAAK,aAAa,SAAS,SAASA,IAAG,CAAC;AAAA,QACnE;AACA,aAAKA,QAAO,QAAQ,MAAM;AACxB,mBAAS,KAAKA,IAAG,IAAI,IAAI,SAAS,KAAK,KAAK,KAAK,KAAK,QAAQ,KAAKA,IAAG,CAAC;AAAA,QACzE;AACA,eAAO;AAAA,MACT;AAEA,eAAS,IAAI,GAAG;AACd,eAAO,EAAE,QAAQ,QAAQ,MAAM,EACtB,QAAQ,OAAO,KAAM,EACrB,QAAQ,UAAU,KAAK,EACvB,QAAQ,KAAK,KAAK,EAClB,QAAQ,UAAU,SAAS,EAC3B,QAAQ,eAAe,SAAS;AAAA,MAC3C;AAEA,eAAS,aAAa,GAAG;AACvB,eAAQ,CAAC,EAAE,QAAQ,GAAG,IAAK,MAAM;AAAA,MACnC;AAEA,eAAS,cAAc,MAAM,SAAS;AACpC,YAAI,SAAS,OAAO,QAAQ,UAAU;AACtC,YAAI,MAAM,SAAS,KAAK,IAAI;AAC5B,gBAAQ,SAAS,GAAG,IAAI,EAAC,MAAM,KAAK,GAAG,UAAU,CAAC,EAAC;AACnD,gBAAQ,QAAQ,eAAgB,IAAI,GAAG,IAAI,aAAa,KAAK,UAAU,MAAM;AAC7E,eAAO;AAAA,MACT;AAEA,MAAAH,OAAM,UAAU;AAAA,QACd,KAAK,SAAS,MAAM,SAAS;AAC3B,kBAAQ,QAAQ,cAAc,aAAa,KAAK,CAAC,IAAI,OAAO,IAAI,KAAK,CAAC,IAAI,oBAC/C,KAAK,IAAI,MAAM,KAAK,MAAM,OAAO,KAAK,OAAO,MAAM,KAAK,OAAO;AAE1F,UAAAA,OAAM,KAAK,KAAK,OAAO,OAAO;AAC9B,kBAAQ,QAAQ;AAAA,QAClB;AAAA,QAEA,KAAK,SAAS,MAAM,SAAS;AAC3B,kBAAQ,QAAQ,eAAe,aAAa,KAAK,CAAC,IAAI,OAAO,IAAI,KAAK,CAAC,IAAI;AAC3E,UAAAA,OAAM,KAAK,KAAK,OAAO,OAAO;AAC9B,kBAAQ,QAAQ;AAAA,QAClB;AAAA,QAEA,KAAK;AAAA,QACL,KAAK,SAAS,MAAM,SAAS;AAC3B,cAAI,MAAM,EAAC,UAAU,CAAC,GAAG,MAAM,IAAI,MAAM,CAAC,GAAG,WAAW,KAAI;AAC5D,UAAAA,OAAM,KAAK,KAAK,OAAO,GAAG;AAC1B,cAAI,WAAW,QAAQ,SAAS,cAAc,MAAM,OAAO,CAAC;AAC5D,mBAAS,OAAO,IAAI;AACpB,mBAAS,WAAW,IAAI;AAAA,QAC1B;AAAA,QAEA,KAAK,SAAS,MAAM,SAAS;AAC3B,cAAI,MAAM,EAAC,MAAM,CAAC,GAAG,MAAM,IAAI,UAAU,QAAQ,UAAU,QAAQ,KAAK,EAAC;AACzE,UAAAA,OAAM,KAAK,KAAK,OAAO,GAAG;AAC1B,kBAAQ,KAAK,KAAK,CAAC,IAAI,IAAI;AAC3B,cAAI,CAAC,QAAQ,WAAW;AACtB,oBAAQ,QAAQ,YAAY,IAAI,KAAK,CAAC,IAAI;AAAA,UAC5C;AAAA,QACF;AAAA,QAEA,MAAM,SAAS,MAAM,SAAS;AAC5B,kBAAQ,QAAQ,MAAM,WAAW,KAAK,OAAO,KAAK,OAAO;AAAA,QAC3D;AAAA,QAEA,MAAM,SAAS,MAAM,SAAS;AAC5B,kBAAQ,QAAQ,eAAe,aAAa,KAAK,CAAC,IAAI,OAAO,IAAI,KAAK,CAAC,IAAI;AAAA,QAC7E;AAAA,QAEA,MAAM,SAAS,MAAM,SAAS;AAC5B,kBAAQ,QAAQ,MAAM,MAAM,IAAI,KAAK,IAAI,IAAI,GAAG;AAAA,QAClD;AAAA,QAEA,KAAK;AAAA,QAEL,KAAK;AAAA,MACP;AAEA,eAAS,aAAa,MAAM,SAAS;AACnC,gBAAQ,QAAQ,eAAe,aAAa,KAAK,CAAC,IAAI,OAAO,IAAI,KAAK,CAAC,IAAI;AAAA,MAC7E;AAEA,eAAS,MAAM,GAAG;AAChB,eAAO,SAAS,IAAI;AAAA,MACtB;AAEA,MAAAA,OAAM,OAAO,SAAS,UAAU,SAAS;AACvC,YAAI;AACJ,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,IAAI,GAAG,KAAK;AAC/C,iBAAOA,OAAM,QAAQ,SAAS,CAAC,EAAE,GAAG;AACpC,kBAAQ,KAAK,SAAS,CAAC,GAAG,OAAO;AAAA,QACnC;AACA,eAAO;AAAA,MACT;AAEA,MAAAA,OAAM,QAAQ,SAAS,QAAQ,MAAM,SAAS;AAC5C,kBAAU,WAAW,CAAC;AACtB,eAAO,UAAU,QAAQ,IAAI,CAAC,GAAG,QAAQ,eAAe,CAAC,CAAC;AAAA,MAC5D;AAEA,MAAAA,OAAM,QAAQ,CAAC;AAEf,MAAAA,OAAM,WAAW,SAAS,MAAM,SAAS;AACvC,eAAO,CAAC,MAAM,CAAC,CAAC,QAAQ,UAAU,CAAC,CAAC,QAAQ,eAAe,QAAQ,YAAY,CAAC,CAAC,QAAQ,QAAQ,EAAE,KAAK,IAAI;AAAA,MAC9G;AAEA,MAAAA,OAAM,UAAU,SAAS,MAAM,SAAS;AACtC,kBAAU,WAAW,CAAC;AACtB,YAAIG,OAAMH,OAAM,SAAS,MAAM,OAAO;AACtC,YAAI,WAAW,KAAK,MAAMG,IAAG;AAE7B,YAAI,UAAU;AACZ,cAAI,WAAW,SAAS;AACxB,mBAAS,QAAQ,UAAU;AACzB,mBAAO,SAAS,IAAI,EAAE;AAAA,UACxB;AACA,iBAAO;AAAA,QACT;AAEA,mBAAW,KAAK,SAAS,KAAK,MAAM,KAAK,KAAK,MAAM,QAAQ,UAAU,GAAG,MAAM,OAAO,GAAG,MAAM,OAAO;AACtG,eAAO,KAAK,MAAMA,IAAG,IAAI;AAAA,MAC3B;AAAA,IACF,GAAG,OAAO,YAAY,cAAc,UAAU,KAAK;AAAA;AAAA;;;ACtanD;AAAA;AAeA,QAAIC,SAAQ,CAAC;AAEb,KAAC,SAAUA,QAAO;AAChB,MAAAA,OAAM,WAAW,SAAU,SAAS,MAAM,UAAU,SAAS;AAC3D,kBAAU,WAAW,CAAC;AACtB,aAAK,IAAI,QAAQ,QAAQ,KAAK;AAC9B,aAAK,IAAI;AACT,aAAK,UAAU,WAAW,CAAC;AAC3B,aAAK,OAAO,QAAQ;AACpB,aAAK,WAAW,QAAQ,YAAY,CAAC;AACrC,aAAK,OAAO,QAAQ,QAAQ,CAAC;AAC7B,aAAK,MAAM;AAAA,MACb;AAEA,MAAAA,OAAM,SAAS,YAAY;AAAA;AAAA,QAEzB,GAAG,SAAU,SAAS,UAAU,QAAQ;AAAE,iBAAO;AAAA,QAAI;AAAA;AAAA,QAGrD,GAAG;AAAA;AAAA,QAGH,GAAG;AAAA,QAEH,QAAQ,SAAS,OAAO,SAAS,UAAU,QAAQ;AACjD,iBAAO,KAAK,GAAG,CAAC,OAAO,GAAG,YAAY,CAAC,GAAG,MAAM;AAAA,QAClD;AAAA;AAAA,QAGA,IAAI,SAAU,SAAS,UAAU,QAAQ;AACvC,iBAAO,KAAK,EAAE,SAAS,UAAU,MAAM;AAAA,QACzC;AAAA;AAAA,QAGA,IAAI,SAAS,QAAQ,UAAU;AAC7B,cAAI,UAAU,KAAK,SAAS,MAAM;AAGlC,cAAI,WAAW,SAAS,QAAQ,IAAI;AACpC,cAAI,QAAQ,YAAY,QAAQ,QAAQ,UAAU;AAChD,mBAAO,QAAQ;AAAA,UACjB;AAEA,cAAI,OAAO,YAAY,UAAU;AAC/B,gBAAI,CAAC,KAAK,GAAG;AACX,oBAAM,IAAI,MAAM,wBAAwB;AAAA,YAC1C;AACA,uBAAW,KAAK,EAAE,QAAQ,UAAU,KAAK,OAAO;AAAA,UAClD;AAEA,cAAI,CAAC,UAAU;AACb,mBAAO;AAAA,UACT;AAGA,eAAK,SAAS,MAAM,EAAE,OAAO;AAE7B,cAAI,QAAQ,MAAM;AAEhB,gBAAI,CAAC,SAAS,UAAW,UAAS,YAAY,CAAC;AAC/C,iBAAK,OAAO,QAAQ,MAAM;AACxB,kBAAI,CAAC,SAAS,UAAU,GAAG,GAAG;AAC5B,yBAAS,UAAU,GAAG,IAAK,KAAK,cAAc,UAAa,SAAS,UAAU,KAAK,SAAS,IAAK,SAAS,UAAU,KAAK,SAAS,IAAI,KAAK;AAAA,cAC7I;AAAA,YACF;AACA,uBAAW;AAAA,cAAyB;AAAA,cAAU,QAAQ;AAAA,cAAM,QAAQ;AAAA,cAClE,KAAK;AAAA,cAAW,KAAK;AAAA,cAAe,SAAS;AAAA,YAAS;AAAA,UAC1D;AACA,eAAK,SAAS,MAAM,EAAE,WAAW;AAEjC,iBAAO;AAAA,QACT;AAAA;AAAA,QAGA,IAAI,SAAS,QAAQ,SAAS,UAAU,QAAQ;AAC9C,cAAI,UAAU,KAAK,GAAG,QAAQ,QAAQ;AACtC,cAAI,CAAC,SAAS;AACZ,mBAAO;AAAA,UACT;AAEA,iBAAO,QAAQ,GAAG,SAAS,UAAU,MAAM;AAAA,QAC7C;AAAA;AAAA,QAGA,IAAI,SAAS,SAAS,UAAU,SAAS;AACvC,cAAI,OAAO,QAAQ,QAAQ,SAAS,CAAC;AAErC,cAAI,CAAC,QAAQ,IAAI,GAAG;AAClB,oBAAQ,SAAS,UAAU,IAAI;AAC/B;AAAA,UACF;AAEA,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,oBAAQ,KAAK,KAAK,CAAC,CAAC;AACpB,oBAAQ,SAAS,UAAU,IAAI;AAC/B,oBAAQ,IAAI;AAAA,UACd;AAAA,QACF;AAAA;AAAA,QAGA,GAAG,SAAS,KAAK,KAAK,UAAU,UAAU,OAAO,KAAK,MAAM;AAC1D,cAAI;AAEJ,cAAI,QAAQ,GAAG,KAAK,IAAI,WAAW,GAAG;AACpC,mBAAO;AAAA,UACT;AAEA,cAAI,OAAO,OAAO,YAAY;AAC5B,kBAAM,KAAK,GAAG,KAAK,KAAK,UAAU,UAAU,OAAO,KAAK,IAAI;AAAA,UAC9D;AAEA,iBAAO,CAAC,CAAC;AAET,cAAI,CAAC,YAAY,QAAQ,KAAK;AAC5B,gBAAI,KAAM,OAAO,OAAO,WAAY,MAAM,IAAI,IAAI,SAAS,CAAC,CAAC;AAAA,UAC/D;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA,QAGA,GAAG,SAASC,MAAK,KAAK,UAAU,aAAa;AAC3C,cAAI,OACA,QAAQA,KAAI,MAAM,GAAG,GACrB,MAAM,KAAK,EAAE,MAAM,CAAC,GAAG,KAAK,UAAU,WAAW,GACjD,aAAa,KAAK,QAAQ,UAC1B,KAAK;AAET,cAAIA,SAAQ,OAAO,QAAQ,IAAI,IAAI,SAAS,CAAC,CAAC,GAAG;AAC/C,kBAAM,IAAI,IAAI,SAAS,CAAC;AAAA,UAC1B,OAAO;AACL,qBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,sBAAQ,YAAY,MAAM,CAAC,GAAG,KAAK,UAAU;AAC7C,kBAAI,UAAU,QAAW;AACvB,qBAAK;AACL,sBAAM;AAAA,cACR,OAAO;AACL,sBAAM;AAAA,cACR;AAAA,YACF;AAAA,UACF;AAEA,cAAI,eAAe,CAAC,KAAK;AACvB,mBAAO;AAAA,UACT;AAEA,cAAI,CAAC,eAAe,OAAO,OAAO,YAAY;AAC5C,gBAAI,KAAK,EAAE;AACX,kBAAM,KAAK,GAAG,KAAK,KAAK,QAAQ;AAChC,gBAAI,IAAI;AAAA,UACV;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA,QAGA,GAAG,SAASA,MAAK,KAAK,UAAU,aAAa;AAC3C,cAAI,MAAM,OACN,IAAI,MACJ,QAAQ,OACR,aAAa,KAAK,QAAQ;AAE9B,mBAAS,IAAI,IAAI,SAAS,GAAG,KAAK,GAAG,KAAK;AACxC,gBAAI,IAAI,CAAC;AACT,kBAAM,YAAYA,MAAK,GAAG,UAAU;AACpC,gBAAI,QAAQ,QAAW;AACrB,sBAAQ;AACR;AAAA,YACF;AAAA,UACF;AAEA,cAAI,CAAC,OAAO;AACV,mBAAQ,cAAe,QAAQ;AAAA,UACjC;AAEA,cAAI,CAAC,eAAe,OAAO,OAAO,YAAY;AAC5C,kBAAM,KAAK,GAAG,KAAK,KAAK,QAAQ;AAAA,UAClC;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA,QAGA,IAAI,SAAS,MAAM,IAAI,UAAU,MAAM,MAAM;AAC3C,cAAI,UAAU,KAAK,QAAQ;AAE3B,eAAK,QAAQ,aAAa;AAC1B,eAAK,EAAE,KAAK,GAAG,eAAe,KAAK,KAAK,IAAI,IAAI,CAAC,GAAG,IAAI,QAAQ,CAAC;AACjE,eAAK,QAAQ,aAAa;AAE1B,iBAAO;AAAA,QACT;AAAA;AAAA,QAGA,IAAI,SAAS,MAAM,IAAI,UAAU;AAC/B,cAAI,KAAK,QAAQ,eAAe;AAC9B,kBAAM,IAAI,MAAM,2BAA2B;AAAA,UAC7C;AACA,iBAAO,KAAK,EAAE,QAAQ,MAAM,KAAK,OAAO,EAAE,OAAO,IAAI,QAAQ;AAAA,QAC/D;AAAA;AAAA,QAGA,GAAG,SAAS,GAAG;AAAE,eAAK,OAAO;AAAA,QAAG;AAAA,QAEhC,IAAI,WAAW;AAAE,cAAI,IAAI,KAAK;AAAK,eAAK,MAAM;AAAI,iBAAO;AAAA,QAAG;AAAA;AAAA,QAG5D,IAAI,SAAS,MAAM,KAAK,UAAU,UAAU,OAAO,KAAK,MAAM;AAC5D,cAAI,YACA,KAAK,IAAI,IAAI,SAAS,CAAC,GACvB,SAAS,KAAK,KAAK,EAAE;AAEzB,cAAI,OAAO,UAAU,YAAY;AAC/B,gBAAI,UAAU;AACZ,qBAAO;AAAA,YACT,OAAO;AACL,2BAAc,KAAK,aAAa,KAAK,YAAY,KAAK,SAAS,KAAK,SAAS,IAAK,KAAK,SAAS,KAAK,SAAS,IAAI,KAAK;AACvH,qBAAO,KAAK,GAAG,QAAQ,IAAI,UAAU,WAAW,UAAU,OAAO,GAAG,GAAG,IAAI;AAAA,YAC7E;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAAA;AAAA,QAGA,IAAI,SAAS,MAAM,KAAK,UAAU;AAChC,cAAI,KAAK,IAAI,IAAI,SAAS,CAAC;AAC3B,cAAI,SAAS,KAAK,KAAK,EAAE;AAEzB,cAAI,OAAO,UAAU,YAAY;AAC/B,mBAAO,KAAK,GAAG,eAAe,OAAO,KAAK,EAAE,CAAC,GAAG,IAAI,QAAQ;AAAA,UAC9D;AAEA,iBAAO;AAAA,QACT;AAAA,QAEA,KAAK,SAAS,MAAM,SAAS,UAAU,QAAQ;AAC7C,cAAI,IAAI,KAAK,KAAK,IAAI;AACtB,cAAI,GAAG;AACL,iBAAK,YAAY;AACjB,cAAE,SAAS,UAAU,MAAM,MAAM;AACjC,iBAAK,YAAY;AAAA,UACnB;AAAA,QACF;AAAA,MAEF;AAGA,eAAS,YAAYA,MAAK,OAAO,YAAY;AAC3C,YAAI;AAEJ,YAAI,SAAS,OAAO,SAAS,UAAU;AAErC,cAAI,MAAMA,IAAG,MAAM,QAAW;AAC5B,kBAAM,MAAMA,IAAG;AAAA,UAGjB,WAAW,cAAc,MAAM,OAAO,OAAO,MAAM,OAAO,YAAY;AACpE,kBAAM,MAAM,IAAIA,IAAG;AAAA,UACrB;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAEA,eAAS,yBAAyB,UAAU,MAAM,UAAU,WAAW,eAAe,WAAW;AAC/F,iBAAS,kBAAkB;AAAA,QAAC;AAAC;AAC7B,wBAAgB,YAAY;AAC5B,iBAAS,gBAAgB;AAAA,QAAC;AAAC;AAC3B,sBAAc,YAAY,SAAS;AACnC,YAAIA;AACJ,YAAI,UAAU,IAAI,gBAAgB;AAClC,gBAAQ,OAAO,IAAI,cAAc;AACjC,gBAAQ,WAAW,CAAC;AACpB,gBAAQ,MAAM;AAEd,oBAAY,aAAa,CAAC;AAC1B,gBAAQ,YAAY;AACpB,gBAAQ,WAAW;AACnB,aAAKA,QAAO,MAAM;AAChB,cAAI,CAAC,UAAUA,IAAG,EAAG,WAAUA,IAAG,IAAI,KAAKA,IAAG;AAAA,QAChD;AACA,aAAKA,QAAO,WAAW;AACrB,kBAAQ,KAAKA,IAAG,IAAI,UAAUA,IAAG;AAAA,QACnC;AAEA,wBAAgB,iBAAiB,CAAC;AAClC,gBAAQ,gBAAgB;AACxB,aAAKA,QAAO,UAAU;AACpB,cAAI,CAAC,cAAcA,IAAG,EAAG,eAAcA,IAAG,IAAI,SAASA,IAAG;AAAA,QAC5D;AACA,aAAKA,QAAO,eAAe;AACzB,kBAAQ,SAASA,IAAG,IAAI,cAAcA,IAAG;AAAA,QAC3C;AAEA,eAAO;AAAA,MACT;AAEA,UAAI,OAAO,MACP,MAAM,MACN,MAAM,MACN,QAAQ,OACR,QAAQ,OACR,SAAS;AAEb,eAAS,eAAe,KAAK;AAC3B,eAAO,OAAQ,QAAQ,QAAQ,QAAQ,SAAa,KAAK,GAAG;AAAA,MAC9D;AAEA,eAAS,YAAY,KAAK;AACxB,cAAM,eAAe,GAAG;AACxB,eAAO,OAAO,KAAK,GAAG,IACpB,IACG,QAAQ,MAAM,OAAO,EACrB,QAAQ,KAAK,MAAM,EACnB,QAAQ,KAAK,MAAM,EACnB,QAAQ,OAAO,OAAO,EACtB,QAAQ,OAAO,QAAQ,IAC1B;AAAA,MACJ;AAEA,UAAI,UAAU,MAAM,WAAW,SAAS,GAAG;AACzC,eAAO,OAAO,UAAU,SAAS,KAAK,CAAC,MAAM;AAAA,MAC/C;AAAA,IAEF,GAAG,OAAO,YAAY,cAAc,UAAUD,MAAK;AAAA;AAAA;;;ACpVnD;AAAA;AAiBA,QAAIE,SAAQ;AACZ,IAAAA,OAAM,WAAW,mBAAsB;AACvC,IAAAA,OAAM,WAAWA,OAAM;AACvB,WAAO,UAAUA;AAAA;AAAA;;;ACfjB,IAAY;CAAZ,SAAYC,WAAQ;AAClB,EAAAA,UAAA,QAAA,IAAA;AACA,EAAAA,UAAA,QAAA,IAAA;AACA,EAAAA,UAAA,SAAA,IAAA;AACF,GAJY,aAAA,WAAQ,CAAA,EAAA;AAqEb,IAAM,mBAAsD;EACjE,cAAc;EACd,cAAc;;AAKT,IAAM,mBAAsD;EACjE,OAAO;EACP,OAAO;EACP,MAAM;;AAKD,IAAM,gBAAgD;EAC3D,MAAM;EACN,MAAM;;AAGR,IAAY;CAAZ,SAAYC,kBAAe;AACzB,EAAAA,iBAAA,MAAA,IAAA;AACA,EAAAA,iBAAA,MAAA,IAAA;AACA,EAAAA,iBAAA,OAAA,IAAA;AACF,GAJY,oBAAA,kBAAe,CAAA,EAAA;;;AC9F3B,IAAM,WAAW;EAEf;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAKF,IAAM,QAAQ,OAAO,MAAM,SAAS,KAAK,IAAI,IAAI,KAAK,GAAG;AAKnD,SAAU,gBAAgB,KAAW;AACzC,SAAO,IAAI,QAAQ,OAAO,MAAM;AAClC;AAKM,SAAU,UAAU,MAAY;AACpC,SAAO,OAAO,KAAK,QAAQ,OAAO,GAAG,IAAI;AAC3C;AAKM,SAAU,SAAS,MAAY;AACnC,MAAI,GAAG,KAAK;AACZ,MAAI,OAAO;AAEX,OAAK,IAAI,GAAG,MAAM,KAAK,QAAQ,IAAI,KAAK,KAAK;AAC3C,UAAM,KAAK,WAAW,CAAC;AACvB,YAAQ,QAAQ,KAAK,OAAO;AAC5B,YAAQ;EACV;AAEA,SAAO;AACT;AAaM,SAAU,IAAI,KAAa;AAC/B,QAAM,SAAS,IAAI;AACnB,MAAIC,OAAM;AAEV,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,IAAAA,OAAM,KAAK,IAAIA,MAAK,IAAI,CAAC,CAAC;EAC5B;AACA,SAAOA;AACT;;;AC/DA,SAAS,aAAa,UAAkB,UAAgB;AACtD,QAAM,gBAAgB,SAAS,MAAM,GAAG;AACxC,SAAO,cAAc,SAAS,IAAI,cAAc,cAAc,SAAS,CAAC,IAAI;AAC9E;AAEA,SAAS,cAAc,KAAa,UAAkB;AACpD,SAAO,SAAS,OAAgB,CAAC,YAAY,WAAW,cAAc,IAAI,WAAW,MAAM,GAAG,KAAK;AACrG;AAEA,IAAM,2BAA2B,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AACpE,SAAS,YAAY,MAAc,YAAqB,aAAoB;AAC1E,QAAM,WAAW,gBAAgB,SAAY,CAAC,GAAG,0BAA0B,WAAW,IAAI;AAE1F,QAAM,iBAAiB,aACnB,IAAI,OAAO,IAAI,gBAAgB,UAAU,CAAC,aAAa,IACvD,IAAI,OAAO,aAAa;AAE5B,QAAM,CAAC,EAAE,WAAW,EAAE,IAAI,eAAe,KAAK,IAAI,KAAK,CAAA;AACvD,QAAM,iBAAiB,SAAS,KAAK,OAAK,SAAS,QAAQ,CAAC,MAAM,CAAC;AACnE,QAAM,qBAAqB,iBAAiB,SAAS,MAAM,eAAe,MAAM,IAAI;AAKpF,SAAO,mBAAmB,QAAQ,iEAAiE,EAAE;AACvG;AAEA,SAAS,eAAe,MAAc,WAAkB;AACtD,SAAO,YAAY,MAAM,OAAO,SAAS;AAC3C;AAEA,SAAS,eAAe,MAAc,WAAkB;AACtD,SAAO,YAAY,MAAM,OAAO,SAAS;AAC3C;AAUM,SAAU,MAAM,WAAmB,SAA2B,CAAA,GAAE;AACpE,QAAM,QAAoB,CAAA;AAC1B,MAAI,cAA+B;AACnC,MAAI,eAAiC;AACrC,MAAI,UAAyB;AAC7B,MAAI,WAA0B;AAC9B,MAAI,UAAyB;AAE7B,MAAI,kBAAiC;AACrC,MAAI,kBAAiC;AAGrC,QAAM,oBAAoB;AAC1B,QAAM,oBAAoB;AAC1B,QAAM,mBAAmB;AAGzB,QAAM,UAAU;AAChB,QAAM,UAAU;AAChB,QAAM,kBAAkB;AACxB,QAAM,cAAc;AAEpB,QAAM,WAAW;AACjB,QAAM,SAAS;AAEf,QAAM,aAAa;AACnB,QAAM,WAAW;AAEjB,QAAM,kBAAkB;AACxB,QAAM,qBAAqB;AAC3B,QAAM,QAAQ;AAEd,QAAM,cAAc;AACpB,QAAM,aAAa;AAGnB,QAAM,gBAAgB;AACtB,QAAM,eAAe;AACrB,QAAM,kBAAkB;AACxB,QAAM,sBAAsB;AAE5B,QAAM,YAAY,UACf,QAAQ,iCAAiC,EAAE,EAC3C,QAAQ,UAAU,IAAI,EACtB,MAAM,IAAI;AAGb,WAAS,YAAS;AAChB,QAAI,iBAAiB,QAAQ,gBAAgB,MAAM;AACjD,kBAAY,OAAO,KAAK,YAAY;AACpC,qBAAe;IACjB;EACF;AAMA,WAAS,WAAQ;AACf,QAAI,gBAAgB,MAAM;AACxB,UAAI,CAAC,YAAY,WAAW,oBAAoB,MAAM;AACpD,oBAAY,UAAU;MACxB;AAEA,UAAI,CAAC,YAAY,WAAW,oBAAoB,MAAM;AACpD,oBAAY,UAAU;MACxB;AAEA,UAAI,YAAY,SAAS;AACvB,cAAM,KAAK,WAAW;AACtB,sBAAc;MAChB;IACF;AAEA,sBAAkB;AAClB,sBAAkB;EACpB;AAGA,WAAS,YAAS;AAChB,cAAS;AACT,aAAQ;AAIR,kBAAc;MACZ,QAAQ,CAAA;MACR,cAAc;MACd,YAAY;;EAEhB;AAEA,WAAS,WAAW,MAAY;AAC9B,cAAS;AAET,QAAI;AAgBJ,QAAI,gBAAgB,MAAM;AACxB,UAAK,SAAS,4CAA4C,KAAK,IAAI,GAAI;AACrE,oBAAY,aAAa;AACzB,kBAAU,SAAS,OAAO,CAAC,GAAG,EAAE;AAChC,kBAAU,SAAS,OAAO,CAAC,GAAG,EAAE;MAClC,WAAY,SAAS,8DAA8D,KAAK,IAAI,GAAI;AAC9F,oBAAY,aAAa;AACzB,kBAAU,SAAS,OAAO,CAAC,GAAG,EAAE;AAChC,mBAAW,SAAS,OAAO,CAAC,GAAG,EAAE;AACjC,kBAAU,SAAS,OAAO,CAAC,GAAG,EAAE;MAClC,OAAO;AACL,YAAI,KAAK,WAAW,gBAAgB,GAAG;AACrC,kBAAQ,MAAM,uCAAuC;QACvD;AAEA,kBAAU;AACV,kBAAU;AACV,oBAAY,aAAa;MAC3B;IACF;AAGA,mBAAe;MACb,OAAO,CAAA;MAGP,cAAc;MAGd,eAAe;MAGf,cAAc;MACd,QAAQ;;EAEZ;AAEA,WAAS,WAAW,MAAY;AAC9B,QAAI,gBAAgB,QAAQ,iBAAiB,QAAQ,YAAY,QAAQ,YAAY;AAAM;AAI3F,UAAM,cAAwB;MAC5B,SAAS;;AAGX,UAAM,gBAAgB,YAAY,aAAa,CAAC,MAAM,MAAM,IAAI,IAAI,CAAC,GAAG;AACxE,UAAM,kBAAkB,YAAY,aAAa,CAAC,MAAM,MAAM,IAAI,IAAI,CAAC,GAAG;AAE1E,QAAI,cAAc,MAAM,aAAa,GAAG;AACtC,kBAAY;AACZ,kBAAY,OAAO,SAAS;AAC5B,kBAAY,YAAY;AACxB,kBAAY,YAAY;IAC1B,WAAW,cAAc,MAAM,eAAe,GAAG;AAC/C,kBAAY;AACZ,kBAAY,OAAO,SAAS;AAC5B,kBAAY,YAAY;AACxB,kBAAY,YAAY;IAC1B,OAAO;AACL,kBAAY,OAAO,SAAS;AAC5B,kBAAY,YAAY;AACxB,kBAAY,YAAY;IAC1B;AACA,iBAAa,MAAM,KAAK,WAAW;EACrC;AAOA,WAAS,gBAAgB,MAAc,SAAe;AACpD,QAAI,MAAM;AAEV,WAAO,MAAM,UAAU,SAAS,GAAG;AACjC,UAAI,KAAK,WAAW,MAAM,GAAG;AAC3B,eAAO;MACT;AAEA,UACE,UAAU,GAAG,EAAE,WAAW,iBAAiB,KAC3C,UAAU,MAAM,CAAC,EAAE,WAAW,iBAAiB,KAC/C,UAAU,MAAM,CAAC,EAAE,WAAW,gBAAgB,GAC9C;AACA,eAAO;MACT;AAEA;IACF;AAEA,WAAO;EACT;AAEA,YAAU,QAAQ,CAAC,MAAM,cAAa;AAIpC,QAAI,CAAC,QAAQ,KAAK,WAAW,GAAG,GAAG;AACjC;IACF;AAGA,QAAI;AAEJ,UAAM,WAAW,UAAU,YAAY,CAAC;AACxC,UAAM,UAAU,UAAU,YAAY,CAAC;AACvC,UAAM,eAAe,UAAU,YAAY,CAAC;AAE5C,QAAI,KAAK,WAAW,YAAY,KAAK,KAAK,WAAW,iBAAiB,GAAG;AACvE,gBAAS;AAGT,YAAM,eAAe;AACrB,UAAK,SAAS,aAAa,KAAK,IAAI,GAAI;AACtC,0BAAkB,YAAY,OAAO,CAAC,GAAG,QAAW,OAAO,SAAS;AACpE,0BAAkB,YAAY,OAAO,CAAC,GAAG,QAAW,OAAO,SAAS;MACtE;AAEA,UAAI,gBAAgB,MAAM;AACxB,cAAM,IAAI,MAAM,sBAAsB;MACxC;AAEA,kBAAY,YAAY;AACxB;IACF;AAEA,QAAI,KAAK,WAAW,cAAc,KAAK,EAAC,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,YAAW;AAC9D,gBAAS;AACT,YAAM,sBAAsB;AAC5B,UAAK,SAAS,oBAAoB,KAAK,IAAI,GAAI;AAC7C,0BAAkB,YAAY,OAAO,CAAC,GAAG,QAAW,OAAO,SAAS;AACpE,0BAAkB,YAAY,OAAO,CAAC,GAAG,QAAW,OAAO,SAAS;MACtE;AAEA,UAAI,gBAAgB,MAAM;AACxB,cAAM,IAAI,MAAM,sBAAsB;MACxC;AAEA,kBAAY,WAAW;AACvB;IACF;AAEA,QACE,CAAC,eACA,CAAC,YAAY,aACZ,eACA,KAAK,WAAW,iBAAiB,KAEjC,QAAQ,WAAW,iBAAiB,KACpC,aAAa,WAAW,gBAAgB,GAC1C;AACA,gBAAS;IACX;AAGA,QAAI,gBAAW,QAAX,gBAAW,SAAA,SAAX,YAAa,UAAU;AACzB;IACF;AAEA,QACE,gBACE,OAAO,OAAO,mBAAmB,YACjC,YAAY,aAAa,YAAY,eAAe,OAAO,kBAC1D,OAAO,OAAO,sBAAsB,YAAY,KAAK,SAAS,OAAO,oBACxE;AACA,kBAAY,WAAW;AACvB,kBAAY,aAAa;AACzB,kBAAY,eAAe;AAC3B,kBAAY,SAAS,CAAA;AACrB,qBAAe;AAEf,YAAM,UACJ,OAAO,OAAO,sBAAsB,aAChC,OAAO,kBAAkB,MAAM,MAAM,IACrC;AACN,iBAAW,OAAO;AAClB;IACF;AAOA,QACG,KAAK,WAAW,iBAAiB,KAAK,QAAQ,WAAW,iBAAiB,KAC1E,KAAK,WAAW,iBAAiB,KAAK,SAAS,WAAW,iBAAiB,GAC5E;AAKA,UACE,eACA,CAAC,YAAY,WACb,KAAK,WAAW,MAAM,MACrB,SAAS,eAAe,MAAM,OAAO,SAAS,IAC/C;AACA,oBAAY,UAAU;AACtB,oBAAY,WAAW,aAAa,YAAY,SAAS,YAAY,QAAQ;AAC7E;MACF;AAMA,UACE,eACA,CAAC,YAAY,WACb,KAAK,WAAW,MAAM,MACrB,SAAS,eAAe,MAAM,OAAO,SAAS,IAC/C;AACA,oBAAY,UAAU;AACtB,oBAAY,WAAW,aAAa,YAAY,SAAS,YAAY,QAAQ;AAC7E;MACF;IACF;AAEA,QACE,gBACC,KAAK,WAAW,gBAAgB,KAC9B,YAAY,aAAa,YAAY,WAAW,YAAY,WAAW,CAAC,eAC3E;AACA,iBAAW,IAAI;AACf;IACF;AAQA,QAAI,iBAAiB,KAAK,WAAW,GAAG,KAAK,KAAK,WAAW,GAAG,KAAK,KAAK,WAAW,GAAG,IAAI;AAC1F,iBAAW,IAAI;AACf;IACF;AAEA,UAAM,yBAAyB,CAAC,gBAAgB,MAAM,SAAS;AAE/D,QAAI,gBAAgB,MAAM;AACxB,YAAM,IAAI,MAAM,sBAAsB;IACxC;AAMA,QAAK,SAAS,QAAQ,KAAK,IAAI,GAAI;AACjC,kBAAY,UAAU,OAAO,CAAC;IAChC,WAAY,SAAS,QAAQ,KAAK,IAAI,GAAI;AACxC,kBAAY,UAAU,OAAO,CAAC;IAChC,WAAY,SAAS,gBAAgB,KAAK,IAAI,GAAI;AAChD,kBAAY,kBAAkB,OAAO,CAAC;AACtC,kBAAY,YAAY;IAC1B,WAAY,SAAS,YAAY,KAAK,IAAI,GAAI;AAC5C,kBAAY,cAAc,OAAO,CAAC;AAClC,kBAAY,QAAQ;IACtB,WAAY,SAAS,SAAS,KAAK,IAAI,GAAI;AACzC,UAAI,wBAAwB;AAC1B,oBAAY,UAAU,OAAO,CAAC;MAChC;AACA,kBAAY,SAAS;IACvB,WAAY,SAAS,OAAO,KAAK,IAAI,GAAI;AACvC,UAAI,wBAAwB;AAC1B,oBAAY,UAAU,OAAO,CAAC;MAChC;AACA,kBAAY,SAAS;IACvB,WAAY,SAAS,WAAW,KAAK,IAAI,GAAI;AAC3C,UAAI,wBAAwB;AAC1B,oBAAY,UAAU,OAAO,CAAC;MAChC;AACA,kBAAY,WAAW;IACzB,WAAY,SAAS,SAAS,KAAK,IAAI,GAAI;AACzC,UAAI,wBAAwB;AAC1B,oBAAY,UAAU,OAAO,CAAC;MAChC;AACA,kBAAY,WAAW;IACzB,WAAY,SAAS,YAAY,KAAK,IAAI,GAAI;AAC5C,kBAAY,WAAW;AACvB,kBAAY,UAAU,YAAY,OAAO,CAAC,GAAG,QAAW,OAAO,SAAS;AACxE,kBAAY,UAAU,YAAY,OAAO,CAAC,GAAG,QAAW,OAAO,SAAS;AACxE,iBAAW,aAAa;IAC1B,WAAW,WAAW,KAAK,IAAI,GAAG;AAChC,kBAAY,WAAW;AACvB,iBAAW,IAAI;IACjB,WAAY,SAAS,gBAAgB,KAAK,IAAI,GAAI;AAChD,kBAAY,sBAAsB,SAAS,OAAO,CAAC,GAAG,EAAE;IAC1D,WAAY,SAAS,mBAAmB,KAAK,IAAI,GAAI;AACnD,kBAAY,oBAAoB,SAAS,OAAO,CAAC,GAAG,EAAE;IACxD,WAAY,SAAS,MAAM,KAAK,IAAI,GAAI;AACtC,kBAAY,iBAAiB,OAAO,CAAC;AACrC,kBAAY,gBAAgB,OAAO,CAAC;AACpC,UAAI,OAAO,CAAC;AAAG,oBAAY,OAAO,OAAO,CAAC;IAC5C,WAAY,SAAS,cAAc,KAAK,IAAI,GAAI;AAC9C,kBAAY,iBAAiB,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAClD,kBAAY,gBAAgB,OAAO,CAAC;IACtC,WAAY,SAAS,aAAa,KAAK,IAAI,GAAI;AAC7C,kBAAY,UAAU,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAC3C,kBAAY,UAAU,OAAO,CAAC;IAChC,WAAY,SAAS,gBAAgB,KAAK,IAAI,GAAI;AAChD,kBAAY,cAAc,OAAO,CAAC;AAClC,kBAAY,QAAQ;IACtB,WAAY,SAAS,oBAAoB,KAAK,IAAI,GAAI;AACpD,kBAAY,kBAAkB,OAAO,CAAC;AACtC,kBAAY,YAAY;IAC1B;EACF,CAAC;AAED,YAAS;AACT,WAAQ;AAER,SAAO;AACT;;;ACheA,SAAS,OAAO;AAAC;AACjB,KAAK,YAAY;AAAA,EACf,MAAM,SAAS,KAAK,WAAW,WAAW;AACxC,QAAI;AACJ,QAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,QAAI,WAAW,QAAQ;AACvB,QAAI,OAAO,YAAY,YAAY;AACjC,iBAAW;AACX,gBAAU,CAAC;AAAA,IACb;AACA,QAAI,OAAO;AACX,aAAS,KAAK,OAAO;AACnB,cAAQ,KAAK,YAAY,OAAO,OAAO;AACvC,UAAI,UAAU;AACZ,mBAAW,WAAY;AACrB,mBAAS,KAAK;AAAA,QAChB,GAAG,CAAC;AACJ,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAGA,gBAAY,KAAK,UAAU,WAAW,OAAO;AAC7C,gBAAY,KAAK,UAAU,WAAW,OAAO;AAC7C,gBAAY,KAAK,YAAY,KAAK,SAAS,WAAW,OAAO,CAAC;AAC9D,gBAAY,KAAK,YAAY,KAAK,SAAS,WAAW,OAAO,CAAC;AAC9D,QAAI,SAAS,UAAU,QACrB,SAAS,UAAU;AACrB,QAAI,aAAa;AACjB,QAAI,gBAAgB,SAAS;AAC7B,QAAI,QAAQ,iBAAiB,MAAM;AACjC,sBAAgB,KAAK,IAAI,eAAe,QAAQ,aAAa;AAAA,IAC/D;AACA,QAAI,oBAAoB,mBAAmB,QAAQ,aAAa,QAAQ,qBAAqB,SAAS,mBAAmB;AACzH,QAAI,sBAAsB,KAAK,IAAI,IAAI;AACvC,QAAI,WAAW,CAAC;AAAA,MACd,QAAQ;AAAA,MACR,eAAe;AAAA,IACjB,CAAC;AAGD,QAAI,SAAS,KAAK,cAAc,SAAS,CAAC,GAAG,WAAW,WAAW,GAAG,OAAO;AAC7E,QAAI,SAAS,CAAC,EAAE,SAAS,KAAK,UAAU,SAAS,KAAK,QAAQ;AAE5D,aAAO,KAAK,YAAY,MAAM,SAAS,CAAC,EAAE,eAAe,WAAW,WAAW,KAAK,eAAe,CAAC;AAAA,IACtG;AAmBA,QAAI,wBAAwB,WAC1B,wBAAwB;AAG1B,aAAS,iBAAiB;AACxB,eAAS,eAAe,KAAK,IAAI,uBAAuB,CAAC,UAAU,GAAG,gBAAgB,KAAK,IAAI,uBAAuB,UAAU,GAAG,gBAAgB,GAAG;AACpJ,YAAI,WAAW;AACf,YAAI,aAAa,SAAS,eAAe,CAAC,GACxC,UAAU,SAAS,eAAe,CAAC;AACrC,YAAI,YAAY;AAEd,mBAAS,eAAe,CAAC,IAAI;AAAA,QAC/B;AACA,YAAI,SAAS;AACb,YAAI,SAAS;AAEX,cAAI,gBAAgB,QAAQ,SAAS;AACrC,mBAAS,WAAW,KAAK,iBAAiB,gBAAgB;AAAA,QAC5D;AACA,YAAI,YAAY,cAAc,WAAW,SAAS,IAAI;AACtD,YAAI,CAAC,UAAU,CAAC,WAAW;AAEzB,mBAAS,YAAY,IAAI;AACzB;AAAA,QACF;AAKA,YAAI,CAAC,aAAa,UAAU,WAAW,SAAS,QAAQ,QAAQ;AAC9D,qBAAW,KAAK,UAAU,SAAS,MAAM,OAAO,GAAG,OAAO;AAAA,QAC5D,OAAO;AACL,qBAAW,KAAK,UAAU,YAAY,OAAO,MAAM,GAAG,OAAO;AAAA,QAC/D;AACA,iBAAS,KAAK,cAAc,UAAU,WAAW,WAAW,cAAc,OAAO;AACjF,YAAI,SAAS,SAAS,KAAK,UAAU,SAAS,KAAK,QAAQ;AAEzD,iBAAO,KAAK,YAAY,MAAM,SAAS,eAAe,WAAW,WAAW,KAAK,eAAe,CAAC;AAAA,QACnG,OAAO;AACL,mBAAS,YAAY,IAAI;AACzB,cAAI,SAAS,SAAS,KAAK,QAAQ;AACjC,oCAAwB,KAAK,IAAI,uBAAuB,eAAe,CAAC;AAAA,UAC1E;AACA,cAAI,SAAS,KAAK,QAAQ;AACxB,oCAAwB,KAAK,IAAI,uBAAuB,eAAe,CAAC;AAAA,UAC1E;AAAA,QACF;AAAA,MACF;AACA;AAAA,IACF;AAMA,QAAI,UAAU;AACZ,OAAC,SAAS,OAAO;AACf,mBAAW,WAAY;AACrB,cAAI,aAAa,iBAAiB,KAAK,IAAI,IAAI,qBAAqB;AAClE,mBAAO,SAAS;AAAA,UAClB;AACA,cAAI,CAAC,eAAe,GAAG;AACrB,iBAAK;AAAA,UACP;AAAA,QACF,GAAG,CAAC;AAAA,MACN,GAAG;AAAA,IACL,OAAO;AACL,aAAO,cAAc,iBAAiB,KAAK,IAAI,KAAK,qBAAqB;AACvE,YAAI,MAAM,eAAe;AACzB,YAAI,KAAK;AACP,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW,SAAS,UAAU,MAAM,OAAO,SAAS,WAAW,SAAS;AACtE,QAAI,OAAO,KAAK;AAChB,QAAI,QAAQ,CAAC,QAAQ,qBAAqB,KAAK,UAAU,SAAS,KAAK,YAAY,SAAS;AAC1F,aAAO;AAAA,QACL,QAAQ,KAAK,SAAS;AAAA,QACtB,eAAe;AAAA,UACb,OAAO,KAAK,QAAQ;AAAA,UACpB;AAAA,UACA;AAAA,UACA,mBAAmB,KAAK;AAAA,QAC1B;AAAA,MACF;AAAA,IACF,OAAO;AACL,aAAO;AAAA,QACL,QAAQ,KAAK,SAAS;AAAA,QACtB,eAAe;AAAA,UACb,OAAO;AAAA,UACP;AAAA,UACA;AAAA,UACA,mBAAmB;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,eAAe,SAAS,cAAc,UAAU,WAAW,WAAW,cAAc,SAAS;AAC3F,QAAI,SAAS,UAAU,QACrB,SAAS,UAAU,QACnB,SAAS,SAAS,QAClB,SAAS,SAAS,cAClB,cAAc;AAChB,WAAO,SAAS,IAAI,UAAU,SAAS,IAAI,UAAU,KAAK,OAAO,UAAU,SAAS,CAAC,GAAG,UAAU,SAAS,CAAC,GAAG,OAAO,GAAG;AACvH;AACA;AACA;AACA,UAAI,QAAQ,mBAAmB;AAC7B,iBAAS,gBAAgB;AAAA,UACvB,OAAO;AAAA,UACP,mBAAmB,SAAS;AAAA,UAC5B,OAAO;AAAA,UACP,SAAS;AAAA,QACX;AAAA,MACF;AAAA,IACF;AACA,QAAI,eAAe,CAAC,QAAQ,mBAAmB;AAC7C,eAAS,gBAAgB;AAAA,QACvB,OAAO;AAAA,QACP,mBAAmB,SAAS;AAAA,QAC5B,OAAO;AAAA,QACP,SAAS;AAAA,MACX;AAAA,IACF;AACA,aAAS,SAAS;AAClB,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,SAAS,OAAO,MAAM,OAAO,SAAS;AAC5C,QAAI,QAAQ,YAAY;AACtB,aAAO,QAAQ,WAAW,MAAM,KAAK;AAAA,IACvC,OAAO;AACL,aAAO,SAAS,SAAS,QAAQ,cAAc,KAAK,YAAY,MAAM,MAAM,YAAY;AAAA,IAC1F;AAAA,EACF;AAAA,EACA,aAAa,SAAS,YAAY,OAAO;AACvC,QAAI,MAAM,CAAC;AACX,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAI,MAAM,CAAC,GAAG;AACZ,YAAI,KAAK,MAAM,CAAC,CAAC;AAAA,MACnB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,WAAW,SAAS,UAAU,OAAO;AACnC,WAAO;AAAA,EACT;AAAA,EACA,UAAU,SAAS,SAAS,OAAO;AACjC,WAAO,MAAM,KAAK,KAAK;AAAA,EACzB;AAAA,EACA,MAAM,SAAS,KAAK,OAAO;AACzB,WAAO,MAAM,KAAK,EAAE;AAAA,EACtB;AAAA,EACA,aAAa,SAAS,YAAY,eAAe;AAC/C,WAAO;AAAA,EACT;AACF;AACA,SAAS,YAAYC,OAAM,eAAe,WAAW,WAAW,iBAAiB;AAG/E,MAAI,aAAa,CAAC;AAClB,MAAI;AACJ,SAAO,eAAe;AACpB,eAAW,KAAK,aAAa;AAC7B,oBAAgB,cAAc;AAC9B,WAAO,cAAc;AACrB,oBAAgB;AAAA,EAClB;AACA,aAAW,QAAQ;AACnB,MAAI,eAAe,GACjB,eAAe,WAAW,QAC1B,SAAS,GACT,SAAS;AACX,SAAO,eAAe,cAAc,gBAAgB;AAClD,QAAI,YAAY,WAAW,YAAY;AACvC,QAAI,CAAC,UAAU,SAAS;AACtB,UAAI,CAAC,UAAU,SAAS,iBAAiB;AACvC,YAAI,QAAQ,UAAU,MAAM,QAAQ,SAAS,UAAU,KAAK;AAC5D,gBAAQ,MAAM,IAAI,SAAUC,QAAO,GAAG;AACpC,cAAI,WAAW,UAAU,SAAS,CAAC;AACnC,iBAAO,SAAS,SAASA,OAAM,SAAS,WAAWA;AAAA,QACrD,CAAC;AACD,kBAAU,QAAQD,MAAK,KAAK,KAAK;AAAA,MACnC,OAAO;AACL,kBAAU,QAAQA,MAAK,KAAK,UAAU,MAAM,QAAQ,SAAS,UAAU,KAAK,CAAC;AAAA,MAC/E;AACA,gBAAU,UAAU;AAGpB,UAAI,CAAC,UAAU,OAAO;AACpB,kBAAU,UAAU;AAAA,MACtB;AAAA,IACF,OAAO;AACL,gBAAU,QAAQA,MAAK,KAAK,UAAU,MAAM,QAAQ,SAAS,UAAU,KAAK,CAAC;AAC7E,gBAAU,UAAU;AAAA,IACtB;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAI,gBAAgB,IAAI,KAAK;AAC7B,SAAS,UAAU,QAAQ,QAAQ,SAAS;AAC1C,SAAO,cAAc,KAAK,QAAQ,QAAQ,OAAO;AACnD;AAEA,SAAS,oBAAoB,MAAM,MAAM;AACvC,MAAI;AACJ,OAAK,IAAI,GAAG,IAAI,KAAK,UAAU,IAAI,KAAK,QAAQ,KAAK;AACnD,QAAI,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG;AACtB,aAAO,KAAK,MAAM,GAAG,CAAC;AAAA,IACxB;AAAA,EACF;AACA,SAAO,KAAK,MAAM,GAAG,CAAC;AACxB;AACA,SAAS,oBAAoB,MAAM,MAAM;AACvC,MAAI;AAKJ,MAAI,CAAC,QAAQ,CAAC,QAAQ,KAAK,KAAK,SAAS,CAAC,KAAK,KAAK,KAAK,SAAS,CAAC,GAAG;AACpE,WAAO;AAAA,EACT;AACA,OAAK,IAAI,GAAG,IAAI,KAAK,UAAU,IAAI,KAAK,QAAQ,KAAK;AACnD,QAAI,KAAK,KAAK,UAAU,IAAI,EAAE,KAAK,KAAK,KAAK,UAAU,IAAI,EAAE,GAAG;AAC9D,aAAO,KAAK,MAAM,CAAC,CAAC;AAAA,IACtB;AAAA,EACF;AACA,SAAO,KAAK,MAAM,CAAC,CAAC;AACtB;AACA,SAAS,cAAc,QAAQ,WAAW,WAAW;AACnD,MAAI,OAAO,MAAM,GAAG,UAAU,MAAM,KAAK,WAAW;AAClD,UAAM,MAAM,UAAU,OAAO,KAAK,UAAU,MAAM,GAAG,6BAA6B,EAAE,OAAO,KAAK,UAAU,SAAS,GAAG,iBAAiB,CAAC;AAAA,EAC1I;AACA,SAAO,YAAY,OAAO,MAAM,UAAU,MAAM;AAClD;AACA,SAAS,cAAc,QAAQ,WAAW,WAAW;AACnD,MAAI,CAAC,WAAW;AACd,WAAO,SAAS;AAAA,EAClB;AACA,MAAI,OAAO,MAAM,CAAC,UAAU,MAAM,KAAK,WAAW;AAChD,UAAM,MAAM,UAAU,OAAO,KAAK,UAAU,MAAM,GAAG,2BAA2B,EAAE,OAAO,KAAK,UAAU,SAAS,GAAG,iBAAiB,CAAC;AAAA,EACxI;AACA,SAAO,OAAO,MAAM,GAAG,CAAC,UAAU,MAAM,IAAI;AAC9C;AACA,SAAS,aAAa,QAAQ,WAAW;AACvC,SAAO,cAAc,QAAQ,WAAW,EAAE;AAC5C;AACA,SAAS,aAAa,QAAQ,WAAW;AACvC,SAAO,cAAc,QAAQ,WAAW,EAAE;AAC5C;AACA,SAAS,eAAe,SAAS,SAAS;AACxC,SAAO,QAAQ,MAAM,GAAG,aAAa,SAAS,OAAO,CAAC;AACxD;AAGA,SAAS,aAAa,GAAG,GAAG;AAE1B,MAAI,SAAS;AACb,MAAI,EAAE,SAAS,EAAE,QAAQ;AACvB,aAAS,EAAE,SAAS,EAAE;AAAA,EACxB;AACA,MAAI,OAAO,EAAE;AACb,MAAI,EAAE,SAAS,EAAE,QAAQ;AACvB,WAAO,EAAE;AAAA,EACX;AAIA,MAAI,MAAM,MAAM,IAAI;AACpB,MAAI,IAAI;AACR,MAAI,CAAC,IAAI;AACT,WAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,QAAI,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG;AAChB,UAAI,CAAC,IAAI,IAAI,CAAC;AAAA,IAChB,OAAO;AACL,UAAI,CAAC,IAAI;AAAA,IACX;AACA,WAAO,IAAI,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG;AAC5B,UAAI,IAAI,CAAC;AAAA,IACX;AACA,QAAI,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG;AAChB;AAAA,IACF;AAAA,EACF;AAEA,MAAI;AACJ,WAAS,IAAI,QAAQ,IAAI,EAAE,QAAQ,KAAK;AACtC,WAAO,IAAI,KAAK,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG;AAC5B,UAAI,IAAI,CAAC;AAAA,IACX;AACA,QAAI,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG;AAChB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAkCA,IAAI,oBAAoB;AA2BxB,IAAI,8BAA8B,IAAI,OAAO,IAAI,OAAO,mBAAmB,YAAY,EAAE,OAAO,mBAAmB,GAAG,GAAG,IAAI;AAC7H,IAAI,WAAW,IAAI,KAAK;AACxB,SAAS,SAAS,SAAU,MAAM,OAAO,SAAS;AAChD,MAAI,QAAQ,YAAY;AACtB,WAAO,KAAK,YAAY;AACxB,YAAQ,MAAM,YAAY;AAAA,EAC5B;AACA,SAAO,KAAK,KAAK,MAAM,MAAM,KAAK;AACpC;AACA,SAAS,WAAW,SAAU,OAAO;AACnC,MAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,MAAI;AACJ,MAAI,QAAQ,eAAe;AACzB,QAAI,QAAQ,cAAc,gBAAgB,EAAE,eAAe,QAAQ;AACjE,YAAM,IAAI,MAAM,wDAAwD;AAAA,IAC1E;AACA,YAAQ,MAAM,KAAK,QAAQ,cAAc,QAAQ,KAAK,GAAG,SAAU,SAAS;AAC1E,aAAO,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,OAAO;AACL,YAAQ,MAAM,MAAM,2BAA2B,KAAK,CAAC;AAAA,EACvD;AACA,MAAI,SAAS,CAAC;AACd,MAAI,WAAW;AACf,QAAM,QAAQ,SAAU,MAAM;AAC5B,QAAI,KAAK,KAAK,IAAI,GAAG;AACnB,UAAI,YAAY,MAAM;AACpB,eAAO,KAAK,IAAI;AAAA,MAClB,OAAO;AACL,eAAO,KAAK,OAAO,IAAI,IAAI,IAAI;AAAA,MACjC;AAAA,IACF,WAAW,KAAK,KAAK,QAAQ,GAAG;AAC9B,UAAI,OAAO,OAAO,SAAS,CAAC,KAAK,UAAU;AACzC,eAAO,KAAK,OAAO,IAAI,IAAI,IAAI;AAAA,MACjC,OAAO;AACL,eAAO,KAAK,WAAW,IAAI;AAAA,MAC7B;AAAA,IACF,OAAO;AACL,aAAO,KAAK,IAAI;AAAA,IAClB;AACA,eAAW;AAAA,EACb,CAAC;AACD,SAAO;AACT;AACA,SAAS,OAAO,SAAU,QAAQ;AAMhC,SAAO,OAAO,IAAI,SAAU,OAAO,GAAG;AACpC,QAAI,KAAK,GAAG;AACV,aAAO;AAAA,IACT,OAAO;AACL,aAAO,MAAM,QAAQ,QAAQ,EAAE;AAAA,IACjC;AAAA,EACF,CAAC,EAAE,KAAK,EAAE;AACZ;AACA,SAAS,cAAc,SAAU,SAAS,SAAS;AACjD,MAAI,CAAC,WAAW,QAAQ,mBAAmB;AACzC,WAAO;AAAA,EACT;AACA,MAAI,WAAW;AAGf,MAAI,YAAY;AAChB,MAAI,WAAW;AACf,UAAQ,QAAQ,SAAU,QAAQ;AAChC,QAAI,OAAO,OAAO;AAChB,kBAAY;AAAA,IACd,WAAW,OAAO,SAAS;AACzB,iBAAW;AAAA,IACb,OAAO;AACL,UAAI,aAAa,UAAU;AAEzB,wCAAgC,UAAU,UAAU,WAAW,MAAM;AAAA,MACvE;AACA,iBAAW;AACX,kBAAY;AACZ,iBAAW;AAAA,IACb;AAAA,EACF,CAAC;AACD,MAAI,aAAa,UAAU;AACzB,oCAAgC,UAAU,UAAU,WAAW,IAAI;AAAA,EACrE;AACA,SAAO;AACT;AAWA,SAAS,gCAAgC,WAAW,UAAU,WAAW,SAAS;AA2ChF,MAAI,YAAY,WAAW;AACzB,QAAI,cAAc,SAAS,MAAM,MAAM,MAAM,EAAE,CAAC;AAChD,QAAI,cAAc,SAAS,MAAM,MAAM,MAAM,EAAE,CAAC;AAChD,QAAI,cAAc,UAAU,MAAM,MAAM,MAAM,EAAE,CAAC;AACjD,QAAI,cAAc,UAAU,MAAM,MAAM,MAAM,EAAE,CAAC;AACjD,QAAI,WAAW;AACb,UAAI,iBAAiB,oBAAoB,aAAa,WAAW;AACjE,gBAAU,QAAQ,cAAc,UAAU,OAAO,aAAa,cAAc;AAC5E,eAAS,QAAQ,aAAa,SAAS,OAAO,cAAc;AAC5D,gBAAU,QAAQ,aAAa,UAAU,OAAO,cAAc;AAAA,IAChE;AACA,QAAI,SAAS;AACX,UAAI,iBAAiB,oBAAoB,aAAa,WAAW;AACjE,cAAQ,QAAQ,cAAc,QAAQ,OAAO,aAAa,cAAc;AACxE,eAAS,QAAQ,aAAa,SAAS,OAAO,cAAc;AAC5D,gBAAU,QAAQ,aAAa,UAAU,OAAO,cAAc;AAAA,IAChE;AAAA,EACF,WAAW,WAAW;AAOpB,QAAI,WAAW;AACb,gBAAU,QAAQ,UAAU,MAAM,QAAQ,QAAQ,EAAE;AAAA,IACtD;AACA,QAAI,SAAS;AACX,cAAQ,QAAQ,QAAQ,MAAM,QAAQ,QAAQ,EAAE;AAAA,IAClD;AAAA,EAEF,WAAW,aAAa,SAAS;AAC/B,QAAI,YAAY,QAAQ,MAAM,MAAM,MAAM,EAAE,CAAC,GAC3C,aAAa,SAAS,MAAM,MAAM,MAAM,EAAE,CAAC,GAC3C,WAAW,SAAS,MAAM,MAAM,MAAM,EAAE,CAAC;AAI3C,QAAI,aAAa,oBAAoB,WAAW,UAAU;AAC1D,aAAS,QAAQ,aAAa,SAAS,OAAO,UAAU;AAKxD,QAAI,WAAW,oBAAoB,aAAa,WAAW,UAAU,GAAG,QAAQ;AAChF,aAAS,QAAQ,aAAa,SAAS,OAAO,QAAQ;AACtD,YAAQ,QAAQ,cAAc,QAAQ,OAAO,WAAW,QAAQ;AAIhE,cAAU,QAAQ,cAAc,UAAU,OAAO,WAAW,UAAU,MAAM,GAAG,UAAU,SAAS,SAAS,MAAM,CAAC;AAAA,EACpH,WAAW,SAAS;AAIlB,QAAI,kBAAkB,QAAQ,MAAM,MAAM,MAAM,EAAE,CAAC;AACnD,QAAI,mBAAmB,SAAS,MAAM,MAAM,MAAM,EAAE,CAAC;AACrD,QAAI,UAAU,eAAe,kBAAkB,eAAe;AAC9D,aAAS,QAAQ,aAAa,SAAS,OAAO,OAAO;AAAA,EACvD,WAAW,WAAW;AAIpB,QAAI,oBAAoB,UAAU,MAAM,MAAM,MAAM,EAAE,CAAC;AACvD,QAAI,mBAAmB,SAAS,MAAM,MAAM,MAAM,EAAE,CAAC;AACrD,QAAI,WAAW,eAAe,mBAAmB,gBAAgB;AACjE,aAAS,QAAQ,aAAa,SAAS,OAAO,QAAQ;AAAA,EACxD;AACF;AACA,IAAI,oBAAoB,IAAI,KAAK;AACjC,kBAAkB,WAAW,SAAU,OAAO;AAM5C,MAAIE,SAAQ,IAAI,OAAO,cAAc,OAAO,mBAAmB,qBAAqB,EAAE,OAAO,mBAAmB,GAAG,GAAG,IAAI;AAC1H,SAAO,MAAM,MAAMA,MAAK,KAAK,CAAC;AAChC;AACA,SAAS,mBAAmB,QAAQ,QAAQ,SAAS;AACnD,SAAO,kBAAkB,KAAK,QAAQ,QAAQ,OAAO;AACvD;AAgBA,IAAI,WAAW,IAAI,KAAK;AACxB,SAAS,WAAW,SAAU,OAAO,SAAS;AAC5C,MAAI,QAAQ,iBAAiB;AAE3B,YAAQ,MAAM,QAAQ,SAAS,IAAI;AAAA,EACrC;AACA,MAAI,WAAW,CAAC,GACd,mBAAmB,MAAM,MAAM,WAAW;AAG5C,MAAI,CAAC,iBAAiB,iBAAiB,SAAS,CAAC,GAAG;AAClD,qBAAiB,IAAI;AAAA,EACvB;AAGA,WAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAChD,QAAI,OAAO,iBAAiB,CAAC;AAC7B,QAAI,IAAI,KAAK,CAAC,QAAQ,gBAAgB;AACpC,eAAS,SAAS,SAAS,CAAC,KAAK;AAAA,IACnC,OAAO;AACL,eAAS,KAAK,IAAI;AAAA,IACpB;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,SAAS,SAAU,MAAM,OAAO,SAAS;AAQhD,MAAI,QAAQ,kBAAkB;AAC5B,QAAI,CAAC,QAAQ,kBAAkB,CAAC,KAAK,SAAS,IAAI,GAAG;AACnD,aAAO,KAAK,KAAK;AAAA,IACnB;AACA,QAAI,CAAC,QAAQ,kBAAkB,CAAC,MAAM,SAAS,IAAI,GAAG;AACpD,cAAQ,MAAM,KAAK;AAAA,IACrB;AAAA,EACF,WAAW,QAAQ,sBAAsB,CAAC,QAAQ,gBAAgB;AAChE,QAAI,KAAK,SAAS,IAAI,GAAG;AACvB,aAAO,KAAK,MAAM,GAAG,EAAE;AAAA,IACzB;AACA,QAAI,MAAM,SAAS,IAAI,GAAG;AACxB,cAAQ,MAAM,MAAM,GAAG,EAAE;AAAA,IAC3B;AAAA,EACF;AACA,SAAO,KAAK,UAAU,OAAO,KAAK,MAAM,MAAM,OAAO,OAAO;AAC9D;AAkBA,IAAI,eAAe,IAAI,KAAK;AAC5B,aAAa,WAAW,SAAU,OAAO;AACvC,SAAO,MAAM,MAAM,uBAAuB;AAC5C;AAKA,IAAI,UAAU,IAAI,KAAK;AACvB,QAAQ,WAAW,SAAU,OAAO;AAClC,SAAO,MAAM,MAAM,eAAe;AACpC;AAwCA,SAAS,QAAQ,GAAG;AAClB;AAEA,SAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUC,IAAG;AAChG,WAAO,OAAOA;AAAA,EAChB,IAAI,SAAUA,IAAG;AACf,WAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,EACpH,GAAG,QAAQ,CAAC;AACd;AAyCA,IAAI,WAAW,IAAI,KAAK;AAGxB,SAAS,kBAAkB;AAC3B,SAAS,WAAW,SAAS;AAC7B,SAAS,YAAY,SAAU,OAAO,SAAS;AAC7C,MAAI,uBAAuB,QAAQ,sBACjC,wBAAwB,QAAQ,mBAChC,oBAAoB,0BAA0B,SAAS,SAAU,GAAG,GAAG;AACrE,WAAO,OAAO,MAAM,cAAc,uBAAuB;AAAA,EAC3D,IAAI;AACN,SAAO,OAAO,UAAU,WAAW,QAAQ,KAAK,UAAU,aAAa,OAAO,MAAM,MAAM,iBAAiB,GAAG,mBAAmB,IAAI;AACvI;AACA,SAAS,SAAS,SAAU,MAAM,OAAO,SAAS;AAChD,SAAO,KAAK,UAAU,OAAO,KAAK,UAAU,KAAK,QAAQ,cAAc,IAAI,GAAG,MAAM,QAAQ,cAAc,IAAI,GAAG,OAAO;AAC1H;AAOA,SAAS,aAAa,KAAK,OAAO,kBAAkB,UAAUC,MAAK;AACjE,UAAQ,SAAS,CAAC;AAClB,qBAAmB,oBAAoB,CAAC;AACxC,MAAI,UAAU;AACZ,UAAM,SAASA,MAAK,GAAG;AAAA,EACzB;AACA,MAAI;AACJ,OAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACpC,QAAI,MAAM,CAAC,MAAM,KAAK;AACpB,aAAO,iBAAiB,CAAC;AAAA,IAC3B;AAAA,EACF;AACA,MAAI;AACJ,MAAI,qBAAqB,OAAO,UAAU,SAAS,KAAK,GAAG,GAAG;AAC5D,UAAM,KAAK,GAAG;AACd,uBAAmB,IAAI,MAAM,IAAI,MAAM;AACvC,qBAAiB,KAAK,gBAAgB;AACtC,SAAK,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK,GAAG;AAClC,uBAAiB,CAAC,IAAI,aAAa,IAAI,CAAC,GAAG,OAAO,kBAAkB,UAAUA,IAAG;AAAA,IACnF;AACA,UAAM,IAAI;AACV,qBAAiB,IAAI;AACrB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,IAAI,QAAQ;AACrB,UAAM,IAAI,OAAO;AAAA,EACnB;AACA,MAAI,QAAQ,GAAG,MAAM,YAAY,QAAQ,MAAM;AAC7C,UAAM,KAAK,GAAG;AACd,uBAAmB,CAAC;AACpB,qBAAiB,KAAK,gBAAgB;AACtC,QAAI,aAAa,CAAC,GAChB;AACF,SAAK,QAAQ,KAAK;AAEhB,UAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,GAAG;AACnD,mBAAW,KAAK,IAAI;AAAA,MACtB;AAAA,IACF;AACA,eAAW,KAAK;AAChB,SAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK,GAAG;AACzC,aAAO,WAAW,CAAC;AACnB,uBAAiB,IAAI,IAAI,aAAa,IAAI,IAAI,GAAG,OAAO,kBAAkB,UAAU,IAAI;AAAA,IAC1F;AACA,UAAM,IAAI;AACV,qBAAiB,IAAI;AAAA,EACvB,OAAO;AACL,uBAAmB;AAAA,EACrB;AACA,SAAO;AACT;AAEA,IAAI,YAAY,IAAI,KAAK;AACzB,UAAU,WAAW,SAAU,OAAO;AACpC,SAAO,MAAM,MAAM;AACrB;AACA,UAAU,OAAO,UAAU,cAAc,SAAU,OAAO;AACxD,SAAO;AACT;;;ACr3BM,SAAU,YAAY,GAAW,GAAS;AAC9C,MAAI,EAAE,WAAW,GAAG;AAClB,WAAO,EAAE;EACX;AACA,MAAI,EAAE,WAAW,GAAG;AAClB,WAAO,EAAE;EACX;AAEA,QAAM,SAAS,CAAA;AAGf,MAAI;AACJ,OAAK,IAAI,GAAG,KAAK,EAAE,QAAQ,KAAK;AAC9B,WAAO,CAAC,IAAI,CAAC,CAAC;EAChB;AAGA,MAAI;AACJ,OAAK,IAAI,GAAG,KAAK,EAAE,QAAQ,KAAK;AAC9B,WAAO,CAAC,EAAE,CAAC,IAAI;EACjB;AAGA,OAAK,IAAI,GAAG,KAAK,EAAE,QAAQ,KAAK;AAC9B,SAAK,IAAI,GAAG,KAAK,EAAE,QAAQ,KAAK;AAC9B,UAAI,EAAE,OAAO,IAAI,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,GAAG;AACvC,eAAO,CAAC,EAAE,CAAC,IAAI,OAAO,IAAI,CAAC,EAAE,IAAI,CAAC;MACpC,OAAO;AACL,eAAO,CAAC,EAAE,CAAC,IAAI,KAAK,IAClB,OAAO,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,GACvB,KAAK,IACH,OAAO,CAAC,EAAE,IAAI,CAAC,IAAI,GACnB,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CACrB;MAEL;IACF;EACF;AAEA,SAAO,OAAO,EAAE,MAAM,EAAE,EAAE,MAAM;AAClC;AAIM,SAAU,cAAiB,KAAyB;AACxD,SAAO,CAAC,GAAM,MAAgB;AAC5B,UAAM,SAAS,IAAI,CAAC,EAAE,KAAI;AAC1B,UAAM,SAAS,IAAI,CAAC,EAAE,KAAI;AAC1B,UAAM,MAAM,YAAY,QAAQ,MAAM;AACtC,WAAO,OAAO,OAAO,SAAS,OAAO;EACvC;AACF;AAIM,SAAU,aAAgBC,WAAgC;AAC9D,WAAS,cAAc,GAAQ,GAAQ,QAA6B,oBAAI,IAAG,GAAE;AAC3E,QAAI,gBAAgB;AACpB,QAAI;AAEJ,aAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,EAAE,GAAG;AACjC,eAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,EAAE,GAAG;AACjC,cAAM,WAAW,KAAK,UAAU,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC5C,YAAI;AACJ,YAAI,EAAE,MAAM,IAAI,QAAQ,MAAM,KAAK,MAAM,IAAI,QAAQ,KAAK;AACxD,eAAKA,UAAS,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACxB,gBAAM,IAAI,UAAU,EAAE;QACxB;AACA,YAAI,KAAK,eAAe;AACtB,0BAAgB;AAChB,sBAAY,EAAE,QAAQ,GAAG,QAAQ,GAAG,OAAO,cAAa;QAC1D;MACF;IACF;AAEA,WAAO;EACT;AAEA,WAAS,MAAM,GAAQ,GAAQ,QAAQ,GAAG,QAA6B,oBAAI,IAAG,GAAE;AAC9E,UAAM,KAAK,cAAc,GAAG,GAAG,KAAK;AAEpC,QAAI,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,GAAG;AAClC,aAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IAChB;AAEA,UAAM,KAAK,EAAE,MAAM,GAAG,GAAG,MAAM;AAC/B,UAAM,KAAK,EAAE,MAAM,GAAG,GAAG,MAAM;AAC/B,UAAM,SAAS,CAAC,EAAE,GAAG,MAAM,CAAC;AAC5B,UAAM,SAAS,CAAC,EAAE,GAAG,MAAM,CAAC;AAC5B,UAAM,QAAQ,GAAG,SAAS;AAC1B,UAAM,QAAQ,GAAG,SAAS;AAC1B,UAAM,KAAK,EAAE,MAAM,KAAK;AACxB,UAAM,KAAK,EAAE,MAAM,KAAK;AAExB,UAAM,SAAS,MAAM,IAAI,IAAI,QAAQ,GAAG,KAAK;AAC7C,UAAM,aAAa,MAAM,QAAQ,QAAQ,QAAQ,GAAG,KAAK;AACzD,UAAM,SAAS,MAAM,IAAI,IAAI,QAAQ,GAAG,KAAK;AAC7C,QAAI,SAAS;AAEb,QAAI,GAAG,SAAS,KAAK,GAAG,SAAS,GAAG;AAClC,eAAS,OAAO,OAAO,MAAM;IAC/B;AAEA,QAAI,EAAE,SAAS,SAAS,EAAE,SAAS,OAAO;AACxC,eAAS,OAAO,OAAO,MAAM;IAC/B;AAEA,WAAO;EACT;AAEA,SAAO;AACT;;;AChHO,IAAM,eAA8C;EACzD,SAAS;EACT,SAAS;EACT,SAAS;EACT,MAAM;EACN,gBAAgB;EAChB,gBAAgB;;AAsBX,IAAM,sBAAsB;EACjC,UAAU,iBAAiB;EAC3B,qBAAqB;EACrB,wBAAwB;EACxB,WAAW,cAAc;EACzB,aAAa,gBAAgB;;AAG/B,IAAM,YAAY;AAClB,IAAM,WAAmB,cAAc,CAAC,WAA0B,OAAO,KAAK;AAC9E,IAAM,UAAkB,aAAa,QAAQ;AAE7C,SAAS,cAAc,MAAY;AACjC,SAAO,KAAK,QAAQ,UAAU,MAAM;AACtC;AAEA,SAAS,kBAAkB,MAAY;AACrC,SAAO,KAAK,QAAQ,kCAAkC,EAAE;AAC1D;AAEA,SAAS,kBAAkB,MAAY;AACrC,SAAO,KAAK,QAAQ,kCAAkC,EAAE;AAC1D;AAKM,SAAU,WAAW,UAAkB;AAC3C,UAAQ,UAAU;IAChB,KAAK,SAAS;AACZ,aAAO,aAAa;IACtB,KAAK,SAAS;AACZ,aAAO,aAAa;IACtB,KAAK,SAAS;AACZ,aAAO,aAAa;EACxB;AACF;AAEM,SAAU,iBAAiB,aAA4B;AAC3D,UAAQ,aAAa;IACnB,KAAK,gBAAgB;AACnB,aAAO;IACT,KAAK,gBAAgB;AACnB,aAAO;IACT,KAAK,gBAAgB;IACrB;AACE,aAAO;EACX;AACF;AAKA,SAAS,aAAa,YAAmB;AACvC,SAAO,aAAa,IAAI;AAC1B;AAMM,SAAU,cAAc,KAAW;AACvC,SAAO,IACJ,MAAM,CAAC,EACP,QAAQ,MAAM,OAAO,EACrB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,QAAQ,EACtB,QAAQ,MAAM,QAAQ,EACtB,QAAQ,OAAO,QAAQ;AAC5B;AAKM,SAAU,gBAAgB,MAAc,YAAqB,SAAS,MAAI;AAC9E,QAAM,eAAe,aAAa,UAAU;AAC5C,SAAO;IACL,QAAQ,KAAK,UAAU,GAAG,YAAY;IACtC,SAAS,SAAS,cAAc,KAAK,UAAU,YAAY,CAAC,IAAI,KAAK,UAAU,YAAY;;AAE/F;AAaM,SAAU,aAAa,MAAkB;AAE7C,QAAM,cAAc,UAAU,KAAK,OAAO;AAC1C,QAAM,cAAc,UAAU,KAAK,OAAO;AAE1C,MAAI,gBAAgB,eAAe,CAAC,cAAc,WAAW,KAAK,CAAC,cAAc,WAAW,GAAG;AAC7F,UAAM,cAAc,CAAA;AACpB,UAAM,cAAc,CAAA;AAEpB,UAAM,mBAAmB,YAAY,MAAM,SAAS;AACpD,UAAM,mBAAmB,YAAY,MAAM,SAAS;AAEpD,UAAM,uBAAuB,iBAAiB;AAC9C,UAAM,uBAAuB,iBAAiB;AAE9C,QAAI,IAAI;AACR,QAAI,IAAI,uBAAuB;AAC/B,QAAI,IAAI,uBAAuB;AAE/B,WAAO,IAAI,KAAK,IAAI,GAAG;AACrB,UAAI,iBAAiB,CAAC,MAAM,iBAAiB,CAAC,GAAG;AAC/C,oBAAY,KAAK,iBAAiB,CAAC,CAAC;AACpC,aAAK;MACP,OAAO;AACL;MACF;IACF;AAEA,WAAO,IAAI,KAAK,IAAI,GAAG;AACrB,UAAI,iBAAiB,CAAC,MAAM,iBAAiB,CAAC,GAAG;AAC/C,oBAAY,QAAQ,iBAAiB,CAAC,CAAC;AACvC,aAAK;AACL,aAAK;MACP,OAAO;AACL;MACF;IACF;AAEA,UAAM,cAAc,YAAY,KAAK,SAAS;AAC9C,UAAM,cAAc,YAAY,KAAK,SAAS;AAE9C,UAAM,mBAAmB,iBAAiB,MAAM,GAAG,IAAI,CAAC,EAAE,KAAK,SAAS;AACxE,UAAM,mBAAmB,iBAAiB,MAAM,GAAG,IAAI,CAAC,EAAE,KAAK,SAAS;AAExE,QAAI,YAAY,UAAU,YAAY,QAAQ;AAC5C,aACE,cAAc,YAAY,MAAM,mBAAmB,QAAQ,mBAAmB,MAAM,YAAY;IAEpG,WAAW,YAAY,QAAQ;AAC7B,aAAO,cAAc,YAAY,MAAM,mBAAmB,QAAQ,mBAAmB;IACvF,WAAW,YAAY,QAAQ;AAC7B,aAAO,MAAM,mBAAmB,QAAQ,mBAAmB,MAAM,YAAY;IAC/E;AAEA,WAAO,cAAc,QAAQ;EAC/B,WAAW,CAAC,cAAc,WAAW,GAAG;AACtC,WAAO;EACT,OAAO;AACL,WAAO;EACT;AACF;AAKM,SAAU,UAAU,MAAkB;AAC1C,SAAO,OAAO,SAAS,aAAa,IAAI,CAAC,EAAE,SAAQ,EAAG,MAAM,EAAE,CAAC;AACjE;AAKM,SAAU,YAAY,MAAc;AACxC,MAAI,eAAe;AAEnB,MAAI,KAAK,UAAU;AACjB,mBAAe;EACjB,WAAW,KAAK,QAAQ;AACtB,mBAAe;EACjB,WAAW,KAAK,OAAO;AACrB,mBAAe;EACjB,WAAW,KAAK,WAAW;AACzB,mBAAe;EACjB,WAAW,KAAK,YAAY,KAAK,SAAS;AAExC,mBAAe;EACjB;AAEA,SAAO;AACT;AAKM,SAAU,cACd,WACA,WACA,YACA,SAAuB,CAAA,GAAE;AAEzB,QAAM,EAAE,UAAU,wBAAwB,qBAAqB,UAAS,IAAE,OAAA,OAAA,OAAA,OAAA,CAAA,GAAQ,mBAAmB,GAAK,MAAM;AAEhH,QAAM,QAAQ,gBAAgB,WAAW,YAAY,KAAK;AAC1D,QAAM,QAAQ,gBAAgB,WAAW,YAAY,KAAK;AAE1D,MAAI,MAAM,QAAQ,SAAS,0BAA0B,MAAM,QAAQ,SAAS,wBAAwB;AAClG,WAAO;MACL,SAAS;QACP,QAAQ,MAAM;QACd,SAAS,cAAc,MAAM,OAAO;;MAEtC,SAAS;QACP,QAAQ,MAAM;QACd,SAAS,cAAc,MAAM,OAAO;;;EAG1C;AAEA,QAAMC,QACJ,cAAc,SACH,UAAU,MAAM,SAAS,MAAM,OAAO,IACtC,mBAAmB,MAAM,SAAS,MAAM,OAAO;AAE5D,QAAM,eAAgC,CAAA;AACtC,MAAI,cAAc,UAAU,aAAa,SAAS;AAChD,UAAM,UAAUA,MAAK,OAAO,aAAW,QAAQ,OAAO;AACtD,UAAM,QAAQA,MAAK,OAAO,aAAW,QAAQ,KAAK;AAClD,UAAM,SAAS,QAAQ,OAAO,OAAO;AACrC,WAAO,QAAQ,WAAQ;AACrB,UAAI,MAAM,CAAC,EAAE,WAAW,KAAK,MAAM,CAAC,EAAE,WAAW,GAAG;AAClD,cAAM,OAAO,SAAS,MAAM,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;AAC9C,YAAI,OAAO,qBAAqB;AAC9B,uBAAa,KAAK,MAAM,CAAC,EAAE,CAAC,CAAC;AAC7B,uBAAa,KAAK,MAAM,CAAC,EAAE,CAAC,CAAC;QAC/B;MACF;IACF,CAAC;EACH;AAEA,QAAM,kBAAkBA,MAAK,OAAO,CAACC,kBAAiB,SAAQ;AAC5D,UAAM,WAAW,KAAK,QAAQ,QAAQ,KAAK,UAAU,QAAQ;AAC7D,UAAM,WAAW,aAAa,QAAQ,IAAI,IAAI,KAAK,wBAAwB;AAC3E,UAAM,eAAe,cAAc,KAAK,KAAK;AAE7C,WAAO,aAAa,OAChB,GAAGA,gBAAe,IAAI,QAAQ,GAAG,QAAQ,IAAI,YAAY,KAAK,QAAQ,MACtE,GAAGA,gBAAe,GAAG,YAAY;EACvC,GAAG,EAAE;AAEL,SAAO;IACL,SAAS;MACP,QAAQ,MAAM;MACd,SAAS,kBAAkB,eAAe;;IAE5C,SAAS;MACP,QAAQ,MAAM;MACd,SAAS,kBAAkB,eAAe;;;AAGhD;;;AC3SA,IAAM,oBAAoB;AAC1B,IAAM,yBAAyB;AAMxB,IAAM,gCAAgC;EAC3C,aAAyB,oBAAoB;;AAGzC,IAAO,mBAAP,MAAuB;EAI3B,YAAY,YAA0B,SAAiC,CAAA,GAAE;AACvE,SAAK,aAAa;AAClB,SAAK,SAAM,OAAA,OAAA,OAAA,OAAA,CAAA,GAAQ,6BAA6B,GAAK,MAAM;EAC7D;EAEA,OAAO,WAAqB;AAC1B,UAAM,QAAQ,UACX,IAAI,UACH,KAAK,WAAW,OACd,mBACA,QACA;MACE,YAAwB,UAAU,IAAI;MACtC,SAAS,KAAK;MACd,SAAS,KAAK;MACd,UAAsB,aAAa,IAAI;MACvC,cAAc,MAAM,KAAK;MACzB,YAAY,MAAM,KAAK;OAEzB;MACE,UAAU,KAAK,WAAW,SAAS,wBAAoC,YAAY,IAAI,CAAC;KACzF,CACF,EAEF,KAAK,IAAI;AAEZ,WAAO,KAAK,WAAW,OAAO,mBAAmB,WAAW;MAC1D,aAAyB,iBAAiB,KAAK,OAAO,WAAW;MACjE,aAAa,UAAU;MACvB;KACD;EACH;;;;AC7BK,IAAM,kCAA+B,OAAA,OAAA,OAAA,OAAA,CAAA,GAC3B,mBAAmB,GAAA,EAClC,wBAAwB,OACxB,wBAAwB,MACxB,iCAAiC,IAAG,CAAA;AAGtC,IAAM,uBAAuB;AAC7B,IAAMC,qBAAoB;AAC1B,IAAMC,0BAAyB;AAC/B,IAAM,wBAAwB;AAE9B,IAAqB,qBAArB,MAAuC;EAIrC,YAAY,YAA0B,SAAmC,CAAA,GAAE;AACzE,SAAK,aAAa;AAClB,SAAK,SAAM,OAAA,OAAA,OAAA,OAAA,CAAA,GAAQ,+BAA+B,GAAK,MAAM;EAC/D;EAEA,OAAO,WAAqB;AAC1B,UAAM,YAAY,UACf,IAAI,UAAO;AACV,UAAI;AACJ,UAAI,KAAK,OAAO,QAAQ;AACtB,gBAAQ,KAAK,iBAAiB,IAAI;MACpC,OAAO;AACL,gBAAQ,KAAK,kBAAiB;MAChC;AACA,aAAO,KAAK,iBAAiB,MAAM,KAAK;IAC1C,CAAC,EACA,KAAK,IAAI;AAEZ,WAAO,KAAK,WAAW,OAAO,sBAAsB,WAAW;MAC7D,aAAyB,iBAAiB,KAAK,OAAO,WAAW;MACjE,SAAS;KACV;EACH;EAEA,iBAAiB,MAAgB,OAAa;AAC5C,QAAI,KAAK,OAAO,0BAA0B,MAAM,QAAQ,KAAK,MAAM,KAAK,KAAK,OAAO,WAAW;AAAG,aAAO;AAEzG,UAAM,mBAAmB,KAAK,WAAW,SAASD,oBAAmB,WAAW;AAChF,UAAM,mBAAmB,KAAK,WAAW,SAAS,sBAAsB,WAAW;AACnF,UAAM,mBAAmB,KAAK,WAAW,SAASC,yBAAwB,MAAM;AAChF,UAAM,kBAAkB,KAAK,WAAW,SAAS,uBAAmC,YAAY,IAAI,CAAC;AAErG,WAAO,iBAAiB,OAAO;MAC7B;MACA,YAAwB,UAAU,IAAI;MACtC;MACA,UAAU,iBAAiB,OACzB;QACE,cAA0B,aAAa,IAAI;SAE7C;QACE,UAAU;QACV,SAAS;OACV;KAEJ;EACH;EAEA,oBAAiB;AACf,WAAO,KAAK,WAAW,OAAO,sBAAsB,cAAc;MAChE,cAAc;MACd;KACD;EACH;EAEA,iBAAiB,MAAc;AAC7B,UAAMC,WAAkB,aACd,cAAc,CAAC,MAA4B,gBAAgB,EAAE,SAAS,KAAK,UAAU,EAAE,OAAO,CAAC;AAGzG,WAAO,KAAK,OACT,IAAI,WAAQ;AACX,UAAI,QAAQ,KAAK,WAAW,OAAO,sBAAsB,gBAAgB;QACvE;QACA,aAAa,KAAK,WAAW,MAAM,SAAqB,cAAc,MAAM,MAAM;QAClF,WAAW;QACX,cAAc;OACf;AAED,WAAK,mBAAmB,KAAK,EAAE,QAAQ,CAAC,CAAC,cAAc,UAAU,QAAQ,MAAK;AAC5E,YAAI,SAAS,UAAU,SAAS,UAAU,CAAC,aAAa,QAAQ;AAC9D,eAAK,qBAAqB,UAAU,UAAUA,QAAO,EAAE,IAAI,CAAC,CAACC,WAAUC,SAAQ,MAAK;AAClF,kBAAM,EAAE,MAAM,MAAK,IAAK,KAAK,oBAAoB,MAAM,KAAK,YAAYD,WAAUC,SAAQ;AAC1F,qBAAS;AACT,qBAAS;UACX,CAAC;QACH,WAAW,aAAa,QAAQ;AAC9B,uBAAa,QAAQ,UAAO;AAC1B,kBAAM,EAAE,QAAQ,QAAO,IAAiB,gBAAgB,KAAK,SAAS,KAAK,UAAU;AACrF,qBAAS,KAAK,uBAAuB,MAAM;cACzC,MAAkB,aAAa;cAC/B;cACA;cACA,WAAW,KAAK;cAChB,WAAW,KAAK;aACjB;UACH,CAAC;QACH,WAAW,SAAS,UAAU,SAAS,QAAQ;AAC7C,gBAAM,EAAE,MAAM,MAAK,IAAK,KAAK,oBAAoB,MAAM,KAAK,YAAY,UAAU,QAAQ;AAC1F,mBAAS;AACT,mBAAS;QACX,OAAO;AACL,kBAAQ,MAAM,0DAA0D,cAAc,UAAU,QAAQ;QAC1G;MACF,CAAC;AAED,aAAO;IACT,CAAC,EACA,KAAK,IAAI;EACd;EAEA,mBAAmB,OAAgB;AACjC,UAAM,mBAAmC,CAAA;AAEzC,QAAI,WAAkD,CAAA;AACtD,QAAI,WAAmD,CAAA;AAEvD,aAAS,IAAI,GAAG,IAAI,MAAM,MAAM,QAAQ,KAAK;AAC3C,YAAM,WAAW,MAAM,MAAM,CAAC;AAE9B,UACG,SAAS,SAAS,SAAS,UAAU,SAAS,UAC9C,SAAS,SAAS,SAAS,WAAW,SAAS,SAAS,GACzD;AACA,yBAAiB,KAAK,CAAC,CAAA,GAAI,UAAU,QAAQ,CAAC;AAC9C,mBAAW,CAAA;AACX,mBAAW,CAAA;MACb;AAEA,UAAI,SAAS,SAAS,SAAS,SAAS;AACtC,yBAAiB,KAAK,CAAC,CAAC,QAAQ,GAAG,CAAA,GAAI,CAAA,CAAE,CAAC;MAC5C,WAAW,SAAS,SAAS,SAAS,UAAU,SAAS,WAAW,GAAG;AACrE,yBAAiB,KAAK,CAAC,CAAA,GAAI,CAAA,GAAI,CAAC,QAAQ,CAAC,CAAC;MAC5C,WAAW,SAAS,SAAS,SAAS,UAAU,SAAS,SAAS,GAAG;AACnE,iBAAS,KAAK,QAAQ;MACxB,WAAW,SAAS,SAAS,SAAS,QAAQ;AAC5C,iBAAS,KAAK,QAAQ;MACxB;IACF;AAEA,QAAI,SAAS,UAAU,SAAS,QAAQ;AACtC,uBAAiB,KAAK,CAAC,CAAA,GAAI,UAAU,QAAQ,CAAC;AAC9C,iBAAW,CAAA;AACX,iBAAW,CAAA;IACb;AAEA,WAAO;EACT;EAEA,qBACE,UACA,UACAF,UAAoC;AAEpC,UAAM,cAAc,SAAS,SAAS,SAAS;AAC/C,UAAM,qBAAqB,IAAI,SAAS,OAAO,QAAQ,EAAE,IAAI,UAAQ,KAAK,QAAQ,MAAM,CAAC;AACzF,UAAM,aACJ,cAAc,KAAK,OAAO,0BAC1B,qBAAqB,KAAK,OAAO,oCAChC,KAAK,OAAO,aAAa,WAAW,KAAK,OAAO,aAAa;AAEhE,WAAO,aAAaA,SAAQ,UAAU,QAAQ,IAAI,CAAC,CAAC,UAAU,QAAQ,CAAC;EACzE;EAEA,oBAAoB,MAAgB,YAAqB,UAAsB,UAAoB;AACjG,UAAM,WAAW;MACf,OAAO;MACP,MAAM;;AAGR,UAAM,iBAAiB,KAAK,IAAI,SAAS,QAAQ,SAAS,MAAM;AAChE,aAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK;AACvC,YAAM,UAAU,SAAS,CAAC;AAC1B,YAAM,UAAU,SAAS,CAAC;AAE1B,YAAMG,QACJ,YAAY,UAAa,YAAY,SACrB,cAAc,QAAQ,SAAS,QAAQ,SAAS,YAAY,KAAK,MAAM,IACnF;AAEN,YAAM,kBACJ,YAAY,UAAa,QAAQ,cAAc,SAC5C,OAAA,OAAA,OAAA,OAAA,CAAA,GACOA,UAAS,SACT;QACE,QAAQA,MAAK,QAAQ;QACrB,SAASA,MAAK,QAAQ;QACtB,MAAkB,aAAa;UAElC,OAAA,OAAA,OAAA,OAAA,CAAA,GACkB,gBAAgB,QAAQ,SAAS,UAAU,CAAC,GAAA,EAC3D,MAAkB,WAAW,QAAQ,IAAI,EAAC,CAAA,CAC1C,GAAA,EACN,WAAW,QAAQ,WACnB,WAAW,QAAQ,UAAS,CAAA,IAE9B;AAEN,YAAM,kBACJ,YAAY,UAAa,QAAQ,cAAc,SAC5C,OAAA,OAAA,OAAA,OAAA,CAAA,GACOA,UAAS,SACT;QACE,QAAQA,MAAK,QAAQ;QACrB,SAASA,MAAK,QAAQ;QACtB,MAAkB,aAAa;UAElC,OAAA,OAAA,OAAA,OAAA,CAAA,GACkB,gBAAgB,QAAQ,SAAS,UAAU,CAAC,GAAA,EAC3D,MAAkB,WAAW,QAAQ,IAAI,EAAC,CAAA,CAC1C,GAAA,EACN,WAAW,QAAQ,WACnB,WAAW,QAAQ,UAAS,CAAA,IAE9B;AAEN,YAAM,EAAE,MAAM,MAAK,IAAK,KAAK,iBAAiB,MAAM,iBAAiB,eAAe;AACpF,eAAS,QAAQ;AACjB,eAAS,SAAS;IACpB;AAEA,WAAO;EACT;EAEA,iBAAiB,MAAgB,SAA4B,SAA0B;AACrF,WAAO;MACL,MAAM,KAAK,uBAAuB,MAAM,OAAO;MAC/C,OAAO,KAAK,uBAAuB,MAAM,OAAO;;EAEpD;EAEA,uBAAuB,MAAgB,MAAuB;AAC5D,QAAI,SAAS;AAAW,aAAO;AAE/B,UAAM,iBAAiB,KAAK,WAAW,OAAOL,oBAAmB,WAAW;MAC1E,WAAW,KAAK,aAAa;MAC7B,WAAW,KAAK,aAAa;KAC9B;AAED,WAAO,KAAK,WAAW,OAAO,sBAAsB,QAAQ;MAC1D,MAAM,KAAK;MACX,WAAW;MACX,cAAc;MACd,QAAQ,KAAK,WAAW,MAAM,WAAW,KAAK;MAC9C,SAAS,KAAK;MACd,YAAY;MACZ;MACA;KACD;EACH;;;;AC/PK,IAAM,kCAA+B,OAAA,OAAA,OAAA,OAAA,CAAA,GAC3B,mBAAmB,GAAA,EAClC,wBAAwB,OACxB,wBAAwB,MACxB,iCAAiC,IAAG,CAAA;AAGtC,IAAMM,wBAAuB;AAC7B,IAAMC,qBAAoB;AAC1B,IAAMC,0BAAyB;AAC/B,IAAMC,yBAAwB;AAE9B,IAAqB,qBAArB,MAAuC;EAIrC,YAAY,YAA0B,SAAmC,CAAA,GAAE;AACzE,SAAK,aAAa;AAClB,SAAK,SAAM,OAAA,OAAA,OAAA,OAAA,CAAA,GAAQ,+BAA+B,GAAK,MAAM;EAC/D;EAEA,OAAO,WAAqB;AAC1B,UAAM,YAAY,UACf,IAAI,UAAO;AACV,UAAI;AACJ,UAAI,KAAK,OAAO,QAAQ;AACtB,gBAAQ,KAAK,iBAAiB,IAAI;MACpC,OAAO;AACL,gBAAQ,KAAK,kBAAiB;MAChC;AACA,aAAO,KAAK,iBAAiB,MAAM,KAAK;IAC1C,CAAC,EACA,KAAK,IAAI;AAEZ,WAAO,KAAK,WAAW,OAAOH,uBAAsB,WAAW;MAC7D,aAAyB,iBAAiB,KAAK,OAAO,WAAW;MACjE,SAAS;KACV;EACH;EAEA,iBAAiB,MAAgB,OAAe;AAC9C,QAAI,KAAK,OAAO,0BAA0B,MAAM,QAAQ,KAAK,MAAM,KAAK,KAAK,OAAO,WAAW;AAAG,aAAO;AAEzG,UAAM,mBAAmB,KAAK,WAAW,SAASC,oBAAmB,WAAW;AAChF,UAAM,mBAAmB,KAAK,WAAW,SAASD,uBAAsB,WAAW;AACnF,UAAM,mBAAmB,KAAK,WAAW,SAASE,yBAAwB,MAAM;AAChF,UAAM,kBAAkB,KAAK,WAAW,SAASC,wBAAmC,YAAY,IAAI,CAAC;AAErG,WAAO,iBAAiB,OAAO;MAC7B;MACA,YAAwB,UAAU,IAAI;MACtC;MACA,UAAU,iBAAiB,OACzB;QACE,cAA0B,aAAa,IAAI;SAE7C;QACE,UAAU;QACV,SAAS;OACV;KAEJ;EACH;EAEA,oBAAiB;AACf,WAAO;MACL,OAAO;MACP,MAAM,KAAK,WAAW,OAAOH,uBAAsB,cAAc;QAC/D,cAAc;QACd;OACD;;EAEL;EAEA,iBAAiB,MAAc;AAC7B,UAAMI,WAAkB,aACd,cAAc,CAAC,MAA4B,gBAAgB,EAAE,SAAS,KAAK,UAAU,EAAE,OAAO,CAAC;AAGzG,WAAO,KAAK,OACT,IAAI,WAAQ;AACX,YAAM,WAAW;QACf,MAAM,KAAK,eAAe,MAAM,QAAQ,IAAI;QAC5C,OAAO,KAAK,eAAe,EAAE;;AAG/B,WAAK,mBAAmB,KAAK,EAAE,QAAQ,CAAC,CAAC,cAAc,UAAU,QAAQ,MAAK;AAC5E,YAAI,SAAS,UAAU,SAAS,UAAU,CAAC,aAAa,QAAQ;AAC9D,eAAK,qBAAqB,UAAU,UAAUA,QAAO,EAAE,IAAI,CAAC,CAACC,WAAUC,SAAQ,MAAK;AAClF,kBAAM,EAAE,MAAM,MAAK,IAAK,KAAK,oBAAoB,KAAK,YAAYD,WAAUC,SAAQ;AACpF,qBAAS,QAAQ;AACjB,qBAAS,SAAS;UACpB,CAAC;QACH,WAAW,aAAa,QAAQ;AAC9B,uBAAa,QAAQ,UAAO;AAC1B,kBAAM,EAAE,QAAQ,QAAO,IAAiB,gBAAgB,KAAK,SAAS,KAAK,UAAU;AACrF,kBAAM,EAAE,MAAM,MAAK,IAAK,KAAK,iBAC3B;cACE,MAAkB,aAAa;cAC/B;cACA;cACA,QAAQ,KAAK;eAEf;cACE,MAAkB,aAAa;cAC/B;cACA;cACA,QAAQ,KAAK;aACd;AAEH,qBAAS,QAAQ;AACjB,qBAAS,SAAS;UACpB,CAAC;QACH,WAAW,SAAS,UAAU,SAAS,QAAQ;AAC7C,gBAAM,EAAE,MAAM,MAAK,IAAK,KAAK,oBAAoB,KAAK,YAAY,UAAU,QAAQ;AACpF,mBAAS,QAAQ;AACjB,mBAAS,SAAS;QACpB,OAAO;AACL,kBAAQ,MAAM,0DAA0D,cAAc,UAAU,QAAQ;QAC1G;MACF,CAAC;AAED,aAAO;IACT,CAAC,EACA,OACC,CAAC,aAAaC,UAAQ;AACpB,aAAO,EAAE,MAAM,YAAY,OAAOA,MAAK,MAAM,OAAO,YAAY,QAAQA,MAAK,MAAK;IACpF,GACA,EAAE,MAAM,IAAI,OAAO,GAAE,CAAE;EAE7B;EAEA,mBAAmB,OAAgB;AACjC,UAAM,mBAAmC,CAAA;AAEzC,QAAI,WAAkD,CAAA;AACtD,QAAI,WAAmD,CAAA;AAEvD,aAAS,IAAI,GAAG,IAAI,MAAM,MAAM,QAAQ,KAAK;AAC3C,YAAM,WAAW,MAAM,MAAM,CAAC;AAE9B,UACG,SAAS,SAAS,SAAS,UAAU,SAAS,UAC9C,SAAS,SAAS,SAAS,WAAW,SAAS,SAAS,GACzD;AACA,yBAAiB,KAAK,CAAC,CAAA,GAAI,UAAU,QAAQ,CAAC;AAC9C,mBAAW,CAAA;AACX,mBAAW,CAAA;MACb;AAEA,UAAI,SAAS,SAAS,SAAS,SAAS;AACtC,yBAAiB,KAAK,CAAC,CAAC,QAAQ,GAAG,CAAA,GAAI,CAAA,CAAE,CAAC;MAC5C,WAAW,SAAS,SAAS,SAAS,UAAU,SAAS,WAAW,GAAG;AACrE,yBAAiB,KAAK,CAAC,CAAA,GAAI,CAAA,GAAI,CAAC,QAAQ,CAAC,CAAC;MAC5C,WAAW,SAAS,SAAS,SAAS,UAAU,SAAS,SAAS,GAAG;AACnE,iBAAS,KAAK,QAAQ;MACxB,WAAW,SAAS,SAAS,SAAS,QAAQ;AAC5C,iBAAS,KAAK,QAAQ;MACxB;IACF;AAEA,QAAI,SAAS,UAAU,SAAS,QAAQ;AACtC,uBAAiB,KAAK,CAAC,CAAA,GAAI,UAAU,QAAQ,CAAC;AAC9C,iBAAW,CAAA;AACX,iBAAW,CAAA;IACb;AAEA,WAAO;EACT;EAEA,qBACE,UACA,UACAH,UAAoC;AAEpC,UAAM,cAAc,SAAS,SAAS,SAAS;AAC/C,UAAM,qBAAqB,IAAI,SAAS,OAAO,QAAQ,EAAE,IAAI,UAAQ,KAAK,QAAQ,MAAM,CAAC;AACzF,UAAM,aACJ,cAAc,KAAK,OAAO,0BAC1B,qBAAqB,KAAK,OAAO,oCAChC,KAAK,OAAO,aAAa,WAAW,KAAK,OAAO,aAAa;AAEhE,WAAO,aAAaA,SAAQ,UAAU,QAAQ,IAAI,CAAC,CAAC,UAAU,QAAQ,CAAC;EACzE;EAEA,eAAe,aAAqB,MAAe;AACjD,WAAO,KAAK,WAAW,OAAOJ,uBAAsB,gBAAgB;MAClE;MACA,cAAa,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,YAAW,cAA0B,cAAc,WAAW;MACjF,WAAW;MACX,cAAc;KACf;EACH;EAEA,oBAAoB,YAAqB,UAAsB,UAAoB;AACjF,UAAM,WAAW;MACf,OAAO;MACP,MAAM;;AAGR,UAAM,iBAAiB,KAAK,IAAI,SAAS,QAAQ,SAAS,MAAM;AAChE,aAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK;AACvC,YAAM,UAAU,SAAS,CAAC;AAC1B,YAAM,UAAU,SAAS,CAAC;AAE1B,YAAMQ,QACJ,YAAY,UAAa,YAAY,SACrB,cAAc,QAAQ,SAAS,QAAQ,SAAS,YAAY,KAAK,MAAM,IACnF;AAEN,YAAM,kBACJ,YAAY,UAAa,QAAQ,cAAc,SAC5C,OAAA,OAAA,OAAA,OAAA,CAAA,GACOA,UAAS,SACT;QACE,QAAQA,MAAK,QAAQ;QACrB,SAASA,MAAK,QAAQ;QACtB,MAAkB,aAAa;UAElC,OAAA,OAAA,OAAA,OAAA,CAAA,GACkB,gBAAgB,QAAQ,SAAS,UAAU,CAAC,GAAA,EAC3D,MAAkB,WAAW,QAAQ,IAAI,EAAC,CAAA,CAC1C,GAAA,EACN,QAAQ,QAAQ,UAAS,CAAA,IAE3B;AAEN,YAAM,kBACJ,YAAY,UAAa,QAAQ,cAAc,SAC5C,OAAA,OAAA,OAAA,OAAA,CAAA,GACOA,UAAS,SACT;QACE,QAAQA,MAAK,QAAQ;QACrB,SAASA,MAAK,QAAQ;QACtB,MAAkB,aAAa;UAElC,OAAA,OAAA,OAAA,OAAA,CAAA,GACkB,gBAAgB,QAAQ,SAAS,UAAU,CAAC,GAAA,EAC3D,MAAkB,WAAW,QAAQ,IAAI,EAAC,CAAA,CAC1C,GAAA,EACN,QAAQ,QAAQ,UAAS,CAAA,IAE3B;AAEN,YAAM,EAAE,MAAM,MAAK,IAAK,KAAK,iBAAiB,iBAAiB,eAAe;AAC9E,eAAS,QAAQ;AACjB,eAAS,SAAS;IACpB;AAEA,WAAO;EACT;EAEA,iBAAiB,SAA4B,SAA0B;AACrE,WAAO;MACL,MAAM,KAAK,mBAAmB,OAAO;MACrC,OAAO,KAAK,mBAAmB,OAAO;;EAE1C;EAEA,mBAAmB,MAAuB;AACxC,UAAM,YAAY;AAClB,UAAM,eAAe;AAErB,WAAO,KAAK,WAAW,OAAOR,uBAAsB,QAAQ;MAC1D,OAAM,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,SAAQ,GAAe,aAAa,OAAO;MACvD,WAAW,SAAS,SAAY,YAAY,GAAG,SAAS;MACxD,cAAc,SAAS,SAAY,eAAe,GAAG,YAAY;MACjE,SAAQ,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,YAAW,MAAM,WAAW,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM;MAChD,SAAS,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM;MACf,YAAY,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM;KACnB;EACH;;;;ACpSF,IAAAS,SAAuB;;;ACAvB,IAAAC,SAAuB;AAEhB,IAAM,mBAAsC,CAAA;AAEnD,iBAAiB,mBAAmB,IAAI,IAAU,gBAAS,EAAC,MAAM,SAAU,GAAE,GAAE,GAAC;AAAI,MAAI,IAAE;AAAK,IAAE,EAAE,IAAE,KAAG,EAAE;AAAE,IAAE,EAAE,iCAAmC;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,0CAA4C;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,EAAE,GAAG,cAAa,GAAE,GAAE,QAAQ,CAAC;AAAE,IAAE,EAAE,kBAAmB;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,cAAa,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,0BAA6B;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,YAAW,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,MAAM;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,qCAAuC;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,0CAA4C;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,cAAa,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,SAAS;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,4CAA8C;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,gBAAe,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,SAAS;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,eAAe;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,aAAa;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,OAAO;AAAE,SAAO,EAAE,GAAE;AAAI,GAAE,UAAU,EAAC,cAAa,EAAC,MAAK,YAAY,UAAU,CAAA,GAAI,MAAM,CAAA,EAAI,EAAC,GAAG,MAAM,CAAA,EAAI,CAAC;AAEr3B,iBAAiB,sBAAsB,IAAI,IAAU,gBAAS,EAAC,MAAM,SAAU,GAAE,GAAE,GAAC;AAAI,MAAI,IAAE;AAAK,IAAE,EAAE,IAAE,KAAG,EAAE;AAAE,IAAE,EAAE,oCAAqC;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,eAAc,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,IAAK;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,wCAA0C;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,2DAA6D;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,eAAc,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,UAAU;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,sDAAwD;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,sDAAwD;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,YAAY;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,gCAAkC;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,MAAM;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,SAAQ,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,WAAW;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,QAAQ;AAAE,SAAO,EAAE,GAAE;AAAI,GAAE,UAAU,CAAA,GAAI,MAAM,CAAA,EAAI,CAAC;AAErxB,iBAAiB,sBAAsB,IAAI,IAAU,gBAAS,EAAC,MAAM,SAAU,GAAE,GAAE,GAAC;AAAI,MAAI,IAAE;AAAK,IAAE,EAAE,IAAE,KAAG,EAAE;AAAE,IAAE,EAAE,MAAM;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,iBAAkB;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,aAAY,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,GAAG;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,qBAAoB,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,SAAU;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,iBAAkB;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,qBAAoB,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,IAAK;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,sBAAuB;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,gBAAe,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,IAAK;AAAE,MAAG,EAAE,EAAE,EAAE,EAAE,eAAc,GAAE,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,KAAI,KAAI,OAAO,GAAE;AAAC,MAAE,GAAG,GAAE,GAAE,SAASC,IAAEC,IAAEC,IAAC;AAAE,MAAAA,GAAE,EAAEA,GAAE,EAAEA,GAAE,EAAE,eAAcF,IAAEC,IAAE,CAAC,CAAC,CAAC;IAAE,CAAC;AAAE,MAAE,IAAG;EAAG;AAAC,MAAG,CAAC,EAAE,EAAE,EAAE,EAAE,eAAc,GAAE,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,EAAE,GAAE;AAAC,MAAE,EAAE,QAAQ;EAAE;AAAC;AAAC,IAAE,EAAE,QAAQ;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,WAAW;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,OAAO;AAAE,SAAO,EAAE,GAAE;AAAI,GAAE,UAAU,CAAA,GAAI,MAAM,CAAA,EAAI,CAAC;AAE3uB,iBAAiB,oBAAoB,IAAI,IAAU,gBAAS,EAAC,MAAM,SAAU,GAAE,GAAE,GAAC;AAAI,MAAI,IAAE;AAAK,IAAE,EAAE,IAAE,KAAG,EAAE;AAAE,IAAE,EAAE,MAAM;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,iBAAkB;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,qBAAoB,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,IAAK;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,sBAAuB;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,gBAAe,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,IAAK;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,kCAAkC;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,gBAAgB;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,WAAW;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,OAAO;AAAE,SAAO,EAAE,GAAE;AAAI,GAAE,UAAU,CAAA,GAAI,MAAM,CAAA,EAAI,CAAC;AAE5e,iBAAiB,mBAAmB,IAAI,IAAU,gBAAS,EAAC,MAAM,SAAU,GAAE,GAAE,GAAC;AAAI,MAAI,IAAE;AAAK,IAAE,EAAE,IAAE,KAAG,EAAE;AAAE,IAAE,EAAE,sCAAwC;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,EAAE,GAAG,cAAa,GAAE,GAAE,MAAM,CAAC;AAAE,IAAE,EAAE,kCAAoC;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,gBAAe,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,SAAS;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,EAAE,GAAG,aAAY,GAAE,GAAE,MAAM,CAAC;AAAE,IAAE,EAAE,SAAS;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,mCAAqC;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,0FAAkG;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,YAAY;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,UAAU;AAAE,SAAO,EAAE,GAAE;AAAI,GAAE,UAAU,EAAC,cAAa,EAAC,MAAK,YAAY,UAAU,CAAA,GAAI,MAAM,CAAA,EAAI,GAAE,aAAY,EAAC,MAAK,WAAW,UAAU,CAAA,GAAI,MAAM,CAAA,EAAI,EAAC,GAAG,MAAM,CAAA,EAAI,CAAC;AAE9uB,iBAAiB,cAAc,IAAI,IAAU,gBAAS,EAAC,MAAM,SAAU,GAAE,GAAE,GAAC;AAAI,MAAI,IAAE;AAAK,IAAE,EAAE,IAAE,KAAG,EAAE;AAAE,IAAE,EAAE,MAAM;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,iBAAkB;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,aAAY,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,GAAG;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,QAAO,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,IAAK;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,QAAQ;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,cAAa,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,WAAW;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,iBAAkB;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,QAAO,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,IAAK;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,sBAAuB;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,gBAAe,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,IAAK;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,MAAG,EAAE,EAAE,EAAE,EAAE,UAAS,GAAE,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,KAAI,KAAI,OAAO,GAAE;AAAC,MAAE,GAAG,GAAE,GAAE,SAASD,IAAEC,IAAEC,IAAC;AAAE,MAAAA,GAAE,EAAE,iDAAmD;AAAE,MAAAA,GAAE,EAAEA,GAAE,EAAEA,GAAE,EAAE,UAASF,IAAEC,IAAE,CAAC,CAAC,CAAC;AAAE,MAAAC,GAAE,EAAE,SAAS;AAAE,MAAAA,GAAE,EAAE,OAAO,CAAC;IAAE,CAAC;AAAE,MAAE,IAAG;EAAG;AAAC,MAAG,CAAC,EAAE,EAAE,EAAE,EAAE,UAAS,GAAE,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,EAAE,GAAE;AAAC,MAAE,EAAE,8DAAgE;AAAE,MAAE,EAAE,OAAO,CAAC;EAAE;AAAC;AAAC,MAAG,EAAE,EAAE,EAAE,EAAE,WAAU,GAAE,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,KAAI,KAAI,OAAO,GAAE;AAAC,MAAE,GAAG,GAAE,GAAE,SAASF,IAAEC,IAAEC,IAAC;AAAE,MAAAA,GAAE,EAAE,8CAAgD;AAAE,MAAAA,GAAE,EAAEA,GAAE,EAAEA,GAAE,EAAE,WAAUF,IAAEC,IAAE,CAAC,CAAC,CAAC;AAAE,MAAAC,GAAE,EAAE,SAAS;AAAE,MAAAA,GAAE,EAAE,OAAO,CAAC;IAAE,CAAC;AAAE,MAAE,IAAG;EAAG;AAAC,MAAG,CAAC,EAAE,EAAE,EAAE,EAAE,WAAU,GAAE,GAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,EAAE,GAAE;AAAC,MAAE,EAAE,yDAA2D;AAAE,MAAE,EAAE,OAAO,CAAC;EAAE;AAAC;AAAC,IAAE,EAAE,gBAAgB;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,WAAW;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,OAAO;AAAE,SAAO,EAAE,GAAE;AAAI,GAAE,UAAU,CAAA,GAAI,MAAM,CAAA,EAAI,CAAC;AAE5wC,iBAAiB,iBAAiB,IAAI,IAAU,gBAAS,EAAC,MAAM,SAAU,GAAE,GAAE,GAAC;AAAI,MAAI,IAAE;AAAK,IAAE,EAAE,IAAE,KAAG,EAAE;AAAE,IAAE,EAAE,0BAA2B;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,eAAc,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,IAAK;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,MAAM;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,WAAU,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,QAAQ;AAAE,SAAO,EAAE,GAAE;AAAI,GAAE,UAAU,CAAA,GAAI,MAAM,CAAA,EAAI,CAAC;AAEzT,iBAAiB,iBAAiB,IAAI,IAAU,gBAAS,EAAC,MAAM,SAAU,GAAE,GAAE,GAAC;AAAI,MAAI,IAAE;AAAK,IAAE,EAAE,IAAE,KAAG,EAAE;AAAE,IAAE,EAAE,gHAA4H;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,kBAAoB;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,8JAAgK;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,QAAQ;AAAE,SAAO,EAAE,GAAE;AAAI,GAAE,UAAU,CAAA,GAAI,MAAM,CAAA,EAAI,CAAC;AAE/gB,iBAAiB,mBAAmB,IAAI,IAAU,gBAAS,EAAC,MAAM,SAAU,GAAE,GAAE,GAAC;AAAI,MAAI,IAAE;AAAK,IAAE,EAAE,IAAE,KAAG,EAAE;AAAE,IAAE,EAAE,iGAA2G;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,sCAA0C;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,yLAA2L;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,QAAQ;AAAE,SAAO,EAAE,GAAE;AAAI,GAAE,UAAU,CAAA,GAAI,MAAM,CAAA,EAAI,CAAC;AAEjjB,iBAAiB,mBAAmB,IAAI,IAAU,gBAAS,EAAC,MAAM,SAAU,GAAE,GAAE,GAAC;AAAI,MAAI,IAAE;AAAK,IAAE,EAAE,IAAE,KAAG,EAAE;AAAE,IAAE,EAAE,gGAA0G;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,sCAA0C;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,+IAAiJ;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,QAAQ;AAAE,SAAO,EAAE,GAAE;AAAI,GAAE,UAAU,CAAA,GAAI,MAAM,CAAA,EAAI,CAAC;AAEtgB,iBAAiB,mBAAmB,IAAI,IAAU,gBAAS,EAAC,MAAM,SAAU,GAAE,GAAE,GAAC;AAAI,MAAI,IAAE;AAAK,IAAE,EAAE,IAAE,KAAG,EAAE;AAAE,IAAE,EAAE,8FAAwG;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,sCAA0C;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,yJAA2J;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,QAAQ;AAAE,SAAO,EAAE,GAAE;AAAI,GAAE,UAAU,CAAA,GAAI,MAAM,CAAA,EAAI,CAAC;AAE9gB,iBAAiB,WAAW,IAAI,IAAU,gBAAS,EAAC,MAAM,SAAU,GAAE,GAAE,GAAC;AAAI,MAAI,IAAE;AAAK,IAAE,EAAE,IAAE,KAAG,EAAE;AAAE,IAAE,EAAE,oGAAgH;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,6LAA+L;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,QAAQ;AAAE,SAAO,EAAE,GAAE;AAAI,GAAE,UAAU,CAAA,GAAI,MAAM,CAAA,EAAI,CAAC;AAEpf,iBAAiB,wBAAwB,IAAI,IAAU,gBAAS,EAAC,MAAM,SAAU,GAAE,GAAE,GAAC;AAAI,MAAI,IAAE;AAAK,IAAE,EAAE,IAAE,KAAG,EAAE;AAAE,IAAE,EAAE,WAAY;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,cAAa,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,wCAA4C;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,iBAAgB,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,IAAK;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,mCAAqC;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,MAAM;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,YAAW,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,YAAY;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,iCAAmC;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,wCAA0C;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,4CAA8C;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,gDAAkD;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,kBAAkB;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,SAAQ,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,0BAA0B;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,sBAAsB;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,gBAAgB;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,YAAY;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,QAAQ;AAAE,SAAO,EAAE,GAAE;AAAI,GAAE,UAAU,CAAA,GAAI,MAAM,CAAA,EAAI,CAAC;AAE77B,iBAAiB,sBAAsB,IAAI,IAAU,gBAAS,EAAC,MAAM,SAAU,GAAE,GAAE,GAAC;AAAI,MAAI,IAAE;AAAK,IAAE,EAAE,IAAE,KAAG,EAAE;AAAE,IAAE,EAAE,yBAA2B;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,aAAY,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,QAAQ;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,yBAA2B;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,aAAY,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,QAAQ;AAAE,SAAO,EAAE,GAAE;AAAI,GAAE,UAAU,CAAA,GAAI,MAAM,CAAA,EAAI,CAAC;AAExU,iBAAiB,wBAAwB,IAAI,IAAU,gBAAS,EAAC,MAAM,SAAU,GAAE,GAAE,GAAC;AAAI,MAAI,IAAE;AAAK,IAAE,EAAE,IAAE,KAAG,EAAE;AAAE,IAAE,EAAE,WAAY;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,cAAa,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,wCAA4C;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,iBAAgB,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,IAAK;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,mCAAqC;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,QAAQ;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,YAAW,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,YAAY;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,kCAAoC;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,0CAA4C;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,4CAA8C;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,gDAAkD;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,oDAAsD;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,sBAAsB;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,cAAa,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,8BAA8B;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,0BAA0B;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,oBAAoB;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,gBAAgB;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,0CAA4C;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,4CAA8C;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,gDAAkD;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,oDAAsD;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,sBAAsB;AAAE,IAAE,EAAE,EAAE,EAAE,EAAE,EAAE,eAAc,GAAE,GAAE,CAAC,CAAC,CAAC;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,8BAA8B;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,0BAA0B;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,oBAAoB;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,gBAAgB;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,YAAY;AAAE,IAAE,EAAE,OAAO,CAAC;AAAE,IAAE,EAAE,QAAQ;AAAE,SAAO,EAAE,GAAE;AAAI,GAAE,UAAU,CAAA,GAAI,MAAM,CAAA,EAAI,CAAC;AAEhlD,iBAAiB,gBAAgB,IAAI,IAAU,gBAAS,EAAC,MAAM,SAAU,GAAE,GAAE,GAAC;AAAI,MAAI,IAAE;AAAK,IAAE,EAAE,IAAE,KAAG,EAAE;AAAE,IAAE,EAAE,4DAA8D;AAAE,SAAO,EAAE,GAAE;AAAI,GAAE,UAAU,CAAA,GAAI,MAAM,CAAA,EAAI,CAAC;AAExN,iBAAiB,kBAAkB,IAAI,IAAU,gBAAS,EAAC,MAAM,SAAU,GAAE,GAAE,GAAC;AAAI,MAAI,IAAE;AAAK,IAAE,EAAE,IAAE,KAAG,EAAE;AAAE,IAAE,EAAE,kEAAoE;AAAE,SAAO,EAAE,GAAE;AAAI,GAAE,UAAU,CAAA,GAAI,MAAM,CAAA,EAAI,CAAC;AAEhO,iBAAiB,kBAAkB,IAAI,IAAU,gBAAS,EAAC,MAAM,SAAU,GAAE,GAAE,GAAC;AAAI,MAAI,IAAE;AAAK,IAAE,EAAE,IAAE,KAAG,EAAE;AAAE,IAAE,EAAE,kEAAoE;AAAE,SAAO,EAAE,GAAE;AAAI,GAAE,UAAU,CAAA,GAAI,MAAM,CAAA,EAAI,CAAC;AAEhO,iBAAiB,kBAAkB,IAAI,IAAU,gBAAS,EAAC,MAAM,SAAU,GAAE,GAAE,GAAC;AAAI,MAAI,IAAE;AAAK,IAAE,EAAE,IAAE,KAAG,EAAE;AAAE,IAAE,EAAE,8DAAgE;AAAE,SAAO,EAAE,GAAE;AAAI,GAAE,UAAU,CAAA,GAAI,MAAM,CAAA,EAAI,CAAC;;;ADvB5N,IAAqB,eAArB,MAAiC;EAG/B,YAAY,EAAE,oBAAoB,CAAA,GAAI,eAAe,CAAA,EAAE,GAAsB;AAC3E,UAAM,uBAAuB,OAAO,QAAQ,YAAY,EAAE,OACxD,CAAC,mBAAmB,CAAC,MAAM,cAAc,MAAK;AAC5C,YAAM,mBAAyC,eAAQ,gBAAgB,EAAE,UAAU,MAAK,CAAE;AAC1F,aAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GAAY,iBAAiB,GAAA,EAAE,CAAC,IAAI,GAAG,iBAAgB,CAAA;IACzD,GACA,CAAA,CAAE;AAGJ,SAAK,uBAAoB,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GAAQ,gBAAgB,GAAK,iBAAiB,GAAK,oBAAoB;EAClG;EAEA,OAAO,QAAQ,gBAAsB;AACnC,WAAa,eAAQ,gBAAgB,EAAE,UAAU,MAAK,CAAE;EAC1D;EAEA,OAAO,WAAmB,MAAc,QAAuB,UAA2B,QAAe;AACvG,UAAM,cAAc,KAAK,YAAY,WAAW,IAAI;AACpD,QAAI;AACF,YAAM,WAAW,KAAK,qBAAqB,WAAW;AACtD,aAAO,SAAS,OAAO,QAAQ,UAAU,MAAM;IACjD,SAAS,IAAI;AACX,YAAM,IAAI,MAAM,sCAAsC,WAAW,GAAG;IACtE;EACF;EAEA,SAAS,WAAmB,MAAY;AACtC,WAAO,KAAK,qBAAqB,KAAK,YAAY,WAAW,IAAI,CAAC;EACpE;EAEQ,YAAY,WAAmB,MAAY;AACjD,WAAO,GAAG,SAAS,IAAI,IAAI;EAC7B;;;;AEpCK,IAAM,yBAAsB,OAAA,OAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GAC9B,+BAA+B,GAC/B,+BAA+B,GAAA,EAClC,cAAc,iBAAiB,cAC/B,cAAc,KAAI,CAAA;AAGd,SAAUC,OAAM,WAAmB,gBAAiC,CAAA,GAAE;AAC1E,SAAkB,MAAM,WAAS,OAAA,OAAA,OAAA,OAAA,CAAA,GAAO,sBAAsB,GAAK,aAAa,CAAA;AAClF;AAEM,SAAU,KAAK,WAAgC,gBAAiC,CAAA,GAAE;AACtF,QAAM,SAAM,OAAA,OAAA,OAAA,OAAA,CAAA,GAAQ,sBAAsB,GAAK,aAAa;AAE5D,QAAM,WAAW,OAAO,cAAc,WAAsB,MAAM,WAAW,MAAM,IAAI;AAEvF,QAAM,aAAa,IAAI,aAAa,MAAM;AAE1C,QAAM,EAAE,YAAW,IAAK;AACxB,QAAM,yBAAyB,EAAE,YAAW;AAE5C,QAAM,WAAW,OAAO,eAAe,IAAI,iBAAiB,YAAY,sBAAsB,EAAE,OAAO,QAAQ,IAAI;AAEnH,QAAM,aACJ,OAAO,iBAAiB,iBACpB,IAAI,mBAAmB,YAAY,MAAM,EAAE,OAAO,QAAQ,IAC1D,IAAI,mBAAmB,YAAY,MAAM,EAAE,OAAO,QAAQ;AAEhE,SAAO,WAAW;AACpB;", "names": ["<PERSON>", "text", "delimiters", "key", "<PERSON>", "key", "<PERSON>", "LineType", "ColorSchemeType", "max", "diff", "value", "regex", "o", "key", "distance", "diff", "highlightedLine", "baseTemplatesPath", "iconsBaseTemplatesPath", "matcher", "oldLines", "newLines", "diff", "genericTemplatesPath", "baseTemplatesPath", "iconsBaseTemplatesPath", "tagsBaseTemplatesPath", "matcher", "oldLines", "newLines", "html", "diff", "<PERSON>", "<PERSON>", "c", "p", "t", "parse"]}