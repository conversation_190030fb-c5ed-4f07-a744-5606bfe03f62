import React, { useState, useEffect } from 'react';
import { Modal, Button, Input, Select, Toggle } from '../ui';
import { LLMFactory } from '../../services/llm/llm-factory';
import type { AppConfig, LLMConfig, SettingsModalProps } from '../../types';

export const SettingsModal: React.FC<SettingsModalProps> = ({
  isOpen,
  onClose,
  title,
  config,
  onSave
}) => {
  const [formData, setFormData] = useState<AppConfig>(config);
  const [testingConnection, setTestingConnection] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<{
    success?: boolean;
    message?: string;
  }>({});
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    setFormData(config);
    setConnectionStatus({});
    setErrors({});
  }, [config, isOpen]);

  const supportedProviders = LLMFactory.getSupportedProviders();

  const handleProviderChange = (provider: string) => {
    const defaultConfig = LLMFactory.getDefaultConfig(provider);
    setFormData(prev => ({
      ...prev,
      llm: {
        ...prev.llm,
        provider: provider as LLMConfig['provider'],
        model: defaultConfig.model || prev.llm.model,
        baseUrl: defaultConfig.baseUrl || prev.llm.baseUrl,
        maxTokens: defaultConfig.maxTokens || prev.llm.maxTokens,
        temperature: defaultConfig.temperature || prev.llm.temperature,
      }
    }));
    setConnectionStatus({});
  };

  const handleLLMConfigChange = (field: keyof LLMConfig, value: any) => {
    setFormData(prev => ({
      ...prev,
      llm: {
        ...prev.llm,
        [field]: value
      }
    }));
    setConnectionStatus({});
  };

  const handleTestConnection = async () => {
    setTestingConnection(true);
    setConnectionStatus({});

    try {
      const result = await LLMFactory.testConnection(formData.llm);
      setConnectionStatus({
        success: result.success,
        message: result.success ? 'Connection successful!' : result.error
      });
    } catch (error: any) {
      setConnectionStatus({
        success: false,
        message: error.message || 'Connection failed'
      });
    } finally {
      setTestingConnection(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.llm.apiKey.trim()) {
      newErrors.apiKey = 'API key is required';
    }

    if (formData.llm.maxTokens && (formData.llm.maxTokens < 1 || formData.llm.maxTokens > 100000)) {
      newErrors.maxTokens = 'Max tokens must be between 1 and 100000';
    }

    if (formData.llm.temperature && (formData.llm.temperature < 0 || formData.llm.temperature > 2)) {
      newErrors.temperature = 'Temperature must be between 0 and 2';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (validateForm()) {
      onSave(formData);
      onClose();
    }
  };

  const currentProvider = supportedProviders.find(p => p.provider === formData.llm.provider);
  const modelOptions = currentProvider?.models.map(model => ({
    value: model,
    label: model
  })) || [];

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={title}>
      <div className="space-y-6">
        {/* LLM Configuration */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-text-primary">LLM Configuration</h3>
          
          <Select
            label="Provider"
            value={formData.llm.provider}
            onChange={(e) => handleProviderChange(e.target.value)}
            options={supportedProviders.map(provider => ({
              value: provider.provider,
              label: provider.name
            }))}
            helperText={currentProvider?.description}
          />

          <Select
            label="Model"
            value={formData.llm.model}
            onChange={(e) => handleLLMConfigChange('model', e.target.value)}
            options={modelOptions}
          />

          <Input
            label="API Key"
            type="password"
            value={formData.llm.apiKey}
            onChange={(e) => handleLLMConfigChange('apiKey', e.target.value)}
            error={errors.apiKey}
            placeholder="Enter your API key"
          />

          {formData.llm.provider === 'deepseek' && (
            <Input
              label="Base URL"
              value={formData.llm.baseUrl || ''}
              onChange={(e) => handleLLMConfigChange('baseUrl', e.target.value)}
              placeholder="https://api.deepseek.com/v1"
              helperText="Custom API endpoint (optional)"
            />
          )}

          <div className="grid grid-cols-2 gap-4">
            <Input
              label="Max Tokens"
              type="number"
              value={formData.llm.maxTokens || ''}
              onChange={(e) => handleLLMConfigChange('maxTokens', parseInt(e.target.value) || undefined)}
              error={errors.maxTokens}
              placeholder="2000"
              min="1"
              max="100000"
            />

            <Input
              label="Temperature"
              type="number"
              value={formData.llm.temperature || ''}
              onChange={(e) => handleLLMConfigChange('temperature', parseFloat(e.target.value) || undefined)}
              error={errors.temperature}
              placeholder="0.7"
              min="0"
              max="2"
              step="0.1"
            />
          </div>

          {/* Connection Test */}
          <div className="flex items-center space-x-4">
            <Button
              variant="secondary"
              onClick={handleTestConnection}
              loading={testingConnection}
              disabled={!formData.llm.apiKey.trim()}
            >
              Test Connection
            </Button>
            
            {connectionStatus.message && (
              <div className={`text-sm ${connectionStatus.success ? 'text-green-400' : 'text-red-400'}`}>
                {connectionStatus.message}
              </div>
            )}
          </div>
        </div>

        {/* Execution Mode */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-text-primary">Execution Mode</h3>
          
          <Toggle
            checked={formData.executionMode === 'yolo'}
            onChange={(checked) => setFormData(prev => ({
              ...prev,
              executionMode: checked ? 'yolo' : 'confirm'
            }))}
            label="YOLO Mode"
            description="Execute commands without confirmation (⚠️ Use with caution)"
          />
        </div>

        {/* General Settings */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-text-primary">General Settings</h3>
          
          <Toggle
            checked={formData.autoSave}
            onChange={(checked) => setFormData(prev => ({
              ...prev,
              autoSave: checked
            }))}
            label="Auto Save"
            description="Automatically save conversation history"
          />

          <Input
            label="Max History Length"
            type="number"
            value={formData.maxHistoryLength}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              maxHistoryLength: parseInt(e.target.value) || 100
            }))}
            placeholder="100"
            min="10"
            max="1000"
            helperText="Maximum number of messages to keep in history"
          />
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-border-primary">
          <Button variant="secondary" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Settings
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default SettingsModal;
