{"version": 3, "file": "normalize-options.js", "sourceRoot": "", "sources": ["../src/normalize-options.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAA;AAC1C,OAAO,EAAE,QAAQ,EAAE,gBAAgB,EAAE,MAAM,YAAY,CAAA;AAGvD,OAAO,EACL,eAAe,EACf,cAAc,EACd,iBAAiB,EACjB,qBAAqB,EACrB,qBAAqB,EACrB,iBAAiB,EACjB,iBAAiB,GAClB,MAAM,gBAAgB,CAAA;AACvB,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,cAAc,CAAA;AAChE,OAAO,EAAE,GAAG,EAAE,MAAM,aAAa,CAAA;AAGjC,MAAM,CAAC,IAAI,iBAAyB,CAAA;AAEpC,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAAqC,CAAA;AAEtE,IAAI,MAA2B,CAAA;AAO/B,MAAM,UAAU,gBAAgB,CAC9B,OAA0C,EAC1C,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE;IAEnB,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,wBAAwB,EAAE,GAAG,CAAC,OAAO,KAAK,EAAE,CAAC,CAAA;IAEtE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,GAA6B,QAAQ,IAAI,EAAE,CAAA;IAEzE,IAAI,OAA4B,CAAA;IAEhC,IAAI,UAAU,EAAE,CAAC;QACf,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,CAAA;QAChC,OAAO,GAAG,IAAI,CAAA;IAChB,CAAC;SAAM,IAAI,OAAO,EAAE,CAAC;QACnB,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAA;QACtD,GAAG,CAAC,oBAAoB,EAAE,GAAG,OAAO,CAAC,CAAA;QACrC,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAA;QACjC,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC3C,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE;gBAC1B,QAAQ,EAAE,IAAI;gBACd,GAAG;gBACH,GAAG,EAAE,IAAI;gBACT,iBAAiB,EAAE,KAAK;gBACxB,SAAS,EAAE,KAAK;gBAChB,MAAM,EAAE,cAAc;aACvB,CAAC,CAAA;QACJ,CAAC;QACD,GAAG,CAAC,qBAAqB,EAAE,GAAG,OAAO,CAAC,CAAA;QACtC,OAAO,GAAG,OAAO,CAAC,OAAO,CACvB,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,iBAAiB,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAC9D,CAAA;QACD,GAAG,CAAC,oBAAoB,EAAE,GAAG,OAAO,CAAC,CAAA;QACrC,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;YACvB,OAAO,GAAG,IAAI,CAAA;QAChB,CAAC;QACD,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACxB,OAAO,GAAG,SAAS,CAAA;QACrB,CAAC;aAAM,IAAI,CAAC,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAChD,MAAM,GAAG,IAAI,CAAA;YACb,OAAO,CAAC,IAAI,CACV,uJAAuJ,CACxJ,CAAA;QACH,CAAC;IACH,CAAC;IAED,IAAI,CAAC,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;QAC5B,UAAU,GAAG,iBAAiB,KAAK,OAAO,CAAC,eAAe,CAAC,CAAA;QAC3D,OAAO,GAAG,IAAI,CAAA;IAChB,CAAC;IAED,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,CAAA;IACvC,MAAM,QAAQ,GAAG,GAAG,UAAU,KAAK,WAAW,EAAE,CAAA;IAChD,IAAI,UAAU,EAAE,CAAC;QACf,MAAM,aAAa,GAAG,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QACrD,IAAI,aAAa,EAAE,CAAC;YAClB,GAAG,CAAC,0BAA0B,EAAE,UAAU,EAAE,cAAc,EAAE,OAAO,CAAC,CAAA;YACpE,OAAO,aAAa,CAAA;QACtB,CAAC;IACH,CAAC;IAED,IAAI,CAAC,OAAO,IAAI,UAAU,IAAI,UAAU,KAAK,iBAAiB,EAAE,CAAC;QAC/D,UAAU,GAAG,OAAO,CAAC,iBAAiB,EAAE,KAAK,EAAE,UAAU,CAAC,CAAA;IAC5D,CAAC;IAED,OAAO,GAAG;QACR,cAAc,EAAE,qBAAqB;QACrC,UAAU,EAAE,iBAAiB;QAC7B,cAAc,EAAE,qBAAqB;QACrC,UAAU,EAAE,iBAAiB;QAC7B,GAAG,OAAO;QACV,OAAO;QACP,QAAQ,EAAE,UAAU;YAClB,CAAC,CAAC,EAAE,UAAU,EAAE,UAAU,IAAI,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE;YAC9D,CAAC,CAAC,SAAS;KACd,CAAA;IAED,IAAI,UAAU,EAAE,CAAC;QACf,iBAAiB,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;IAC1C,CAAC;IAED,OAAO,OAAO,CAAA;AAChB,CAAC"}