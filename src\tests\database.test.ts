import { DatabaseManager } from '../database/database';
import { tmpdir } from 'os';
import { join } from 'path';
import { unlinkSync, existsSync } from 'fs';

describe('DatabaseManager', () => {
  let db: DatabaseManager;
  let testDbPath: string;

  beforeEach(() => {
    // Create a temporary database for testing
    testDbPath = join(tmpdir(), `test-arien-${Date.now()}.db`);
    db = DatabaseManager.createTestInstance(testDbPath);
  });

  afterEach(() => {
    // Clean up test database
    db.close();
    if (existsSync(testDbPath)) {
      unlinkSync(testDbPath);
    }
  });

  describe('Conversation Management', () => {
    test('should create a new conversation', () => {
      const conversationId = db.createConversation('Test Conversation');
      expect(conversationId).toBeDefined();
      expect(typeof conversationId).toBe('string');
    });

    test('should retrieve conversations', () => {
      const id1 = db.createConversation('Conversation 1');
      const id2 = db.createConversation('Conversation 2');
      
      const conversations = db.getConversations();
      expect(conversations).toHaveLength(2);
      expect(conversations.map(c => c.id)).toContain(id1);
      expect(conversations.map(c => c.id)).toContain(id2);
    });

    test('should delete a conversation', () => {
      const conversationId = db.createConversation('Test Conversation');
      db.deleteConversation(conversationId);
      
      const conversations = db.getConversations();
      expect(conversations.map(c => c.id)).not.toContain(conversationId);
    });
  });

  describe('Message Management', () => {
    let conversationId: string;

    beforeEach(() => {
      conversationId = db.createConversation('Test Conversation');
    });

    test('should save and retrieve messages', () => {
      const message = {
        id: 'msg-1',
        type: 'user' as const,
        content: 'Hello, world!',
        timestamp: new Date(),
        metadata: { toolName: 'test', success: true }
      };

      db.saveMessage(message, conversationId);
      const messages = db.getMessages(conversationId);
      
      expect(messages).toHaveLength(1);
      expect(messages[0].content).toBe('Hello, world!');
      expect(messages[0].type).toBe('user');
    });

    test('should handle multiple messages in order', () => {
      const messages = [
        {
          id: 'msg-1',
          type: 'user' as const,
          content: 'First message',
          timestamp: new Date(Date.now() - 1000),
        },
        {
          id: 'msg-2',
          type: 'ai' as const,
          content: 'Second message',
          timestamp: new Date(),
        }
      ];

      messages.forEach(msg => db.saveMessage(msg, conversationId));
      const retrieved = db.getMessages(conversationId);
      
      expect(retrieved).toHaveLength(2);
      expect(retrieved[0].content).toBe('First message');
      expect(retrieved[1].content).toBe('Second message');
    });
  });

  describe('Tool Execution Tracking', () => {
    test('should save and retrieve tool executions', () => {
      const messageId = 'msg-1';
      const toolName = 'test-tool';
      const toolArguments = { param1: 'value1', param2: 42 };
      const result = { success: true, output: 'Tool executed successfully' };
      const executionTime = 150;

      const executionId = db.saveToolExecution(
        messageId,
        toolName,
        toolArguments,
        result,
        executionTime
      );

      expect(executionId).toBeDefined();
      
      const executions = db.getToolExecutions(messageId);
      expect(executions).toHaveLength(1);
      expect(executions[0].tool_name).toBe(toolName);
      expect(executions[0].execution_time).toBe(executionTime);
    });
  });

  describe('Settings Management', () => {
    test('should save and retrieve settings', () => {
      const testKey = 'test-setting';
      const testValue = { apiKey: 'secret', model: 'gpt-4' };

      db.saveSetting(testKey, testValue);
      const retrieved = db.getSetting(testKey);
      
      expect(retrieved).toEqual(testValue);
    });

    test('should return null for non-existent settings', () => {
      const result = db.getSetting('non-existent-key');
      expect(result).toBeNull();
    });

    test('should retrieve all settings', () => {
      db.saveSetting('setting1', 'value1');
      db.saveSetting('setting2', { nested: 'value2' });
      
      const allSettings = db.getAllSettings();
      expect(allSettings).toHaveProperty('setting1', 'value1');
      expect(allSettings).toHaveProperty('setting2', { nested: 'value2' });
    });
  });

  describe('Context Cache', () => {
    test('should set and get cache values', () => {
      const key = 'test-cache-key';
      const value = { data: 'cached data', timestamp: Date.now() };

      db.setCacheValue(key, value);
      const retrieved = db.getCacheValue(key);
      
      expect(retrieved).toEqual(value);
    });

    test('should handle cache expiration', () => {
      const key = 'expiring-key';
      const value = 'expiring value';
      const expiresAt = new Date(Date.now() - 1000); // Already expired

      db.setCacheValue(key, value, expiresAt);
      const retrieved = db.getCacheValue(key);
      
      expect(retrieved).toBeNull();
    });

    test('should clear expired cache entries', () => {
      // Set an expired entry
      db.setCacheValue('expired', 'value', new Date(Date.now() - 1000));
      // Set a valid entry
      db.setCacheValue('valid', 'value', new Date(Date.now() + 10000));

      db.clearExpiredCache();
      
      expect(db.getCacheValue('expired')).toBeNull();
      expect(db.getCacheValue('valid')).toBe('value');
    });
  });
});
