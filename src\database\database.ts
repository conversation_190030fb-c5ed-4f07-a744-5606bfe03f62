import Database from 'better-sqlite3';
import { readFileSync } from 'fs';
import { join } from 'path';
import { app } from 'electron';
import { v4 as uuidv4 } from 'uuid';
import type { DatabaseSchema, Message, AppConfig } from '../types';

export class DatabaseManager {
  private db: Database.Database;
  private static instance: DatabaseManager;

  private constructor(dbPath?: string) {
    // Get the user data directory for storing the database
    const finalDbPath = dbPath || join(app.getPath('userData'), 'arien-agent.db');

    this.db = new Database(finalDbPath);
    this.db.pragma('journal_mode = WAL');
    this.db.pragma('foreign_keys = ON');

    this.initializeDatabase();
  }

  public static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }

  public static createTestInstance(dbPath: string): DatabaseManager {
    return new DatabaseManager(dbPath);
  }

  private initializeDatabase(): void {
    try {
      // Read and execute schema
      // In development, schema is in src/database/schema.sql
      // In production build, schema is copied to .vite/build/schema.sql
      let schemaPath = join(__dirname, 'schema.sql');

      // Try the build directory first (production)
      try {
        const schema = readFileSync(schemaPath, 'utf-8');
        this.db.exec(schema);
        console.log('Database initialized successfully from build directory');
        return;
      } catch (buildError) {
        // If that fails, try the development path
        schemaPath = join(__dirname, '..', '..', 'src', 'database', 'schema.sql');
        try {
          const schema = readFileSync(schemaPath, 'utf-8');
          this.db.exec(schema);
          console.log('Database initialized successfully from source directory');
          return;
        } catch (devError) {
          console.error('Failed to read schema from both build and source directories');
          console.error('Build error:', buildError);
          console.error('Dev error:', devError);
          throw devError;
        }
      }
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  }

  // Conversation Management
  public createConversation(title: string = 'New Conversation'): string {
    const id = uuidv4();
    const stmt = this.db.prepare(`
      INSERT INTO conversations (id, title)
      VALUES (?, ?)
    `);
    
    stmt.run(id, title);
    return id;
  }

  public getConversations(): DatabaseSchema['conversations'][] {
    const stmt = this.db.prepare(`
      SELECT * FROM conversations
      ORDER BY updated_at DESC
    `);
    
    return stmt.all() as DatabaseSchema['conversations'][];
  }

  public getConversation(id: string): DatabaseSchema['conversations'] | null {
    const stmt = this.db.prepare(`
      SELECT * FROM conversations WHERE id = ?
    `);
    
    return stmt.get(id) as DatabaseSchema['conversations'] | null;
  }

  public updateConversationTitle(id: string, title: string): void {
    const stmt = this.db.prepare(`
      UPDATE conversations 
      SET title = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
    
    stmt.run(title, id);
  }

  public deleteConversation(id: string): void {
    const stmt = this.db.prepare(`
      DELETE FROM conversations WHERE id = ?
    `);
    
    stmt.run(id);
  }

  // Message Management
  public saveMessage(message: Message, conversationId: string): void {
    const stmt = this.db.prepare(`
      INSERT INTO messages (id, conversation_id, type, content, timestamp, metadata)
      VALUES (?, ?, ?, ?, ?, ?)
    `);
    
    stmt.run(
      message.id,
      conversationId,
      message.type,
      message.content,
      message.timestamp.toISOString(),
      message.metadata ? JSON.stringify(message.metadata) : null
    );
  }

  public getMessages(conversationId: string, limit?: number): Message[] {
    let query = `
      SELECT * FROM messages 
      WHERE conversation_id = ?
      ORDER BY timestamp ASC
    `;
    
    if (limit) {
      query += ` LIMIT ${limit}`;
    }
    
    const stmt = this.db.prepare(query);
    const rows = stmt.all(conversationId) as DatabaseSchema['messages'][];
    
    return rows.map(row => ({
      id: row.id,
      type: row.type as Message['type'],
      content: row.content,
      timestamp: new Date(row.timestamp),
      metadata: row.metadata ? JSON.parse(row.metadata) : undefined
    }));
  }

  public deleteMessage(id: string): void {
    const stmt = this.db.prepare(`
      DELETE FROM messages WHERE id = ?
    `);
    
    stmt.run(id);
  }

  // Tool Execution Tracking
  public saveToolExecution(
    messageId: string,
    toolName: string,
    toolArguments: Record<string, any>,
    result: any,
    executionTime: number
  ): string {
    const id = uuidv4();
    const stmt = this.db.prepare(`
      INSERT INTO tool_executions (id, message_id, tool_name, arguments, result, execution_time)
      VALUES (?, ?, ?, ?, ?, ?)
    `);
    
    stmt.run(
      id,
      messageId,
      toolName,
      JSON.stringify(toolArguments),
      JSON.stringify(result),
      executionTime
    );
    
    return id;
  }

  public getToolExecutions(messageId?: string): DatabaseSchema['tool_executions'][] {
    let query = 'SELECT * FROM tool_executions';
    let params: any[] = [];
    
    if (messageId) {
      query += ' WHERE message_id = ?';
      params.push(messageId);
    }
    
    query += ' ORDER BY created_at DESC';
    
    const stmt = this.db.prepare(query);
    return stmt.all(...params) as DatabaseSchema['tool_executions'][];
  }

  // Context Cache Management
  public setCacheValue(key: string, value: any, expiresAt?: Date): void {
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO context_cache (id, key, value, expires_at)
      VALUES (?, ?, ?, ?)
    `);
    
    stmt.run(
      uuidv4(),
      key,
      JSON.stringify(value),
      expiresAt?.toISOString() || null
    );
  }

  public getCacheValue<T = any>(key: string): T | null {
    const stmt = this.db.prepare(`
      SELECT value, expires_at FROM context_cache 
      WHERE key = ? AND (expires_at IS NULL OR expires_at > CURRENT_TIMESTAMP)
    `);
    
    const row = stmt.get(key) as { value: string; expires_at: string | null } | undefined;
    
    if (!row) return null;
    
    try {
      return JSON.parse(row.value) as T;
    } catch {
      return null;
    }
  }

  public deleteCacheValue(key: string): void {
    const stmt = this.db.prepare(`
      DELETE FROM context_cache WHERE key = ?
    `);
    
    stmt.run(key);
  }

  public clearExpiredCache(): void {
    const stmt = this.db.prepare(`
      DELETE FROM context_cache 
      WHERE expires_at IS NOT NULL AND expires_at <= CURRENT_TIMESTAMP
    `);
    
    stmt.run();
  }

  // Settings Management
  public saveSetting(key: string, value: any): void {
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO app_settings (key, value, updated_at)
      VALUES (?, ?, CURRENT_TIMESTAMP)
    `);
    
    stmt.run(key, JSON.stringify(value));
  }

  public getSetting<T = any>(key: string): T | null {
    const stmt = this.db.prepare(`
      SELECT value FROM app_settings WHERE key = ?
    `);
    
    const row = stmt.get(key) as { value: string } | undefined;
    
    if (!row) return null;
    
    try {
      return JSON.parse(row.value) as T;
    } catch {
      return null;
    }
  }

  public getAllSettings(): Record<string, any> {
    const stmt = this.db.prepare(`
      SELECT key, value FROM app_settings
    `);

    const rows = stmt.all() as { key: string; value: string }[];
    const settings: Record<string, any> = {};

    for (const row of rows) {
      try {
        settings[row.key] = JSON.parse(row.value);
      } catch {
        settings[row.key] = row.value;
      }
    }

    return settings;
  }

  // AppConfig specific methods
  public saveAppConfig(config: AppConfig): void {
    this.saveSetting('appConfig', config);
  }

  public getAppConfig(): AppConfig | null {
    return this.getSetting<AppConfig>('appConfig');
  }

  public updateAppConfig(updates: Partial<AppConfig>): void {
    const currentConfig = this.getAppConfig();
    if (currentConfig) {
      const updatedConfig = { ...currentConfig, ...updates };
      this.saveAppConfig(updatedConfig);
    } else {
      // If no config exists, create a default one with updates
      const defaultConfig: AppConfig = {
        llm: {
          provider: 'openai',
          model: 'gpt-4',
          apiKey: '',
          maxTokens: 2000,
          temperature: 0.7,
        },
        executionMode: 'confirm',
        autoSave: true,
        maxHistoryLength: 100,
        theme: 'dark',
        ...updates
      };
      this.saveAppConfig(defaultConfig);
    }
  }

  // Utility Methods
  public close(): void {
    this.db.close();
  }

  public vacuum(): void {
    this.db.exec('VACUUM');
  }

  public getStats(): {
    conversations: number;
    messages: number;
    toolExecutions: number;
    cacheEntries: number;
  } {
    const conversationsCount = this.db.prepare('SELECT COUNT(*) as count FROM conversations').get() as { count: number };
    const messagesCount = this.db.prepare('SELECT COUNT(*) as count FROM messages').get() as { count: number };
    const toolExecutionsCount = this.db.prepare('SELECT COUNT(*) as count FROM tool_executions').get() as { count: number };
    const cacheEntriesCount = this.db.prepare('SELECT COUNT(*) as count FROM context_cache').get() as { count: number };
    
    return {
      conversations: conversationsCount.count,
      messages: messagesCount.count,
      toolExecutions: toolExecutionsCount.count,
      cacheEntries: cacheEntriesCount.count
    };
  }
}
