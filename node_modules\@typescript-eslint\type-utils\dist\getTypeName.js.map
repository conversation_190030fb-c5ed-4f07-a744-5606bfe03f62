{"version": 3, "file": "getTypeName.js", "sourceRoot": "", "sources": ["../src/getTypeName.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAEjC;;;;GAIG;AACH,SAAgB,WAAW,CACzB,WAA2B,EAC3B,IAAa;IAEb,0DAA0D;IAC1D,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;QACjD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,wEAAwE;IACxE,sEAAsE;IACtE,8BAA8B;IAC9B,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;QACpD,sEAAsE;QACtE,uEAAuE;QACvE,WAAW;QACX,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,MAAM,KAAK,GAAG,MAAM,EAAE,eAAe,EAAE,CAAC;QACxC,MAAM,aAAa,GAAG,KAAK,EAAE,CAAC,CAAC,CAAgC,CAAC;QAChE,IACE,EAAE,CAAC,0BAA0B,CAAC,aAAa,CAAC;YAC5C,aAAa,CAAC,UAAU,IAAI,IAAI,EAChC,CAAC;YACD,OAAO,WAAW,CAChB,WAAW,EACX,WAAW,CAAC,mBAAmB,CAAC,aAAa,CAAC,UAAU,CAAC,CAC1D,CAAC;QACJ,CAAC;IACH,CAAC;IAED,qEAAqE;IACrE,gCAAgC;IAChC,2BAA2B;IAC3B,uCAAuC;IACvC,IACE,IAAI,CAAC,OAAO,EAAE;QACd,IAAI,CAAC,KAAK;aACP,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;aAC7C,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,QAAQ,CAAC,EAC7B,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,0EAA0E;IAC1E,uEAAuE;IACvE,IACE,IAAI,CAAC,cAAc,EAAE;QACrB,IAAI,CAAC,KAAK;aACP,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;aAC7C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,QAAQ,CAAC,EAC5B,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,OAAO,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AACxC,CAAC;AAvDD,kCAuDC"}