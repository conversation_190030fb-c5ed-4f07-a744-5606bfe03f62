{"version": 3, "file": "TypeOrValueSpecifier.js", "sourceRoot": "", "sources": ["../src/TypeOrValueSpecifier.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,4EAA4E;AAE5E,gDAAwB;AACxB,sDAAwC;AA0B3B,QAAA,0BAA0B,GAAgB;IACrD,KAAK,EAAE;QACL;YACE,IAAI,EAAE,QAAQ;SACf;QACD;YACE,IAAI,EAAE,QAAQ;YACd,oBAAoB,EAAE,KAAK;YAC3B,UAAU,EAAE;gBACV,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,CAAC,MAAM,CAAC;iBACf;gBACD,IAAI,EAAE;oBACJ,KAAK,EAAE;wBACL;4BACE,IAAI,EAAE,QAAQ;yBACf;wBACD;4BACE,IAAI,EAAE,OAAO;4BACb,QAAQ,EAAE,CAAC;4BACX,WAAW,EAAE,IAAI;4BACjB,KAAK,EAAE;gCACL,IAAI,EAAE,QAAQ;6BACf;yBACF;qBACF;iBACF;gBACD,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;SAC3B;QACD;YACE,IAAI,EAAE,QAAQ;YACd,oBAAoB,EAAE,KAAK;YAC3B,UAAU,EAAE;gBACV,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,CAAC,KAAK,CAAC;iBACd;gBACD,IAAI,EAAE;oBACJ,KAAK,EAAE;wBACL;4BACE,IAAI,EAAE,QAAQ;yBACf;wBACD;4BACE,IAAI,EAAE,OAAO;4BACb,QAAQ,EAAE,CAAC;4BACX,WAAW,EAAE,IAAI;4BACjB,KAAK,EAAE;gCACL,IAAI,EAAE,QAAQ;6BACf;yBACF;qBACF;iBACF;aACF;YACD,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;SAC3B;QACD;YACE,IAAI,EAAE,QAAQ;YACd,oBAAoB,EAAE,KAAK;YAC3B,UAAU,EAAE;gBACV,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,CAAC,SAAS,CAAC;iBAClB;gBACD,IAAI,EAAE;oBACJ,KAAK,EAAE;wBACL;4BACE,IAAI,EAAE,QAAQ;yBACf;wBACD;4BACE,IAAI,EAAE,OAAO;4BACb,QAAQ,EAAE,CAAC;4BACX,WAAW,EAAE,IAAI;4BACjB,KAAK,EAAE;gCACL,IAAI,EAAE,QAAQ;6BACf;yBACF;qBACF;iBACF;gBACD,OAAO,EAAE;oBACP,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC;SACtC;KACF;CACF,CAAC;AAEF,SAAS,oBAAoB,CAAC,IAAa,EAAE,IAAuB;IAClE,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;QAC7B,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;IAChB,CAAC;IACD,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;QACnD,OAAO,IAAI,CAAC;IACd,CAAC;IACD,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;IACpD,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;QACzB,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAE,IAAoB,KAAK,MAAM,CAAC,WAAW,CAAC,CAAC;AACzE,CAAC;AAED,SAAS,kBAAkB,CACzB,YAAgC,EAChC,gBAAiC,EACjC,OAAmB;IAEnB,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;QAC/B,MAAM,GAAG,GAAG,IAAA,wCAAoB,EAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC,CAAC;QAChE,OAAO,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CACzC,IAAA,wCAAoB,EAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAC3D,CAAC;IACJ,CAAC;IACD,MAAM,YAAY,GAAG,IAAA,wCAAoB,EACvC,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,EAAE,YAAY,CAAC,CACvD,CAAC;IACF,OAAO,gBAAgB,CAAC,IAAI,CAC1B,WAAW,CAAC,EAAE,CAAC,IAAA,wCAAoB,EAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,YAAY,CAC3E,CAAC;AACJ,CAAC;AAED,SAAS,qBAAqB,CAC5B,WAAmB,EACnB,gBAAiC,EACjC,OAAmB;IAEnB,sFAAsF;IACtF,MAAM,gBAAgB,GAAG,WAAW,CAAC,OAAO,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;IAEpE,MAAM,OAAO,GAAG,IAAI,MAAM,CAAC,GAAG,WAAW,IAAI,gBAAgB,EAAE,CAAC,CAAC;IACjE,OAAO,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;QACzC,MAAM,aAAa,GAAG,OAAO,CAAC,uBAAuB,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAC5E,OAAO,CACL,aAAa,KAAK,SAAS;YAC3B,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC;YAC3B,OAAO,CAAC,+BAA+B,CAAC,WAAW,CAAC,CACrD,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,iBAAiB,CACxB,gBAAiC,EACjC,OAAmB;IAEnB,4CAA4C;IAE5C,qFAAqF;IACrF,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAClC,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CACzC,OAAO,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAChD,CAAC;AACJ,CAAC;AAED,SAAgB,oBAAoB,CAClC,IAAa,EACb,SAA+B,EAC/B,OAAmB;IAEnB,IAAI,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC;QACvC,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,CAAC;QAClC,OAAO,oBAAoB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IAC/C,CAAC;IACD,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;QAChD,OAAO,KAAK,CAAC;IACf,CAAC;IACD,MAAM,gBAAgB,GACpB,IAAI;SACD,SAAS,EAAE;QACZ,EAAE,eAAe,EAAE;QACnB,EAAE,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC,IAAI,EAAE,CAAC;IAC5D,QAAQ,SAAS,CAAC,IAAI,EAAE,CAAC;QACvB,KAAK,MAAM;YACT,OAAO,kBAAkB,CAAC,SAAS,CAAC,IAAI,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAC;QACvE,KAAK,KAAK;YACR,OAAO,iBAAiB,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;QACtD,KAAK,SAAS;YACZ,OAAO,qBAAqB,CAC1B,SAAS,CAAC,OAAO,EACjB,gBAAgB,EAChB,OAAO,CACR,CAAC;IACN,CAAC;AACH,CAAC;AA/BD,oDA+BC"}