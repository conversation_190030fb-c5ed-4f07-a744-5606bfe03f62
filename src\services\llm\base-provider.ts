import { encoding_for_model, get_encoding } from 'tiktoken';
import type { <PERSON><PERSON><PERSON><PERSON>, LLMConfig, Message, ToolSchema, AgentPlan } from '../../types';

export abstract class BaseLLMProvider implements LLMProvider {
  protected config: LLMConfig;
  protected tokenizer: any;

  constructor(config: LLMConfig) {
    this.config = config;
    this.initializeTokenizer();
  }

  private initializeTokenizer(): void {
    try {
      // Try to get model-specific encoding, fallback to cl100k_base
      if (this.config.model.includes('gpt')) {
        this.tokenizer = encoding_for_model(this.config.model as any);
      } else {
        this.tokenizer = get_encoding('cl100k_base');
      }
    } catch (error) {
      console.warn('Failed to initialize model-specific tokenizer, using fallback:', error);
      this.tokenizer = get_encoding('cl100k_base');
    }
  }

  abstract get name(): string;
  abstract get models(): string[];
  abstract generateResponse(messages: Message[], tools?: ToolSchema[]): Promise<string>;
  abstract generatePlan(prompt: string, context: Message[], tools: ToolSchema[]): Promise<AgentPlan>;
  abstract streamResponse(messages: Message[], tools?: ToolSchema[]): AsyncGenerator<string, void, unknown>;

  public countTokens(text: string): number {
    try {
      return this.tokenizer.encode(text).length;
    } catch (error) {
      console.warn('Token counting failed, using approximation:', error);
      // Rough approximation: 1 token ≈ 4 characters
      return Math.ceil(text.length / 4);
    }
  }

  public getMaxTokens(model: string): number {
    // Default token limits for common models
    const tokenLimits: Record<string, number> = {
      'gpt-4': 8192,
      'gpt-4-32k': 32768,
      'gpt-4-turbo': 128000,
      'gpt-4o': 128000,
      'gpt-3.5-turbo': 4096,
      'gpt-3.5-turbo-16k': 16384,
      'claude-3-opus': 200000,
      'claude-3-sonnet': 200000,
      'claude-3-haiku': 200000,
      'claude-3-5-sonnet': 200000,
      'deepseek-chat': 32768,
      'deepseek-coder': 32768,
    };

    return tokenLimits[model] || 4096;
  }

  protected formatMessagesForAPI(messages: Message[]): any[] {
    return messages
      .filter(msg => msg.type !== 'tool') // Filter out tool messages for now
      .map(msg => ({
        role: msg.type === 'user' ? 'user' : 'assistant',
        content: msg.content
      }));
  }

  protected formatToolsForAPI(tools: ToolSchema[]): any[] {
    return tools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.parameters
      }
    }));
  }

  protected createSystemPrompt(tools: ToolSchema[]): string {
    const basePrompt = `You are Arien Agent, a powerful AI assistant with access to various tools. You can help users with coding, file operations, system commands, and more.

Key capabilities:
- Execute shell commands and manage processes
- Read, write, and manipulate files
- Search through codebases and files
- Perform complex reasoning and planning
- Provide detailed explanations and code examples

When a user asks you to perform a task that requires tools:
1. Analyze the request carefully
2. Create a step-by-step plan
3. Use the appropriate tools to execute the plan
4. Provide clear feedback on the results

Always be helpful, accurate, and safe. If a command could be destructive, explain the risks and ask for confirmation.`;

    if (tools.length === 0) {
      return basePrompt;
    }

    const toolDescriptions = tools.map(tool => 
      `- ${tool.name}: ${tool.description}`
    ).join('\n');

    return `${basePrompt}

Available tools:
${toolDescriptions}

Use these tools when appropriate to help the user accomplish their goals.`;
  }

  protected parseToolCalls(content: string): any[] {
    // This is a simplified parser - in practice, you'd want more robust parsing
    const toolCallRegex = /```json\s*(\{[\s\S]*?\})\s*```/g;
    const toolCalls = [];
    let match;

    while ((match = toolCallRegex.exec(content)) !== null) {
      try {
        const parsed = JSON.parse(match[1]);
        if (parsed.tool_calls && Array.isArray(parsed.tool_calls)) {
          toolCalls.push(...parsed.tool_calls);
        }
      } catch (error) {
        console.warn('Failed to parse tool call:', error);
      }
    }

    return toolCalls;
  }

  protected estimateTokens(messages: Message[], tools: ToolSchema[] = []): number {
    const systemPrompt = this.createSystemPrompt(tools);
    const messagesText = messages.map(m => m.content).join('\n');
    const toolsText = JSON.stringify(tools);
    
    return this.countTokens(systemPrompt + messagesText + toolsText);
  }

  protected truncateContext(messages: Message[], maxTokens: number, tools: ToolSchema[] = []): Message[] {
    const systemTokens = this.countTokens(this.createSystemPrompt(tools));
    const toolsTokens = this.countTokens(JSON.stringify(tools));
    const availableTokens = maxTokens - systemTokens - toolsTokens - 1000; // Reserve 1000 for response

    if (availableTokens <= 0) {
      return [];
    }

    let currentTokens = 0;
    const truncatedMessages: Message[] = [];

    // Start from the most recent messages and work backwards
    for (let i = messages.length - 1; i >= 0; i--) {
      const message = messages[i];
      const messageTokens = this.countTokens(message.content);
      
      if (currentTokens + messageTokens <= availableTokens) {
        truncatedMessages.unshift(message);
        currentTokens += messageTokens;
      } else {
        break;
      }
    }

    return truncatedMessages;
  }

  public updateConfig(config: Partial<LLMConfig>): void {
    this.config = { ...this.config, ...config };
    if (config.model) {
      this.initializeTokenizer();
    }
  }

  public getConfig(): LLMConfig {
    return { ...this.config };
  }

  public getProviderName(): string {
    return this.name;
  }

  protected handleAPIError(error: any): never {
    console.error('LLM API Error:', error);
    
    if (error.response?.status === 401) {
      throw new Error('Invalid API key. Please check your configuration.');
    } else if (error.response?.status === 429) {
      throw new Error('Rate limit exceeded. Please try again later.');
    } else if (error.response?.status === 400) {
      throw new Error('Invalid request. Please check your input.');
    } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      throw new Error('Network error. Please check your internet connection.');
    } else {
      throw new Error(`LLM API error: ${error.message || 'Unknown error'}`);
    }
  }
}
